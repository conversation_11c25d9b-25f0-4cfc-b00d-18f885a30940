//#if UNITY_EDITOR
//using UnityEditor;
//using UnityEngine;

//namespace CEditor
//{
//    [CustomEditor(typeof(StationTemplateManager))]
//    public class StationTemplateManagerEditor : Editor
//    {
//        private SerializedObject obj; //序列化

//        //private SerializedProperty mainCamera, cutsceneRef, dataCollector;

//        void OnEnable()
//        {
//            obj = new SerializedObject(target);
//            //mainCamera = obj.FindProperty("mainCamera");
//            //cutsceneRef = obj.FindProperty("cutsceneRef");
//            //dataCollector = obj.FindProperty("dataCollector");
//        }

//        public override void OnInspectorGUI()
//        {
//            base.OnInspectorGUI();
//            obj.Update();

//            //EditorGUILayout.PropertyField(mainCamera);
//            //EditorGUILayout.PropertyField(cutsceneRef);
//            //EditorGUILayout.PropertyField(dataCollector);

//            //EditorGUILayout.Space(5);

//            StationTemplateManager stationTemplateManager = (StationTemplateManager)target;
//            //string[] stationTemplateOptions = stationTemplateManager.dataCollector.stationTemplateAddressList.ToArray();
//            //string[] modleOptions = stationTemplateManager.dataCollector.modleAddressList.ToArray();

//            // 站位模板
//            //EditorGUILayout.BeginHorizontal();
//            //if (stationTemplateOptions.Length > 0)
//            //{
//            //    stationTemplateManager.stationTemplateAddressIndex = EditorGUILayout.Popup(
//            //        "当前站位模板",
//            //        stationTemplateManager.stationTemplateAddressIndex,
//            //        stationTemplateOptions,
//            //        GUILayout.MinWidth(500)
//            //    );
//            //}
//            //else
//            //{
//            //    EditorGUILayout.HelpBox("Addressable站位模板列表为空，请先填入可选模型", MessageType.Info);
//            //}
//            //GUILayout.FlexibleSpace();
//            if (GUILayout.Button("放置站位模板", GUILayout.MinWidth(130), GUILayout.Height(30)))
//            {
//                //stationTemplateManager.PawnStationTemplate();
//                Debug.Log("点击了按钮.");
//            }
//            //EditorGUILayout.EndHorizontal();

//            //EditorGUILayout.Space(5);

//            // 角色
//            //if (stationTemplateManager.actorSlots.Count > 0)
//            //{
//            //    for (int i = 0; i < stationTemplateManager.actorSlots.Count; i++)
//            //    {
//            //        EditorGUILayout.BeginHorizontal();
//            //        if (modleOptions.Length > 0 && i < stationTemplateManager.modleAddressIndexList.Count)
//            //        {
//            //            stationTemplateManager.modleAddressIndexList[i] = EditorGUILayout.Popup(
//            //                "当前角色模型",
//            //                stationTemplateManager.modleAddressIndexList[i],
//            //                modleOptions,
//            //                GUILayout.MinWidth(500)
//            //            );
//            //        }
//            //        else
//            //        {
//            //            EditorGUILayout.HelpBox("Addressable角色模型列表为空，请先填入可选模型", MessageType.Info);
//            //        }
//            //        GUILayout.FlexibleSpace();
//            //        if (GUILayout.Button("放置角色", GUILayout.MinWidth(130), GUILayout.Height(30)))
//            //        {
//            //            stationTemplateManager.PawnActor(stationTemplateManager.modleAddressIndexList[i], i);
//            //            Debug.Log("点击了按钮.");
//            //        }
//            //        EditorGUILayout.EndHorizontal();
//            //    }
//            //}

//            if (GUILayout.Button("放置角色", GUILayout.MinWidth(130), GUILayout.Height(30)))
//            {
//                //stationTemplateManager.PawnActor();
//                Debug.Log("点击了按钮.");
//            }

//            obj.ApplyModifiedProperties();
//        }
//    }
//}

//#endif