using System.Collections;
using Cinemachine;
using Sirenix.OdinInspector;
using UnityEngine;
using CEditor;
using System.Collections.Generic;
using System.Linq;
using VGame.Framework;
using System;
using UnityEditor;
using UnityEngine.UI;
using UnityEngine.UIElements;
using VGame;
using VGame.EventDef;

namespace Slate.ActionClips
{
    [Serializable]
    public class IconDropdownItem
    {
        public string Name;
        public Sprite Icon;
    }

    [Name("单一镜头配置")]
    [Attachable(typeof(CCameraTransTrack))]
    [Category("C类编辑器")]
    public class CSingleCameraTransClip : CCameraClipParent
    {
        //[LabelText("自动保存运行时更改")]
        //public bool runtimeAutoSave = true;

        [SerializeField, LabelText("镜头组标签"), ValueDropdown("TargetTagConfig")]
        public string TargetTag = "";

        private IEnumerable TargetTagConfig()
        {
            CCameraTransTrack track = parent as CCameraTransTrack;
            if (track != null && track.CamTemplateID != 0)
                return StationTemplateManager.GetTargetSlotsListV3(track.CamTemplateID).Select(x => x.targetSlotGroupTag);
            else if (StationTemplateManager.instance != null && StationTemplateManager.instance.targetSlotCameraGroups != null && StationTemplateManager.instance.targetSlotCameraGroups.Count > 0)
                return StationTemplateManager.instance.GetTargetSlotsList();
            return StationTemplateManager.GetTargetSlotsListV3().Select(x => x.targetSlotGroupTag);
        }
        public string GetTargetTag()
        {
            if (TargetTag != null) return TargetTag;
            return null;
        }

        // [SerializeField, LabelText("镜头ID"), ReadOnly]
        // //[ValueDropdown("CamIDConfig",DoubleClickToConfirm = true)]
        // public int CamID;
        // private IEnumerable CamIDConfig()
        // {
        //     List<cfg.common.CEditorCameraAsset> subList;
        //     if (StationTemplateManager.instance != null && StationTemplateManager.instance.targetSlotCameraGroups != null && StationTemplateManager.instance.targetSlotCameraGroups.Count > 0)
        //     {
        //         CameraGroupOfTargetSlotInfo info = StationTemplateManager.instance.targetSlotCameraGroups.Find(x => x.targetSlotGroupTag == GetTargetTag());
        //         if (info != null)
        //         {
        //             int targetStationTemplateID = info.templateID;
        //             subList = CfgManager.tables.TbCEditorCameraAsset.DataList.Where(x => StationTemplateToolsKit.CameraIsUnderStationTemplate(targetStationTemplateID, x.ID)).ToList();
        //         }
        //         else
        //         {
        //             CCameraTransTrack track = parent as CCameraTransTrack;
        //             int tempID = 0;
        //             if (track != null)
        //                 tempID = track.CamTemplateID;
        //             if (tempID == 0)
        //                 tempID = 10000;
        //             CameraGroupOfTargetSlotInfo targetTagInfo = StationTemplateManager.GetTargetSlotsListV3(tempID).Find(x => x.targetSlotGroupTag == GetTargetTag());
        //             int derTempID = targetTagInfo == null ? 0 : targetTagInfo.templateID;
        //             subList = CfgManager.tables.TbCEditorCameraAsset.DataList.Where(x => StationTemplateToolsKit.CameraIsUnderStationTemplate(derTempID, x.ID)).ToList();
        //         }
        //     }
        //     else
        //     {
        //         CCameraTransTrack track = parent as CCameraTransTrack;
        //         int tempID = 0;
        //         if (track != null)
        //             tempID = track.CamTemplateID;
        //         if (tempID == 0)
        //             tempID = 10000;
        //         CameraGroupOfTargetSlotInfo targetTagInfo = StationTemplateManager.GetTargetSlotsListV3(tempID).Find(x => x.targetSlotGroupTag == GetTargetTag());
        //         int derTempID = targetTagInfo == null ? 0 : targetTagInfo.templateID;
        //         subList = CfgManager.tables.TbCEditorCameraAsset.DataList.Where(x => StationTemplateToolsKit.CameraIsUnderStationTemplate(derTempID, x.ID)).ToList();
        //     }
        //     
        //     return subList.Select(x =>
        //     {
        //         return new ValueDropdownItem($"{x.ID}-{x.Detail}", x.ID);
        //     });
        // }
        
        // [HorizontalGroup("row1")]
        // public string PreviewImagePath;
        //
        // [HideLabel]
        // [PreviewField(200, ObjectFieldAlignment.Right)]
        // [HorizontalGroup("row1",200)]
        // public UnityEngine.Object PreviewImage;
        //
        // [AssetSelector(Paths = "Assets\\Resources\\PreviewImages")]
        // public UnityEngine.Object PreviewImageSelector;
        //
        // [TableList]
        // public List<CCameraInfoItem> CameraInfoList;
        
        [BoxGroup("当前选中镜头", centerLabel: true)]
        [HorizontalGroup("当前选中镜头/Row1", Width = 0.35f)]
        [PreviewField(Height = 120)]
        [LabelText("缩略图预览")]
        [ReadOnly]
        public Texture2D SelectedCameraIcon;
        
        [SerializeField]
        [VerticalGroup("当前选中镜头/Row1/Right")]
        [LabelText("镜头ID"), ReadOnly]
        [DisplayAsString]
        public int CamID;
        
        [SerializeField, ReadOnly]
        [VerticalGroup("当前选中镜头/Row1/Right")]
        [MultiLineProperty(5)]
        [LabelText("镜头描述")]
        [DisplayAsString]
        public string SelectedCameraDetail;
        
        // 缩略图路径
        [SerializeField]
        [HideInInspector]
        public string SelectedCameraIconPath;

#if UNITY_EDITOR
        [VerticalGroup("当前选中镜头/Row1/Right")]
        [PropertySpace(50)]
        [Button("打开图片浏览器", ButtonHeight = 30)]
        [GUIColor(0.4f, 0.8f, 1f)]
        private void OpenBrowser()
        {
            string picsBasePath = System.IO.Path.Combine(System.Environment.CurrentDirectory, "CEditorPreview");
            List<SCameraInfoParams> pendingInfos = new List<SCameraInfoParams>();
            
            List<cfg.story.CEditorCameraAsset> subList;
            if (StationTemplateManager.instance != null && StationTemplateManager.instance.targetSlotCameraGroups != null && StationTemplateManager.instance.targetSlotCameraGroups.Count > 0)
            {
                CameraGroupOfTargetSlotInfo info = StationTemplateManager.instance.targetSlotCameraGroups.Find(x => x.targetSlotGroupTag == GetTargetTag());
                if (info != null)
                {
                    int targetStationTemplateID = info.templateID;
                    subList = CfgManager.tables.TbCEditorCameraAsset.DataList.Where(x => StationTemplateToolsKit.CameraIsUnderStationTemplate(targetStationTemplateID, x.ID)).ToList();
                }
                else
                {
                    CCameraTransTrack track = parent as CCameraTransTrack;
                    int tempID = 0;
                    if (track != null)
                        tempID = track.CamTemplateID;
                    if (tempID == 0)
                        tempID = 10000;
                    CameraGroupOfTargetSlotInfo targetTagInfo = StationTemplateManager.GetTargetSlotsListV3(tempID).Find(x => x.targetSlotGroupTag == GetTargetTag());
                    int derTempID = targetTagInfo == null ? 0 : targetTagInfo.templateID;
                    subList = CfgManager.tables.TbCEditorCameraAsset.DataList.Where(x => StationTemplateToolsKit.CameraIsUnderStationTemplate(derTempID, x.ID)).ToList();
                }
            }
            else
            {
                CCameraTransTrack track = parent as CCameraTransTrack;
                int tempID = 0;
                if (track != null)
                    tempID = track.CamTemplateID;
                if (tempID == 0)
                    tempID = 10000;
                CameraGroupOfTargetSlotInfo targetTagInfo = StationTemplateManager.GetTargetSlotsListV3(tempID).Find(x => x.targetSlotGroupTag == GetTargetTag());
                int derTempID = targetTagInfo == null ? 0 : targetTagInfo.templateID;
                subList = CfgManager.tables.TbCEditorCameraAsset.DataList.Where(x => StationTemplateToolsKit.CameraIsUnderStationTemplate(derTempID, x.ID)).ToList();
            }
            
            for (int i = 0; i < subList.Count; i++)
            {
                SCameraInfoParams info = new SCameraInfoParams();
                info.Id = subList[i].ID;
                info.Detail = subList[i].Detail;
            
                string picName = CfgManager.tables.TbCEditorCameraAsset.GetOrDefault(info.Id).UnityName.Split("_")[0] + "_" +
                          CfgManager.tables.TbCEditorCameraAsset.GetOrDefault(info.Id).LensID.ToString() + ".png";
                string camPicPath = System.IO.Path.Combine(picsBasePath, picName);
                if (System.IO.Directory.Exists(picsBasePath) && System.IO.File.Exists(camPicPath))
                {
                    info.Path = camPicPath;
                }
                
                pendingInfos.Add(info);
            }
            
            // 打开窗口
            CImageBrowserWindow window = CImageBrowserWindow.ShowWithInfoParams(pendingInfos);
            window.OnItemSelected = HandleItemSelected;
        }
        
        private void HandleItemSelected(CCameraInfoItem item)
        {
            CamID = item.Id;
            SelectedCameraIcon = CImageBrowserWindowUtility.CopyTexture(item.thumbnail);
            SelectedCameraDetail = item.Detail;
            SelectedCameraIconPath = item.filePath;
            Debug.Log($"已选择镜头：{item.Id}，预览图片路径：{item.filePath}");
        }
#endif
        
        //[SerializeField, LabelText("镜头挂点"), ValueDropdown("CamPickerConfig")]
        //public string CamPickerName = "";

        //private IEnumerable CamPickerConfig()
        //{
        //    Cutscene cutscene = root as Cutscene;
        //    if (cutscene == null)
        //        return null;
        //    if (cutscene.StoryScene == null)
        //        return null;
        //    var stationTemplatePickers = cutscene.StoryScene.GetComponentsInChildren<StationTemplatePicker>();
        //    List<string> pickerNames;
        //    pickerNames = stationTemplatePickers.Select(x => x.name).ToList();
        //    return pickerNames;
        //}

        [SerializeField, LabelText("过滤微小镜头变换")]
        public bool filterWeakLensChanges = false;

        [SerializeField, LabelText("切镜过渡时长")]
        public float lensCutBlendTime = 0;

        [SerializeField, LabelText("切镜过渡曲线类别")]
        public CinemachineBlendDefinition.Style lensCutBlendType = CinemachineBlendDefinition.Style.Cut;

        [HideIf("@lensCutBlendType != CinemachineBlendDefinition.Style.Custom", 1)]
        [SerializeField, LabelText("切镜自定义过渡曲线")]
        public AnimationCurve lensCutCustomBlendCurve;

        [SerializeField, LabelText("跟随表现类型")]
        public HangingPerformType followPerform = HangingPerformType.StaticReferInitPose;

        [SerializeField, LabelText("注视表现类型")]
        public HangingPerformType lookAtPerform = HangingPerformType.StaticReferInitPose;

        [SerializeField, LabelText("自定义LookAt点位")]
        public bool customLookAtTarget = false;

        [HideIf("@!customLookAtTarget", 1)]
        [SerializeField, LabelText("自定义LookAt点位距离")]
        public float customLookATargetDistance = 1;

        [SerializeField, LabelText("FOV偏移")]
        public float FOVOffset = 0;

        [SerializeField, LabelText("屏幕空间旋转")]
        public float screenRotateOffset = 0;

        [SerializeField, LabelText("屏幕空间水平偏移")]
        public float screenHorizontalOffset = 0;

        [SerializeField, LabelText("屏幕空间垂直偏移")]
        public float screenVerticalOffset = 0;

        [SerializeField, LabelText("环绕旋转")]
        public float circleAngleOffset = 0;

        [SerializeField, LabelText("俯仰偏移")]
        public float pitchOffset = 0;

        [SerializeField, LabelText("距离偏移")]
        public float distanceOffset = 0;

        [SerializeField, LabelText("相机位置平移")]
        public Vector3 cameraPositionOffset = Vector3.zero;

        [SerializeField, Tooltip("过渡路径类型")]
        public CinemachineVirtualCamera.BlendHint blendHint = CinemachineVirtualCameraBase.BlendHint.None;

        [SerializeField, LabelText("与A类镜头混合衔接")]
        public bool useAnimCamBlend = false;

        [SerializeField, LabelText("启用运镜预设")]
        public bool useCamMove = false;

        [HideIf("@!useCamMove", 1)]
        [SerializeField, LabelText("运镜过渡方式")]
        public CinemachineBlendDefinition.Style camMoveBlendStyle = CinemachineBlendDefinition.Style.EaseInOut;

        [HideIf("@!useCamMove", 1)]
        [SerializeField, LabelText("运镜自定义过渡曲线")]
        public AnimationCurve camMoveCustromBlendCurve;

        [HideIf("@!useCamMove", 1)]
        [SerializeField, LabelText("运镜时长")]
        public float camMoveTime = 1;

        [HideIf("@!useCamMove", 1)]
        [SerializeField, LabelText("运镜反序")]
        public bool camMoveReverse = false;

        [HideIf("@!useCamMove", 1)]
        [SerializeField, LabelText("运镜方式")]
        public CamMovementType camMoveType = CamMovementType.ZoomIn;

        [HideIf("@!useCamMove || !(camMoveType == CamMovementType.ZoomIn || camMoveType == CamMovementType.ZoomOut)", 1)]
        [SerializeField, LabelText("运镜推拉距离")]
        public float camMoveZoomDistance = 0;

        [HideIf("@!useCamMove || camMoveType != CamMovementType.Pan", 1)]
        [SerializeField, LabelText("运镜水平摇镜偏移量")]
        public float camMovePanHorizontal = 0;

        [HideIf("@!useCamMove || camMoveType != CamMovementType.Pan", 1)]
        [SerializeField, LabelText("运镜垂直摇镜偏移量")]
        public float camMovePanVertical = 0;

        [HideIf("@!useCamMove || camMoveType != CamMovementType.Pan", 1)]
        [SerializeField, LabelText("运镜滚镜头偏移量")]
        public float camMovePanRoll = 0;

        [HideIf("@!useCamMove || camMoveType != CamMovementType.Track", 1)]
        [SerializeField, LabelText("运镜水平移镜头")]
        public float camMoveTrackHorizontal = 0;

        [HideIf("@!useCamMove || camMoveType != CamMovementType.Track", 1)]
        [SerializeField, LabelText("运镜垂直移镜头")]
        public float camMoveTrackVertical = 0;

        [HideIf("@!useCamMove || camMoveType != CamMovementType.Circle", 1)]
        [SerializeField, LabelText("运镜环绕镜头")]
        public float camMoveCircleAngle = 0;

        [HideIf("@!useCamMove || camMoveType != CamMovementType.Hitchcock", 1)]
        [SerializeField, LabelText("运镜希区柯克镜头")]
        public float camHitchcock = 0;


        [SerializeField, LabelText("启用镜头抖动")]
        public bool useCamShake = false;

        [HideIf("@!useCamShake", 1)]
        [SerializeField, LabelText("镜头抖动预设"), ValueDropdown("camShakeTypeConfig")]
        public NoiseSettings camShakeType = null;

        private IEnumerable camShakeTypeConfig()
        {
            List<NoiseSettings> allNoiseSettings = new List<NoiseSettings>();
#if UNITY_EDITOR
            string[] guids = AssetDatabase.FindAssets("t:NoiseSettings");
            for (int i = 0; i < guids.Length; i++)
            {
                string path = AssetDatabase.GUIDToAssetPath(guids[i]);
                var setting = AssetDatabase.LoadAssetAtPath<NoiseSettings>(path);
                if (setting == null) continue;
                allNoiseSettings.Add(setting);
            }
#endif
            return allNoiseSettings.Select(x =>
            {
                return new ValueDropdownItem($"{x.name}", x);
            });
        }
        private bool showCamShakeType()
        {
            return useCamShake;
        }

        [HideIf("@!useCamShake", 1)]
        [SerializeField, LabelText("镜头抖动强度")]
        public float camShakeAmpli = 0.1f;

        [HideIf("@!useCamShake", 1)]
        [SerializeField, LabelText("镜头抖动频率")]
        public float camShakeFreq = 1f;
        
        public override string info
        {
            get
            {
                var camAsset = CfgManager.tables.TbCEditorCameraAsset.GetOrDefault(CamID);
                if (camAsset != null)
                {
                    return camAsset.Detail;
                }
                else
                {
                    return "单一镜头配置";
                }
            }
        }

        //Called in forward sampling when the clip is entered
        protected override void OnEnter()
        {
            //cameraSwitchSystem = performer.GetComponent<CameraSwitchSystem>();
            if (StationTemplateManager.instance != null)
            {
                CCameraTransTrack track = parent as CCameraTransTrack;
                if (track != null)
                    track.InitStationTemplateOriginalTransform(CamPickerName);

                if (!useCamMove)
                {
                    CameraControllerData cameraControllerData = CCameraTools.MakeCameraControllerData(
                        FOVOffset,
                        screenRotateOffset,
                        screenHorizontalOffset,
                        screenVerticalOffset,
                        circleAngleOffset,
                        pitchOffset,
                        distanceOffset,
                        blendHint,
                        (useCamShake && camShakeType != null) ? camShakeType : null,
                        camShakeAmpli,
                        camShakeFreq,
                        customLookAtTarget,
                        customLookATargetDistance,
                        cameraPositionOffset);
                    CameraSwitchPerformConfig cameraSwitchPerform = CCameraTools.MakeCameraSwitchPerformConfig(
                        lensCutBlendType,
                        lensCutCustomBlendCurve,
                        lensCutBlendTime);
                    CameraPerformConfig cameraPerformConfig = CCameraTools.MakeCameraPerformConfig(
                        cameraControllerData,
                        cameraSwitchPerform,
                        followPerform,
                        lookAtPerform,
                        cameraPositionOffset,
                        cameraPositionOffset,
                        0,
                        false);

                    if (useAnimCamBlend)
                    {
                        StationTemplateManager.instance.SetBlendPrevCutsceneCamCamera(
                            new CameraIDTransParam()
                            {
                                camID = CamID,
                                cameraPerformConfig = cameraPerformConfig
                            },
                            GetTargetTag());
                    }
                    else
                    {
                        StationTemplateManager.instance.SetCameraID(
                            new CameraIDTransParam()
                            {
                                camID = CamID,
                                cameraPerformConfig = cameraPerformConfig
                            },
                            GetTargetTag(),
                            filterWeakLensChanges);
                    }
                }
                else
                {
                    CameraControllerData baseCameraControllerData = CCameraTools.MakeCameraControllerData(
                        FOVOffset,
                        screenRotateOffset,
                        screenHorizontalOffset,
                        screenVerticalOffset,
                        circleAngleOffset,
                        pitchOffset,
                        distanceOffset,
                        blendHint,
                        (useCamShake && camShakeType != null) ? camShakeType : null,
                        camShakeAmpli,
                        camShakeFreq,
                        customLookAtTarget,
                        customLookATargetDistance,
                        cameraPositionOffset);
                    CameraSwitchPerformConfig baseCameraSwitchPerform = CCameraTools.MakeCameraSwitchPerformConfig(
                        camMoveBlendStyle,
                        camMoveCustromBlendCurve,
                        0);
                    CameraPerformConfig baseCameraPerformConfig = CCameraTools.MakeCameraPerformConfig(
                        baseCameraControllerData,
                        baseCameraSwitchPerform,
                        followPerform,
                        lookAtPerform,
                        cameraPositionOffset,
                        cameraPositionOffset,
                        0,
                        false);


                    CameraControllerData toCameraControllerData = CCameraTools.MakeCameraControllerData(
                        FOVOffset,
                        screenRotateOffset,
                        screenHorizontalOffset,
                        screenVerticalOffset,
                        circleAngleOffset,
                        pitchOffset,
                        distanceOffset,
                        blendHint,
                        (useCamShake && camShakeType != null) ? camShakeType : null,
                        camShakeAmpli,
                        camShakeFreq,
                        customLookAtTarget,
                        customLookATargetDistance, 
                        cameraPositionOffset);
                    CameraSwitchPerformConfig toCameraSwitchPerform = CCameraTools.MakeCameraSwitchPerformConfig(
                        camMoveBlendStyle,
                        camMoveCustromBlendCurve,
                        0); 
                    CameraPerformConfig toCameraPerformConfig = CCameraTools.MakeCameraPerformConfig(
                        toCameraControllerData,
                        toCameraSwitchPerform,
                        followPerform,
                        lookAtPerform,
                        cameraPositionOffset,
                        cameraPositionOffset,
                        0,
                        false);

                    switch (camMoveType)
                    {
                        case CamMovementType.ZoomIn:
                            toCameraPerformConfig.followHangingOffset.z -= camMoveZoomDistance;
                            toCameraPerformConfig.lookAtHangingOffset.z -= camMoveZoomDistance;
                            toCameraPerformConfig.cameraControllerData.cameraPositionOffset.z -= camMoveZoomDistance;
                            break;
                        case CamMovementType.ZoomOut:
                            toCameraPerformConfig.followHangingOffset.z += camMoveZoomDistance;
                            toCameraPerformConfig.lookAtHangingOffset.z += camMoveZoomDistance;
                            toCameraPerformConfig.cameraControllerData.cameraPositionOffset.z += camMoveZoomDistance;
                            break;
                        case CamMovementType.Pan:
                            toCameraPerformConfig.cameraControllerData.screenHorizontalOffset += camMovePanHorizontal;
                            toCameraPerformConfig.cameraControllerData.screenVerticalOffset += camMovePanVertical;
                            toCameraPerformConfig.cameraControllerData.screenRotateOffset += camMovePanRoll;
                            break;
                        case CamMovementType.Track:
                            toCameraPerformConfig.followHangingOffset.x += camMoveTrackHorizontal;
                            toCameraPerformConfig.lookAtHangingOffset.x += camMoveTrackHorizontal;
                            toCameraPerformConfig.followHangingOffset.y += camMoveTrackVertical;
                            toCameraPerformConfig.lookAtHangingOffset.y += camMoveTrackVertical;
                            toCameraPerformConfig.cameraControllerData.cameraPositionOffset.x += camMoveTrackHorizontal;
                            toCameraPerformConfig.cameraControllerData.cameraPositionOffset.y += camMoveTrackVertical;
                            break;
                        case CamMovementType.Circle:
                            toCameraPerformConfig.cameraControllerData.circleAngleOffset += camMoveCircleAngle;
                            toCameraPerformConfig.cameraControllerData.blendHint = CinemachineVirtualCameraBase.BlendHint.CylindricalPosition;
                            baseCameraPerformConfig.cameraControllerData.blendHint = CinemachineVirtualCameraBase.BlendHint.CylindricalPosition;
                            break;
                        case CamMovementType.Hitchcock:
                            toCameraPerformConfig.cameraControllerData.FOVOffset += camHitchcock;
                            break;
                        default: break;
                    }

                    CameraGroupIDTransParam camMoveTransGroup = new CameraGroupIDTransParam();
                    camMoveTransGroup.camGroup = new List<CameraIDTransParam>();
                    camMoveTransGroup.camGroupTransInPerform = new CameraSwitchPerformConfig();
                    camMoveTransGroup.camGroupTransInPerform.blendStyle = lensCutBlendType;
                    camMoveTransGroup.camGroupTransInPerform.custromBlendCurve = lensCutCustomBlendCurve;
                    camMoveTransGroup.camGroupTransInPerform.blendTime = lensCutBlendTime;

                    if (camMoveReverse)
                    {
                        baseCameraPerformConfig.cameraSwitchPerform.blendTime = camMoveTime;
                        camMoveTransGroup.camGroup.Add(new CameraIDTransParam
                        {
                            camID = CamID,
                            cameraPerformConfig = toCameraPerformConfig
                        });
                        camMoveTransGroup.camGroup.Add(new CameraIDTransParam
                        {
                            camID = CamID,
                            cameraPerformConfig = baseCameraPerformConfig
                        });
                    }
                    else
                    {
                        toCameraPerformConfig.cameraSwitchPerform.blendTime = camMoveTime;
                        camMoveTransGroup.camGroup.Add(new CameraIDTransParam
                        {
                            camID = CamID,
                            cameraPerformConfig = baseCameraPerformConfig
                        });
                        camMoveTransGroup.camGroup.Add(new CameraIDTransParam
                        {
                            camID = CamID,
                            cameraPerformConfig = toCameraPerformConfig
                        });
                    }

                    StationTemplateManager.instance.SetCameraGroupID(camMoveTransGroup, GetTargetTag(), filterWeakLensChanges);
                }
            }
            
            Cutscene cutscene = root as Cutscene;
            if (TempUI.Instance != null && cutscene != null)
            {
                var curMainCamera = TempUI.Instance.GetMainCamera();
                
                if(curMainCamera != cutscene.camera)
                {
                    // 【【8月PV】【养成系统】点了手机之后镜头降下又抬上去了 【改动成本大】】https://tapd.woa.com/tapd_fe/70144274/bug/detail/1070144274145519689
                    TempUI.Instance.ReloadMainCamera(cutscene.camera);
                    if (curMainCamera != null && curMainCamera.name.Equals(PhoneConst.PHONE_CAMERA_NAME)) 
                    {
                        YIUIComponent.ClientScene.GetComponent<EventDispatcherComponent>().FireEvent(new Evt_ChangeOriginCamera() { camera = cutscene.camera });
                    }
                    
                    //Debug.Break();
                }
            }
        }

        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime)
        {

        }

        //Called in forwards sampling when the clip exits
        protected override void OnExit() { }

        //Called in backwards sampling when the clip is entered.
        protected override void OnReverseEnter() { }

        //Called in backwards sampling when the clip exits.
        protected override void OnReverse() { }
    }
}