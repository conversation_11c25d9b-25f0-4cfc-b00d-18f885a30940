#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Cinemachine;
using Cysharp.Text;
using Sirenix.Utilities;

public class CCameraAnimationTool : EditorWindow
{
    //private string oldAttributeName = "m_FocalLength";
    private string MayaOldAttributeName = "m_FocalLength";
    private string MaxOldAttributeName = "field of view";
    private string newAttributeName = "m_Lens.FieldOfView";

    // 元数据编辑字段
    private string className = "Cinemachine.CinemachineVirtualCamera";
    private string assemblyName = "Cinemachine";
    private string newClassID = "";
    private string newFileID = "";
    private string newGUID = "";
    private System.Type newType;

    private List<AnimationClip> selectedClips = new List<AnimationClip>();
    private Vector2 scrollPosition;

    [MenuItem("Assets/C类动画工具/C类DCC软件镜头Animation属性批修改", true)]
    private static bool ValidateSelection()
    {
        return Selection.objects.Any(obj => obj is AnimationClip);
    }

    [MenuItem("Assets/C类动画工具/C类DCC软件镜头Animation属性批修改")]
    private static void Init()
    {
        var window = GetWindow<CCameraAnimationTool>("C类动画镜头 DCC软件镜头Animation属性批修改");
        window.minSize = new Vector2(450, 350);
        window.selectedClips = Selection.GetFiltered<AnimationClip>(SelectionMode.Assets).ToList();
        window.Show();
    }

    private void OnGUI()
    {
        GUILayout.Label("Animation Attribute & Metadata Editor", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        // 属性名编辑部分
        EditorGUILayout.LabelField("Attribute Name Replacement", EditorStyles.boldLabel);
        MayaOldAttributeName = EditorGUILayout.TextField("Maya Old Attribute Name:", MayaOldAttributeName);
        MaxOldAttributeName = EditorGUILayout.TextField("Max Old Attribute Name:", MaxOldAttributeName);
        newAttributeName = EditorGUILayout.TextField("New Attribute Name:", newAttributeName);
        EditorGUILayout.HelpBox("Attribute name should be in the format: 'propertyName' or 'component.propertyName'", MessageType.Info);

        EditorGUILayout.Space(15);

        //// 元数据编辑部分
        //EditorGUILayout.LabelField("Metadata Replacement", EditorStyles.boldLabel);
        //EditorGUILayout.HelpBox("These fields allow you to fix broken references by updating the class metadata", MessageType.Warning);

        EditorGUILayout.BeginHorizontal();
        className = EditorGUILayout.TextField("Class Name:", className);
        assemblyName = EditorGUILayout.TextField("Assembly:", assemblyName);
        EditorGUILayout.EndHorizontal();

        //if (GUILayout.Button("Auto-detect from Class"))
        //{
        //    AutoDetectClassMetadata();
        //}

        //newClassID = EditorGUILayout.TextField("New Class ID:", newClassID);
        //newFileID = EditorGUILayout.TextField("New File ID:", newFileID);
        //newGUID = EditorGUILayout.TextField("New GUID:", newGUID);

        //EditorGUILayout.Space();
        EditorGUILayout.LabelField(ZString.Format("Selected Clips: {0}", selectedClips.Count));

        EditorGUILayout.EndScrollView();
        EditorGUILayout.Space();

        GUI.enabled = ShouldEnableRenameButton();
        if (GUILayout.Button("Process Animations", GUILayout.Height(40)))
        {
            selectedClips = Selection.GetFiltered<AnimationClip>(SelectionMode.Assets).ToList();
            AutoDetectClassMetadata();
            ProcessAnimations();
        }
        GUI.enabled = true;
    }

    private bool ShouldEnableRenameButton()
    {
        // 至少有一个修改字段被填写
        return !(string.IsNullOrEmpty(MayaOldAttributeName) && string.IsNullOrEmpty(MaxOldAttributeName)) ||
               !string.IsNullOrEmpty(className) ||
               !string.IsNullOrEmpty(newClassID) ||
               !string.IsNullOrEmpty(newFileID) ||
               !string.IsNullOrEmpty(newGUID);
    }

    private void AutoDetectClassMetadata()
    {
        if (string.IsNullOrEmpty(className))
        {
            EditorUtility.DisplayDialog("Error", "Please enter a class name first", "OK");
            return;
        }

        string fullTypeName = string.IsNullOrEmpty(assemblyName) ? className : $"{className}, {assemblyName}";
        newType = System.Type.GetType(fullTypeName);

        if (newType == null)
        {
            EditorUtility.DisplayDialog("Error", ZString.Format("Class '{0}' not found.\nCheck class and assembly names.", fullTypeName), "OK");
            return;
        }

        // 使用反射获取 classID
        var getClassIDMethod = typeof(UnityEditor.AssetDatabase).Assembly
            .GetType("UnityEditor.AnimationUtility")
            .GetMethod("GetClassID", BindingFlags.Static | BindingFlags.NonPublic);

        if (getClassIDMethod != null)
        {
            int classID = (int)getClassIDMethod.Invoke(null, new object[] { newType });
            newClassID = classID.ToString();
        }
        else
        {
            Debug.LogWarning("Failed to get Class ID via reflection");
        }

        //// 对于 MonoBehaviours，GUID 和 FileID 通常为0
        //if (typeof(MonoBehaviour).IsAssignableFrom(newType))
        //{
        //    newFileID = "0";
        //    newGUID = "00000000000000000000000000000000";
        //}

        // 重绘UI显示新值
        Repaint();
    }

    private void ProcessAnimations()
    {
        if (selectedClips.Count == 0) return;

        try
        {
            AssetDatabase.StartAssetEditing();
            int totalChanges = 0;
            int metadataChanges = 0;

            List<string> oldAttrNameList = new List<string>();
            oldAttrNameList.Add(MayaOldAttributeName);
            oldAttrNameList.Add(MaxOldAttributeName);

            foreach (var clip in selectedClips)
            {
                bool clipModified = false;
                string assetPath = AssetDatabase.GetAssetPath(clip);

                // 1. 处理普通曲线
                var bindings = AnimationUtility.GetCurveBindings(clip);
                //for (int i = 0; i < bindings.Length; i++)
                //{
                //    bool shouldProcess = 
                //        !(string.IsNullOrEmpty(MayaOldAttributeName) && string.IsNullOrEmpty(MaxOldAttributeName)) &&
                //        oldAttrNameList.Contains(bindings[i].propertyName);
                //    EditorCurveBinding binding = bindings[i];
                //    if (shouldProcess)
                //    {
                //        AnimationCurve curve = AnimationUtility.GetEditorCurve(clip, binding);
                //        EditorCurveBinding newBinding = RemovePath(binding);
                //        AnimationUtility.SetEditorCurve(clip, binding, curve);
                //    }
                //    else
                //    {
                //        AnimationCurve curve = AnimationUtility.GetEditorCurve(clip, binding);

                //        EditorCurveBinding newBinding = CreateNewBinding(binding);
                //        AnimationUtility.SetEditorCurve(clip, newBinding, curve);
                //    }
                //}
                foreach (var binding in bindings)
                {
                    bool shouldProcess = !(string.IsNullOrEmpty(MayaOldAttributeName) && string.IsNullOrEmpty(MaxOldAttributeName)) &&
                                        oldAttrNameList.Contains(binding.propertyName);
                    //Debug.Log(ZString.Concat("-------------------", binding.propertyName, " ", binding.path, " ", shouldProcess));
                    //if (!shouldProcess)
                    //    continue;
                    // 获取原始曲线
                    AnimationCurve curve = AnimationUtility.GetEditorCurve(clip, binding);
                    ObjectReferenceKeyframe[] objectCurve = AnimationUtility.GetObjectReferenceCurve(clip, binding);
                    // 创建新绑定
                    EditorCurveBinding newBinding;

                    if (shouldProcess)
                        newBinding = CreateNewBinding(binding);
                    else
                        newBinding = RemovePath(binding);

                    // 移除旧曲线
                    AnimationUtility.SetEditorCurve(clip, binding, null);

                    // 添加新曲线
                    AnimationUtility.SetEditorCurve(clip, newBinding, curve);

                    clipModified = true;
                    totalChanges++;
                }

                //// 2. 处理对象引用曲线
                //var objectBindings = AnimationUtility.GetObjectReferenceCurveBindings(clip);
                //foreach (var binding in objectBindings)
                //{

                //    bool shouldProcess = !(string.IsNullOrEmpty(MayaOldAttributeName) && string.IsNullOrEmpty(MaxOldAttributeName)) &&
                //                        oldAttrNameList.Contains(binding.propertyName);

                //    Debug.Log(ZString.Concat("-------------------", binding.propertyName, " ", shouldProcess));
                //    //if (!shouldProcess)
                //    //    continue;

                //    ObjectReferenceKeyframe[] keyframes = AnimationUtility.GetObjectReferenceCurve(clip, binding);

                //    // 创建新绑定
                //    EditorCurveBinding newBinding;

                //    if (shouldProcess)
                //        newBinding = CreateNewBinding(binding);
                //    else
                //        newBinding = RemovePath(binding);
                //    newBinding.path = null;
                //    // 移除旧曲线
                //    AnimationUtility.SetObjectReferenceCurve(clip, binding, null);

                //    // 添加新曲线
                //    AnimationUtility.SetObjectReferenceCurve(clip, newBinding, keyframes);

                //    clipModified = true;
                //    totalChanges++;
                //}

                // 3. 处理元数据
                if (!string.IsNullOrEmpty(newClassID) || !string.IsNullOrEmpty(newFileID) || !string.IsNullOrEmpty(newGUID))
                {
                    metadataChanges += UpdateClipMetadata(clip, assetPath);
                    clipModified = true;
                }

                if (clipModified)
                {
                    EditorUtility.SetDirty(clip);
                }
            }

            AssetDatabase.StopAssetEditing();
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            string resultMessage = ZString.Format("Processed {0} attributes and {1} metadata entries ", totalChanges, metadataChanges);
            resultMessage += $"across {selectedClips.Count} animation clips";

            Debug.Log(resultMessage);
            ShowNotification(new GUIContent(resultMessage));
        }
        catch (System.Exception e)
        {
            AssetDatabase.StopAssetEditing();
            Debug.LogError(ZString.Format("Processing failed: {0}\n{1}", e.Message, e.StackTrace));
            ShowNotification(new GUIContent($"Error: {e.Message}"));
        }
    }

    private EditorCurveBinding CreateNewBinding(EditorCurveBinding original)
    {
        return EditorCurveBinding.FloatCurve("", newType ?? original.type, string.IsNullOrEmpty(newAttributeName) ? original.propertyName : newAttributeName);
    }

    private EditorCurveBinding RemovePath(EditorCurveBinding original)
    {
        return EditorCurveBinding.FloatCurve("", original.type, original.propertyName);
        //return EditorCurveBinding.FloatCurve(original.path, newType ?? original.type, original.propertyName);
    }

    private int UpdateClipMetadata(AnimationClip clip, string assetPath)
    {
        int changes = 0;

        // 使用序列化对象直接修改元数据
        SerializedObject so = new SerializedObject(clip);
        SerializedProperty bindings = so.FindProperty("m_EditorCurves");

        for (int i = 0; i < bindings.arraySize; i++)
        {
            SerializedProperty curveProp = bindings.GetArrayElementAtIndex(i);
            SerializedProperty attributeProp = curveProp.FindPropertyRelative("attribute");

            if (!string.IsNullOrEmpty(newClassID))
            {
                SerializedProperty classIDProp = attributeProp.FindPropertyRelative("m_ClassID");
                if (classIDProp != null)
                {
                    classIDProp.intValue = int.Parse(newClassID);
                    changes++;
                }
            }

            if (!string.IsNullOrEmpty(newFileID))
            {
                SerializedProperty fileIDProp = attributeProp.FindPropertyRelative("m_Script.fileID");
                if (fileIDProp != null)
                {
                    fileIDProp.longValue = long.Parse(newFileID);
                    changes++;
                }
            }

            if (!string.IsNullOrEmpty(newGUID))
            {
                SerializedProperty guidProp = attributeProp.FindPropertyRelative("m_Script.guid");
                if (guidProp != null)
                {
                    guidProp.stringValue = newGUID;
                    changes++;
                }
            }
        }

        so.ApplyModifiedPropertiesWithoutUndo();
        return changes;
    }
}
#endif