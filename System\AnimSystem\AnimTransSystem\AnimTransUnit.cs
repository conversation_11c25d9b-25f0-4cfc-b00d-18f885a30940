using UnityEngine;


namespace CEditor
{
    [ExecuteAlways]
    public struct PerformConfigMeta
    {
        public AnimationClip animationClip;

        public float startCutTime;
        public float endCutTime;
        public PlayingMode playingMode;

        public float playRate;
        public bool mirror;

        public BlendType transABBlendType;
        public float transABFullTime;
        public BlendType transStaticBlendType;
        public float transStaticFullTime;

        public float weight;
    }

    public enum BlendType
    {
        Linear,
        EaseInOut,
        EaseIn,
        EaseOut,
        QuickEaseInSlowEaseOut
    }

    public enum PlayingMode
    {
        EndStop,
        Loop,
        BlendLoop,
        //LoopThenStop
    }

    public enum BlendOutMode
    {
        MaintainEndStop,
        EndPlay
    }

    [ExecuteAlways]
    public class AnimTransUnit
    {
        public AnimationClip animAClip { get; private set; }
        public AnimationClip animBClip { get; private set; }

        public float animABTransWeight { get; private set; }
        public float animAStaticTransWeight { get; private set; }
        public float animBStaticTransWeight { get; private set; }

        public bool inAnimA { get; private set; }
        public bool inAnimB { get; private set; }

        public bool transA2B { get; private set; }

        public float animASustainTime { get; private set; }
        public float animBSustainTime { get; private set; }

        // start end 位置设置
        public float animAStartTime { get; private set; }
        public float animBStartTime { get; private set; }
        public float animAEndTime { get; private set; }
        public float animBEndTime { get; private set; }

        // 混合曲线配置
        public BlendType transABBlendType { get; private set; }
        public BlendType transStaticBlendType { get; private set; }

        // 混合时长配置
        public float transStaticFullTime { get; private set; }
        public float transABFullTime { get; private set; }

        // 播放速度
        public float animAPlayRate { get; private set; }
        public float animBPlayRate { get; private set; }

        public bool AnimAMirror { get; private set; }
        public bool AnimBMirror { get; private set; }

        // playing mode
        public PlayingMode playingMode { get; private set; }

        public float animAStartNormalizedTime { get; private set; }
        public float animBStartNormalizedTime { get; private set; }
        public float animAEndNormalizedTime { get; private set; }
        public float animBEndNormalizedTime { get; private set; }

        public float weightA { get; private set; }
        public float weightB { get; private set; }
        public float weightLerpAB { get; private set; }

        // 外部调用方法
        public void InitTransUnit(PerformConfigMeta initPerformConfig)
        {
            LoadAnimA(initPerformConfig);
            transA2B = false;
            inAnimA = true;
            inAnimB = false;

            animASustainTime = transABFullTime;
            animBSustainTime = 0;

            UpdateGraphBlendWeight();
        }

        public void UpdateTransUnit()
        {
            UpdateSustainTime(false);
            UpdateGraphBlendWeight();
            UpdateAnimState();
            UpdateSustainTime(true);
        }

        public void Trans(PerformConfigMeta performConfig)
        {
            if (!inAnimA)
            {
                if (LoadAnimA(performConfig))
                {
                    inAnimA = true;
                    transA2B = false;
                }
            }
            if (!inAnimB)
            {
                if (LoadAnimB(performConfig))
                {
                    inAnimB = true;
                    transA2B = true;
                }
            }
        }

        public bool CheckExecuteTransValid()
        {
            UpdateGraphBlendWeight();
            UpdateAnimState();
            return !(inAnimA && inAnimB);
        }

        private bool LoadAnimA(PerformConfigMeta performConfig)
        {
            if (performConfig.animationClip != null)
            {
                animAClip = performConfig.animationClip;
                animAPlayRate = performConfig.playRate;
                AnimAMirror = performConfig.mirror;
                weightA = Mathf.Clamp01(performConfig.weight);

                playingMode = performConfig.playingMode;

                transABBlendType = performConfig.transABBlendType;
                transStaticBlendType = performConfig.transStaticBlendType;
                transABFullTime = performConfig.transABFullTime;
                transStaticFullTime = performConfig.transStaticFullTime > 0 ? performConfig.transStaticFullTime : 0.01f;

                if (playingMode == PlayingMode.EndStop || playingMode == PlayingMode.BlendLoop)
                {
                    animAStartTime = Mathf.Clamp(performConfig.startCutTime, 0, performConfig.animationClip.length);
                    animAEndTime = Mathf.Clamp(performConfig.endCutTime, animAStartTime, performConfig.animationClip.length - 0.00001f);

                    animAStartNormalizedTime = Mathf.Clamp01(animAStartTime / animAClip.length);
                    animAEndNormalizedTime = Mathf.Clamp01(animAEndTime / animAClip.length);
                }
                else if (playingMode == PlayingMode.Loop)
                {
                    animAStartTime = 0;
                    animAEndTime = 0;

                    animAStartNormalizedTime = 0;
                    animAEndNormalizedTime = 0;
                }
                //else if (playingMode == PlayingMode.LoopThenStop)
                //{
                //    animAStartTime = performConfig.startCutTime >= 0 ? performConfig.startCutTime : 0f;
                //    animAEndTime = performConfig.endCutTime >= animAStartTime ? performConfig.endCutTime : animAStartTime;

                //    animAStartNormalizedTime = (animAStartTime % performConfig.animationClip.length) / performConfig.animationClip.length;
                //    animAEndNormalizedTime = (animAEndTime % performConfig.animationClip.length) / performConfig.animationClip.length;
                //}
                return true;
            }
            else { return false; }
        }

        private bool LoadAnimB(PerformConfigMeta performConfig)
        {
            if (performConfig.animationClip != null)
            {
                animBClip = performConfig.animationClip;
                animBPlayRate = performConfig.playRate;
                AnimBMirror = performConfig.mirror;
                weightB = Mathf.Clamp01(performConfig.weight);

                playingMode = performConfig.playingMode;

                transABBlendType = performConfig.transABBlendType;
                transStaticBlendType = performConfig.transStaticBlendType;
                transABFullTime = performConfig.transABFullTime;
                transStaticFullTime = performConfig.transStaticFullTime > 0 ? performConfig.transStaticFullTime : 0.01f;

                if (playingMode == PlayingMode.EndStop || playingMode == PlayingMode.BlendLoop)
                {
                    animBStartTime = Mathf.Clamp(performConfig.startCutTime, 0, performConfig.animationClip.length);
                    animBEndTime = Mathf.Clamp(performConfig.endCutTime, animBStartTime, performConfig.animationClip.length - 0.00001f);

                    animBStartNormalizedTime = Mathf.Clamp01(animBStartTime / animBClip.length);
                    animBEndNormalizedTime = Mathf.Clamp01(animBEndTime / animBClip.length);
                }
                else if (playingMode == PlayingMode.Loop)
                {
                    animBStartTime = 0;
                    animBEndTime = 0;

                    animBStartNormalizedTime = 0;
                    animBEndNormalizedTime = 0;
                }
                //else if (playingMode == PlayingMode.LoopThenStop)
                //{
                //    animBStartTime = performConfig.startCutTime >= 0 ? performConfig.startCutTime : 0f;
                //    animBEndTime = performConfig.endCutTime >= animBStartTime ? performConfig.endCutTime : animBStartTime;

                //    animBStartNormalizedTime = (animBStartTime % performConfig.animationClip.length) / performConfig.animationClip.length;
                //    animBEndNormalizedTime = (animBEndTime % performConfig.animationClip.length) / performConfig.animationClip.length;
                //}
                return true;
            }
            else { return false; }
        }

        private void UpdateSustainTime(bool tailCheck)
        {
            if (!tailCheck)
            {
                if (inAnimA)
                    animASustainTime += Time.deltaTime;
                if (inAnimB)
                    animBSustainTime += Time.deltaTime;
            }
            if (!inAnimA)
                animASustainTime = 0;
            if (!inAnimB)
                animBSustainTime = 0;
        }

        private void UpdateGraphBlendWeight()
        {
            if (transStaticFullTime > 0 && !(inAnimA && !(animAPlayRate > 0)) && !(inAnimB && !(animBPlayRate > 0)))
            {
                animAStaticTransWeight = Mathf.Clamp(((animASustainTime * animAPlayRate) + (transStaticFullTime / animAPlayRate)) - (animAEndTime - animAStartTime), 0, (transStaticFullTime / animAPlayRate)) / (transStaticFullTime / animAPlayRate);
                animBStaticTransWeight = Mathf.Clamp(((animBSustainTime * animBPlayRate) + (transStaticFullTime / animBPlayRate)) - (animBEndTime - animBStartTime), 0, (transStaticFullTime / animBPlayRate)) / (transStaticFullTime / animBPlayRate);
                animAStaticTransWeight = AnimTransToolTik.GetBlendTypeWeightFromTime(transStaticBlendType, animAStaticTransWeight);
                animBStaticTransWeight = AnimTransToolTik.GetBlendTypeWeightFromTime(transStaticBlendType, animBStaticTransWeight);

            }
            else if (transStaticFullTime == 0)
            {
                animAStaticTransWeight = 1;
                animBStaticTransWeight = 1;
            }
            else
            {
                animAStaticTransWeight = 0;
                animBStaticTransWeight = 0;
            }

            if (transA2B)
            {
                if (transABFullTime > 0)
                {
                    animABTransWeight = Mathf.Clamp01(animBSustainTime / transABFullTime);
                    animABTransWeight = AnimTransToolTik.GetBlendTypeWeightFromTime(transABBlendType, animABTransWeight);
                }
                else
                {
                    animABTransWeight = 1;
                }
            }
            else
            {
                if (transABFullTime > 0)
                {
                    animABTransWeight = Mathf.Clamp01(animASustainTime / transABFullTime);
                    animABTransWeight = 1 - AnimTransToolTik.GetBlendTypeWeightFromTime(transABBlendType, animABTransWeight);
                }
                else
                {
                    animABTransWeight = 0;
                }
            }

            weightLerpAB = Mathf.Clamp01(Mathf.Lerp(weightA, weightB, animABTransWeight));
        }

        private void UpdateAnimState()
        {
            if (animABTransWeight == 0f)
            {
                inAnimB = false;
            }
            if (animABTransWeight == 1f)
            {
                inAnimA = false;
            }
        }
    }
}