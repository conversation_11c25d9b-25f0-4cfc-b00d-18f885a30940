﻿#if UNITY_EDITOR
using Sirenix.OdinInspector.Editor;
using System.IO;
using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Sirenix.OdinInspector;
using Cysharp.Text;

public class CImageBrowserWindow : OdinEditorWindow
{
    [HideInInspector]
    public System.Action<CCameraInfoItem> OnItemSelected;

    [TableList(AlwaysExpanded = true)]
    [ShowInInspector]
    private List<CCameraInfoItem> _images = new List<CCameraInfoItem>();

    private Dictionary<string, Texture2D> _thumbnailCache = new Dictionary<string, Texture2D>();
    private List<SCameraInfoParams> _pendingInfos = new List<SCameraInfoParams>();
    private bool _isLoading;
    private int _currentIndex;
    private const int ITEMS_PER_FRAME = 5;

    public static CImageBrowserWindow ShowWithInfoParams(List<SCameraInfoParams> infos)
    {
        var window = GetWindow<CImageBrowserWindow>();
        window.titleContent = new GUIContent("镜头图片预览");
        window.InitializeWithPaths(infos);
        window.Show();
        return window;
    }
    
    [MenuItem("Tools/图片分帧加载预览")]
    private static CImageBrowserWindow OpenWindow()
    {
        var window = GetWindow<CImageBrowserWindow>();
        window.titleContent = new GUIContent("镜头图片预览");
        window.Show();
        return window;
    }

    protected override void OnDisable()
    {
        StopLoading();
        ClearCache();
        base.OnDisable();
    }
    
    protected override void OnDestroy()
    {
        // 仅清理窗口生成的临时纹理
        foreach (var tex in _thumbnailCache.Values)
        {
            DestroyImmediate(tex);
        }
        base.OnDestroy();
    }

    public void InitializeWithPaths(List<SCameraInfoParams> infos)
    {
        _pendingInfos = infos
            //.Where(p => CImageBrowserWindowUtility.IsImageFile(p.Path))
            .Select(p => new SCameraInfoParams 
            {
                Id = p.Id,
                Path = File.Exists(p.Path) ? p.Path.Replace('\\', '/') : string.Empty,
                Detail = p.Detail
            })
            .ToList();
        
        StartLoading();
    } 
    
    // 修改后的StartLoading方法
    private void StartLoading()
    {
        StopLoading();

        _isLoading = true;
        _currentIndex = 0;
        EditorApplication.update += OnEditorUpdate;
    }

    private void StopLoading()
    {
        _isLoading = false;
        EditorApplication.update -= OnEditorUpdate;
        EditorUtility.ClearProgressBar();
    }

    private void OnEditorUpdate()
    {
        if (!_isLoading || _currentIndex >= _pendingInfos.Count)
        {
            StopLoading();
            return;
        }

        int processed = 0;
        while (processed < ITEMS_PER_FRAME && _currentIndex < _pendingInfos.Count)
        {
            SCameraInfoParams info = _pendingInfos[_currentIndex];
            ProcessImage(info);
            _currentIndex++;
            processed++;
        }

        UpdateProgress();
        Repaint();
    }

    private void ProcessImage(SCameraInfoParams inInfo)
    {
        // id存在说明加载过
        if (_images.Exists(x => x.Id == inInfo.Id))
        {
            return;
        }

        var tex = CImageBrowserWindowUtility.LoadTexture(inInfo.Path);
        if (tex == null)
        {
            Debug.LogError(ZString.Format("{0} 不存在镜头图片", inInfo.Id));
            //return;
        }

        var info = new CCameraInfoItem
        {
            Id = inInfo.Id,
            thumbnail = TryOrGetThumbnail(inInfo.Path, tex),
            filePath = inInfo.Path,
            Detail = inInfo.Detail
        };

        _images.Add(info);
    }

    private void UpdateProgress()
    {
        float progress = (float)_currentIndex / _pendingInfos.Count;
        EditorUtility.DisplayProgressBar("加载进度",
            ZString.Format("正在加载 {0}/{1}", _currentIndex, _pendingInfos.Count),
            progress);
    }

    private Texture2D TryOrGetThumbnail(string path, Texture2D original)
    {
        if (_thumbnailCache.TryGetValue(path, out var cached))
        {
            return cached;
        }

        Texture2D thumbnail = CImageBrowserWindowUtility.GenerateThumbnail(original);
        _thumbnailCache[path] = thumbnail;
        return thumbnail;
    }

    private void ClearCache()
    {
        foreach (var tex in _thumbnailCache.Values)
        {
            DestroyImmediate(tex);
        }
        _thumbnailCache.Clear();
    }
}
#endif
