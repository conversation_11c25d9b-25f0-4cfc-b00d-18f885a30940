//using CAnimTrans;
//using UnityEngine;

//namespace Slate.ActionClips
//{
//    [Name("面部眉毛动作配置")]
//    [Attachable(typeof(CAnimEyebrowTransTrack))]
//    [Category("C类编辑器")]
//    public class CAnimEyebrowTransClip : ActorActionClip
//    {
//        //[LabelText("播放速度")]
//        //[SerializeField]
//        //public float PlaySpeed = 1;

//        //[LabelText("循环播放")]
//        //[SerializeField]
//        //public bool Loop = false;

//        //private float _usedBlendAnimTime;

//        //[HideInInspector]
//        //[SerializeField] private float _length = 1 / 30f;

//        //[HideInInspector]
//        //public bool IsCrossing = false;
//        public AnimationClip eyebrowClip;
//        public float eyebrowWeight = 1;
//        public PlayingMode eyebrowPlayingMode = PlayingMode.EndStop;
//        public float eyebrowStartTime = 0;
//        public float eyebrowEndTime = 3;
//        public BlendType eyebrowBlendType = BlendType.EaseInOut;
//        public float eyebrowBlendInDuration = 1;

//        public BlendType eyebrowStaticBlendType = BlendType.EaseInOut;
//        public float eyebrowStaticBlendInDuration = 0.3f;

//        private GameObject performer;

//        public override string info
//        {
//            get
//            {
//                if (eyebrowClip != null)
//                {
//                    return eyebrowClip.name;
//                }
//                else
//                {
//                    return "面部眉毛动作配置";
//                }
//            }
//        }

//        //Called in forward sampling when the clip is entered
//        protected override void OnEnter()
//        {
//            performer = GetActor();
//            AnimTransMaster master = performer.GetComponent<AnimTransMaster>();

//            if (master != null && performer != null)
//            {
//                master.currentFaceEyebrowAnimClip = eyebrowClip;
//                master.currentFaceEyebrowWeight = eyebrowWeight;
//                master.eyebrowPlayingMode = eyebrowPlayingMode;
//                master.eyebrowStartTime = eyebrowStartTime;
//                master.eyebrowEndTime = eyebrowEndTime;
//                master.faceEyebrowBlendType = eyebrowBlendType;
//                master.faceEyebrowBlendInDuration = eyebrowBlendInDuration;
//                master.faceEyebrowStaticBlendType = eyebrowStaticBlendType;
//                master.faceEyebrowStaticBlendInDuration = eyebrowStaticBlendInDuration;

//                master.EyebrowTransAnim();
//            }
//        }

//        //Called per frame while the clip is updating. Time is the local time within the clip.
//        //So a time of 0 means the start of the clip.
//        protected override void OnUpdate(float time, float previousTime) { }

//        //Called in forwards sampling when the clip exits
//        protected override void OnExit() { }

//        //Called in backwards sampling when the clip is entered.
//        protected override void OnReverseEnter() { }

//        //Called in backwards sampling when the clip exits.
//        protected override void OnReverse() { }
//    }
//}