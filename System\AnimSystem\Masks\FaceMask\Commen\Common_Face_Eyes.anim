%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Common_Face_Eyes
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Relax
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Gaodi
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Gaodi2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Kunrao
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Kunrao2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Angry
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Angry2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Angry3
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.A1
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Ao2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Aa3
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.A4
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.A5
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.A6
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.E1
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.E2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.U1
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.U2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.U3
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.I1
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.I2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.I3
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.O
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.UO
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Smile1
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Smile3
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: 'blendShape.blendShape.Mouth_SmileEvil

'
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Smile2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Smile4
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Talk1
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Talk2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Triangle
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_W
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_M
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_UnHappy
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_WOpen
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_MOpen
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Angry
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_AngryOpen
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_BrowOuterUpRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_BrowOuterUpLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_BrowDownRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_BrowDownLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_BrowInnerUp
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_JawForward
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_JawRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_JawLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_JawOpen
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthFunnel
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthPurseU
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthSmileRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthSmileLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthFrownRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthFrownLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthDimpleRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthDimpleLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthStretchRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthStretchLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthRollUpper
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthRollLower
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthShrugUpper
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthShrugLower
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthPressRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthPressLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthLowerDownRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthLowerDownLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthUpperUpRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthUpperUpLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_CheekPuff
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Tooth_Scale_0
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: -2.845572
        outSlope: -2.845572
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Down
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 2.845572
        outSlope: 2.845572
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Up
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 1258344335
      attribute: 1216397328
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2500159185
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2262127679
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3023483252
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 1292451225
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3535786822
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2243487099
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 1814667401
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2396892201
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 4191853759
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2092699197
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 1727610568
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2407343056
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 215415474
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2077346340
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3805846430
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 416336697
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2178521731
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 1376967016
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3407579346
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3155998788
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3026551861
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 762107279
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 1517020441
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2285098974
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3853485395
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 4255472285
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 329989041
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 866389851
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 1688996647
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2379197970
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 364066148
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2361025758
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 652994071
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2585889092
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 1732472894
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 1838738068
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 1653976134
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 1220879205
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 176337563
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 1102672790
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 5151210
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 4113903252
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 4152216244
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2754686208
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 4204697243
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3056737835
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2193880475
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 1890426562
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2931489294
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2418541376
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 345223882
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2976547106
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3670327983
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3404035934
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3826847845
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 4287095701
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 858071373
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2592749007
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2385165589
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 517838532
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2115515003
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3208067982
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3757219633
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 982691695
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2622645749
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 4107913379
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 3067164290
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 599927433
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 2699162211
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 618710235
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 4023105895
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 812148435
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    - serializedVersion: 2
      path: 1258344335
      attribute: 4099393895
      script: {fileID: 0}
      typeID: 137
      customType: 20
      isPPtrCurve: 0
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Relax
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Gaodi
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Gaodi2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Kunrao
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Kunrao2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Angry
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Angry2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Angry3
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.A1
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Ao2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Aa3
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.A4
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.A5
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.A6
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.E1
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.E2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.U1
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.U2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.U3
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.I1
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.I2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.I3
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.O
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.UO
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Smile1
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Smile3
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: 'blendShape.blendShape.Mouth_SmileEvil

'
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Smile2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Smile4
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Talk1
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Talk2
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Triangle
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_W
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_M
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_UnHappy
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_WOpen
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_MOpen
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_Angry
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Mouth_AngryOpen
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_BrowOuterUpRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_BrowOuterUpLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_BrowDownRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_BrowDownLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_BrowInnerUp
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_JawForward
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_JawRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_JawLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_JawOpen
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthFunnel
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthPurseU
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthSmileRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthSmileLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthFrownRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthFrownLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthDimpleRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthDimpleLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthStretchRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthStretchLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthRollUpper
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthRollLower
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthShrugUpper
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthShrugLower
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthPressRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthPressLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthLowerDownRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthLowerDownLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthUpperUpRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthUpperUpLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthRight
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_MouthLeft
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.ARkit_CheekPuff
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Tooth_Scale_0
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: -2.845572
        outSlope: -2.845572
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Down
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  - serializedVersion: 2
    curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 2.845572
        outSlope: 2.845572
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: blendShape.blendShape.Brow_Up
    path: Mesh_Lod0/Mesh_Face
    classID: 137
    script: {fileID: 0}
    flags: 0
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
