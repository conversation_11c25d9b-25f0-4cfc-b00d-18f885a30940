using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using UnityEditor;
using UnityEngine;

namespace CEditor
{
    [ExecuteAlways]
    public class GlobalAssetsManager : MonoBehaviour
    {
        public List<BodyAssetDetail> bodyAssetsList { get; set; } = new List<BodyAssetDetail>();
        public List<EmoteAssetDetail> bodyEmoteList { get; set; } = new List<EmoteAssetDetail>();
        public List<PersonalityAssetDetail> personalityList { get; set; } = new List<PersonalityAssetDetail>();
        void Start()
        {

        }

        // Update is called once per frame
        void Update()
        {

        }

        public void LoadBodyAssetsData()
        {

        }

        private void SaveBodyAssetsData()
        {

        }
        public void LoadBodyEmoteData()
        {

        }

        private void SaveBodyEmotionData()
        {

        }


        /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        public void LoadPersonalityData()
        {

        }

        private void SavePersonalityData()
        {

        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

