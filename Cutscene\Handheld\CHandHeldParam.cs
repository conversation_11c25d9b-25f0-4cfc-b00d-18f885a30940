using System.Collections;
using Sirenix.OdinInspector;
using UnityEngine;
using VGame.Framework;
using VGame;
using System.Linq;
using cfg.story;
using System.Threading.Tasks;

namespace Slate.ActionClips
{
    [System.Serializable]
    public class CHandHeldParams
    {
        [SerializeField, LabelText("手持配置"), ValueDropdown("HandheldConfig"), OnValueChanged("OnHandheldConfigChangeAsync")]
        public int handheldKey;

        private IEnumerable HandheldConfig()
        {
            return CfgManager.tables.TbCEditorHandheldTable.DataList.Select(x =>
            {
                return new ValueDropdownItem($"{x.ID}-{x.Detail}", x.ID);
            });
        }

        private CEditorHandheldTable handheldConfig = null;

        public async Task OnHandheldConfigChangeAsync()
        {
            handheldConfig = CfgManager.tables.TbCEditorHandheldTable.GetOrDefault(handheldKey);
            if (handheldConfig == null) return;

            if (!string.IsNullOrEmpty(handheldConfig.OverwriteAnimName))
                overwriteAnim = await DialogueUtil.Load<AnimationClip>(handheldConfig.OverwriteAnimName);
            else
                overwriteAnim = null;

            if (overwriteAnim != null)
                overwriteHandAnim = true;
            else
                overwriteHandAnim = false;

            if (!string.IsNullOrEmpty(handheldConfig.GameObjectName))
                holdObject = await DialogueUtil.LoadPrefab(handheldConfig.GameObjectName);
            else
                holdObject = null;

            if (holdObject != null)
            {
                isHoldingObject = true;
                refBoneName = handheldConfig.HangingBoneName;
                positionOffset = new Vector3(handheldConfig.PositionOffsetX, handheldConfig.PositionOffsetY, handheldConfig.PositionOffsetZ);
                rotateOffset = new Vector3(handheldConfig.RotateOffsetX, handheldConfig.RotateOffsetY, handheldConfig.RotateOffsetZ);
                localscale = new Vector3(handheldConfig.ScaleX, handheldConfig.ScaleY, handheldConfig.ScaleZ);
                if (string.IsNullOrEmpty(handheldConfig.GameObjectAnimName) || handheldConfig.GameObjectAnimName.ToLower() == "null")
                {
                    useHoldObjectAnim = false;
                    holdObjectAnimation = null;
                }
                else
                {
                    var holdObjectAnim = await DialogueUtil.Load<AnimationClip>(handheldConfig.GameObjectAnimName);
                    if (holdObjectAnim == null)
                    {
                        useHoldObjectAnim = false;
                        holdObjectAnimation = null;
                    }
                    else
                    {
                        useHoldObjectAnim = true;
                        holdObjectAnimation = holdObjectAnim;
                    }
                }
            }
            else
            {
                isHoldingObject = false;
                refBoneName = "";
                positionOffset = Vector3.zero;
                rotateOffset = Vector3.zero;
                localscale = Vector3.zero;
                useHoldObjectAnim = false;
                holdObjectAnimation = null;
            }
        }

        [SerializeField, LabelText("启用手部覆写")]
        public bool overwriteHandAnim = true;

        [HideIf("@!overwriteHandAnim", 1)]
        [SerializeField, LabelText("手部覆写动画")]
        public AnimationClip overwriteAnim = null;

        [SerializeField, LabelText("启用手持物")]
        public bool isHoldingObject = true;

        [HideIf("@!isHoldingObject", 1)]
        [SerializeField, LabelText("手持物GameObject")]
        public GameObject holdObject = null;

        [SerializeField, LabelText("启用手持物动画")]
        public bool useHoldObjectAnim = false;

        [HideIf("@!useHoldObjectAnim", 1)]
        [SerializeField, LabelText("手持物动画")]
        public AnimationClip holdObjectAnimation;

        [HideIf("@!isHoldingObject", 1)]
        [SerializeField, LabelText("手部挂点骨骼名")]
        public string refBoneName = "";

        [HideIf("@!isHoldingObject", 1)]
        [SerializeField, LabelText("局部位置偏移")]
        public Vector3 positionOffset = Vector3.zero;

        [HideIf("@!isHoldingObject", 1)]
        [SerializeField, LabelText("局部旋转偏移")]
        public Vector3 rotateOffset = Vector3.zero;

        [HideIf("@!isHoldingObject", 1)]
        [SerializeField, LabelText("局部缩放量")]
        public Vector3 localscale = Vector3.one;
    }
}
