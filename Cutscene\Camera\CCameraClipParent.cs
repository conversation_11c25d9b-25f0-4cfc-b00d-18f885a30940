using Slate;
using Sirenix.OdinInspector;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CCameraClipParent : DirectorActionClip
{
    [SerializeField, LabelText("镜头挂点"), ValueDropdown("CamPickerConfig")]
    public string CamPickerName = "";
    private IEnumerable CamPickerConfig()
    {
        Cutscene cutscene = root as Cutscene;
        if (cutscene == null)
            return null;
        if (cutscene.StoryScene == null)
            return null;
        var stationTemplatePickers = cutscene.StoryScene.GetComponentsInChildren<StationTemplatePicker>();
        List<string> pickerNames;
        pickerNames = stationTemplatePickers.Select(x => x.name).ToList();
        return pickerNames;
    }

    // Start is called before the first frame update
    void Start()
    {
        
    }

    // Update is called once per frame
    void Update()
    {
        
    }
}
