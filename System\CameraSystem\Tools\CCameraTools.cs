using Cinemachine;
using System;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Text;
using static Cinemachine.CinemachineVirtualCameraBase;

namespace CEditor
{
    [ExecuteAlways]
    public static class CCameraTools
    {
        public static string GetUniqueGameObjectName(string baseName, Transform parentTransform)
        {
            int defaultNum = 0;
            while (parentTransform.Find(ZString.Concat(baseName, defaultNum.ToString())) != null)
            {
                defaultNum++;
            }
            return ZString.Concat(baseName, defaultNum.ToString());
        }

        // 挂点相关工具方法
        public static string GetHangingStructuredName(HangingBindContext context, string customName, string offsetString, bool isDynamic)
        {
            string baseString = ZString.Concat(context.hangingTargetType.ToString(), "_", context.targetIndex.ToString(), "_", customName, "_", offsetString);
            if (isDynamic)
                return ZString.Concat(baseString, "@dynamic");
            else
                return ZString.Concat(baseString, "@static");
        }

        public static string GetHangingStructuredName(string baseHangingName, bool isDynamic)
        {
            string[] strings = baseHangingName.Split("@");
            if (strings.Length > 1)
            {
                if (isDynamic)
                    return ZString.Concat(strings[0], "@dynamic");
                else
                    return ZString.Concat(strings[0], "@static");
            }
            else
            {
                if (isDynamic)
                    return ZString.Concat(baseHangingName, "@dynamic");
                else
                    return ZString.Concat(baseHangingName, "@static");
            }
        }

        public static bool AnalyzeWhetherIsDynamic(string hangingNmae)
        {
            string[] strings = hangingNmae.Split("@");
            if (strings.Length > 1)
            {
                if (strings[1] == "dynamic")
                    return true;
                else if (strings[1] == "static")
                    return false;
            }
            return false;
        }

        public static bool HasRegularFollowHanging(CinemachineVirtualCamera vc)
        {
            if (vc == null) return false;
            if (vc.Follow ==  null) return false;
            if (vc.Follow == vc.gameObject.transform.parent) return false;
            return true;
        }

        public static bool HasRegularLookAtHanging(CinemachineVirtualCamera vc)
        {
            if (vc == null) return false;
            if (vc.LookAt == null) return false;
            if (vc.LookAt == vc.gameObject.transform.parent) return false;
            return true;
        }

        public static HangingBindContext GetHangingBindContextByHangingName(string hangingName)
        {
            string[] hangingNameTags = hangingName.Split("_");
            HangingTargetType hangingType = (HangingTargetType)Enum.Parse(typeof(HangingTargetType), hangingNameTags[0], true);
            int hangingIndex = Convert.ToInt32(hangingNameTags[1]);
            return new HangingBindContext { hangingTargetType = hangingType, targetIndex = hangingIndex };
        }

        public static bool HangingNameEqualsTargetHangingContext(string hangingName, HangingBindContext hangingBindContext)
        {
            HangingBindContext contextFromString = GetHangingBindContextByHangingName(hangingName);
            if (contextFromString.hangingTargetType == hangingBindContext.hangingTargetType && contextFromString.targetIndex == hangingBindContext.targetIndex)
                return true;
            return false;
        }

        // TargetGroup解析
        public static bool IsTargetGroup(string name)
        {
            if (string.IsNullOrEmpty(name))
                return false;
            if(name.Substring(0, 2) == "Gp")
                return true;
            return false;
        }

        public static List<TargetGroupHangingInfoStringOffsetByName> AnalysisTargetGroupName(string name)
        {
            if (string.IsNullOrEmpty(name))
                return null;
            if (!IsTargetGroup(name))
                return null;
            List<TargetGroupHangingInfoStringOffsetByName> hangingList = new List<TargetGroupHangingInfoStringOffsetByName>();
            string[] hangyings = name.Substring(2, name.Length - 2).Split(")");
            foreach (string s in hangyings)
            {
                if (string.IsNullOrEmpty(s))
                    continue;
                hangingList.Add(new TargetGroupHangingInfoStringOffsetByName()
                {
                    baseObjName = s.Split("#")[0],
                    slotIndex = StationTemplateToolsKit.GetSlotIndexByStringName(s.Split("#")[1].Split("(")[0]),
                    offset = s.Split("(")[1],
                    type = HangingType.ActorBaseHanging
                });
            }
            return hangingList;
        }




        // 相机相关工具方法
        public static string TransToCloneCameraName(string baseName)
        {
            return ZString.Concat(baseName, "<Clone>");
        }

        public static bool IsCloneCamera(string cameraName)
        {
            if (!string.IsNullOrEmpty(cameraName))
                return cameraName.Split("<Clone>").Length == 2;
            return false;
        }

        public static bool IsNoFollowOrLookAtCam(CameraNameInfo cameraNameInfo)
        {
            if (cameraNameInfo != null)
            {
                if (string.IsNullOrEmpty(cameraNameInfo.baseCamInfo.followHanging.baseObjName) || cameraNameInfo.baseCamInfo.followHanging.baseObjName.ToLower() == "null" || string.IsNullOrEmpty(cameraNameInfo.baseCamInfo.lookAtHanging.baseObjName) || cameraNameInfo.baseCamInfo.lookAtHanging.baseObjName.ToLower() == "null")
                {
                    return true;
                }
            }
            return false;
        }

        public static string GetOriCamNameFromClone(string cameraName)
        {
            if (!string.IsNullOrEmpty(cameraName))
                return cameraName.Split("<Clone>")[0];
            return null;
        }

        public static bool IsCamera(string cameraName)
        {
            return cameraName.Substring(0, 3) == "Cam";
        }

        public static CameraControllerData MakeCameraControllerData(float FOVOffset, float screenRotateOffset, float screenHorizontalOffset, float screenVerticalOffset, float circleAngleOffset, float pitchOffset, float distanceOffset, CinemachineVirtualCamera.BlendHint blendHint, NoiseSettings camShakeType, float camShakeAmpli, float camShakeFreq, bool customLookAtTarget, float customLookATargetDistance, Vector3 cameraPositionOffset)
        {
            CameraControllerData cameraControllerData = new CameraControllerData();
            //cameraControllerData.runtimeAutoSave = runtimeAutoSave;
            cameraControllerData.FOVOffset = FOVOffset;
            cameraControllerData.screenRotateOffset = screenRotateOffset;
            cameraControllerData.screenHorizontalOffset = screenHorizontalOffset;
            cameraControllerData.screenVerticalOffset = screenVerticalOffset;
            cameraControllerData.circleAngleOffset = circleAngleOffset;
            cameraControllerData.pitchOffset = pitchOffset;
            cameraControllerData.distanceOffset = distanceOffset;
            cameraControllerData.blendHint = blendHint;

            cameraControllerData.cameraPositionOffset = cameraPositionOffset;

            cameraControllerData.customLookAtTarget = customLookAtTarget;
            cameraControllerData.customLookATargetDistance = customLookATargetDistance;

            cameraControllerData.camShakeType = camShakeType;
            cameraControllerData.camShakeAmpli = camShakeAmpli;
            cameraControllerData.camShakeFreq = camShakeFreq;

            return cameraControllerData;
        }

        public static CameraSwitchPerformConfig MakeCameraSwitchPerformConfig(CinemachineBlendDefinition.Style blendStyle, AnimationCurve custromBlendCurve, float blendTime)
        {
            CameraSwitchPerformConfig cameraSwitchPerformConfig = new CameraSwitchPerformConfig();
            cameraSwitchPerformConfig.blendStyle = blendStyle;
            cameraSwitchPerformConfig.custromBlendCurve = custromBlendCurve;
            cameraSwitchPerformConfig.blendTime = blendTime;

            return cameraSwitchPerformConfig;
        }

        public static CameraPerformConfig MakeCameraPerformConfig(CameraControllerData cameraControllerData, CameraSwitchPerformConfig cameraSwitchPerformConfig, HangingPerformType followPerform, HangingPerformType lookAtPerform, Vector3 followHangingOffset, Vector3 lookAtHangingOffset, float blendListHoldOnTime, bool checkSimilar)
        {
            CameraPerformConfig cameraPerformConfig = new CameraPerformConfig();
            cameraPerformConfig.cameraControllerData = cameraControllerData;
            cameraPerformConfig.cameraSwitchPerform = cameraSwitchPerformConfig;

            cameraPerformConfig.followPerform = followPerform;
            cameraPerformConfig.lookAtPerform = lookAtPerform;
            cameraPerformConfig.followHangingOffset = followHangingOffset;

            cameraPerformConfig.lookAtHangingOffset = lookAtHangingOffset;
            cameraPerformConfig.blendListHoldOnTime = blendListHoldOnTime;

            cameraPerformConfig.checkSimilar = checkSimilar;

            return cameraPerformConfig;
        }

        public static CameraRefTransParam TransBlendListRefItemToCameraTransParam(BlendListItemRefPerform blendListItemPerform)
        {
            CameraControllerData cameraControllerData = CCameraTools.MakeCameraControllerData(
                blendListItemPerform.FOVOffset,
                blendListItemPerform.screenRotateOffset,
                blendListItemPerform.screenHorizontalOffset,
                blendListItemPerform.screenVerticalOffset,
                blendListItemPerform.circleAngleOffset,
                blendListItemPerform.pitchOffset,
                blendListItemPerform.distanceOffset,
                blendListItemPerform.blendHint,
                blendListItemPerform.camShakeType,
                blendListItemPerform.camShakeAmpli,
                blendListItemPerform.camShakeFreq,
                blendListItemPerform.customLookAtTarget,
                blendListItemPerform.customLookATargetDistance,
                blendListItemPerform.cameraPositionOffset);

            CameraSwitchPerformConfig cameraSwitchPerform = MakeCameraSwitchPerformConfig(
                blendListItemPerform.blendStyle,
                blendListItemPerform.customBlendCurve,
                blendListItemPerform.blendTime);

            CameraPerformConfig cameraPerformConfig = MakeCameraPerformConfig(
                cameraControllerData,
                cameraSwitchPerform,
                blendListItemPerform.followPerform,
                blendListItemPerform.lookAtPerform,
                blendListItemPerform.cameraPositionOffset,
                blendListItemPerform.cameraPositionOffset,
                blendListItemPerform.houldOnTime,
                false);

            CameraRefTransParam cameraRefTransParam = new CameraRefTransParam();
            cameraRefTransParam.cam = blendListItemPerform.cam;
            cameraRefTransParam.cameraPerformConfig = cameraPerformConfig;

            return cameraRefTransParam;
        }

        public static CameraIDTransParam TransBlendListIDItemToCameraTransParam(BlendListItemIDPerform blendListItemPerform)
        {
            CameraControllerData cameraControllerData = CCameraTools.MakeCameraControllerData(
                blendListItemPerform.FOVOffset,
                blendListItemPerform.screenRotateOffset,
                blendListItemPerform.screenHorizontalOffset,
                blendListItemPerform.screenVerticalOffset,
                blendListItemPerform.circleAngleOffset,
                blendListItemPerform.pitchOffset,
                blendListItemPerform.distanceOffset,
                blendListItemPerform.blendHint,
                blendListItemPerform.camShakeType,
                blendListItemPerform.camShakeAmpli,
                blendListItemPerform.camShakeFreq,
                blendListItemPerform.customLookAtTarget,
                blendListItemPerform.customLookATargetDistance,
                blendListItemPerform.cameraPositionOffset);
            

            CameraSwitchPerformConfig cameraSwitchPerform = MakeCameraSwitchPerformConfig(
                blendListItemPerform.blendStyle,
                blendListItemPerform.customBlendCurve,
                blendListItemPerform.blendTime);

            CameraPerformConfig cameraPerformConfig = MakeCameraPerformConfig(
                cameraControllerData,
                cameraSwitchPerform,
                blendListItemPerform.followPerform,
                blendListItemPerform.lookAtPerform,
                blendListItemPerform.cameraPositionOffset,
                blendListItemPerform.cameraPositionOffset,
                blendListItemPerform.houldOnTime,
                false);

            CameraIDTransParam cameraRefTransParam = new CameraIDTransParam();
            cameraRefTransParam.camID = blendListItemPerform.camID;
            cameraRefTransParam.cameraPerformConfig = cameraPerformConfig;

            return cameraRefTransParam;
        }

        public static List<CameraRefTransParam> TransBlendListItemRefListToCameraRefTransParamList(List<BlendListItemRefPerform> blendListItemPerform)
        {
            List<CameraRefTransParam> cameraParamList = new List<CameraRefTransParam>();

            foreach (BlendListItemRefPerform i in blendListItemPerform)
            {
                cameraParamList.Add(TransBlendListRefItemToCameraTransParam(i));
            }
            return cameraParamList;
        }

        public static List<CameraIDTransParam> TransBlendListItemIDListToCameraIDTransParamList(List<BlendListItemIDPerform> blendListItemPerform)
        {
            List<CameraIDTransParam> cameraParamList = new List<CameraIDTransParam>();

            foreach (BlendListItemIDPerform i in blendListItemPerform)
            {
                cameraParamList.Add(TransBlendListIDItemToCameraTransParam(i));
            }
            return cameraParamList;
        }

        /////镜头命名解析
        public static CameraNameInfo AnalysisCameraInfoFromName(string cameraName)
        {
            if (!IsCamera(cameraName))
                return null;
            CameraNameInfo cameraNameInfo = new CameraNameInfo();
            string[] nameSplitArray = cameraName.Split("_");
            string header = nameSplitArray[0];

            Func<string, CameraShootingScale> ConvertStringToShootingScaleType = (string name) =>
            {
                switch (name)
                {
                    case "A":
                        return CameraShootingScale.Far;
                    case "B":
                        return CameraShootingScale.Mid;
                    case "C":
                        return CameraShootingScale.Near;
                    case "D":
                        return CameraShootingScale.VeryNear;
                    case "X":
                        return CameraShootingScale.Null;
                    default:
                        return CameraShootingScale.Null;
                }
            };

            switch (header)
            {
                case "Cam1":
                    if (nameSplitArray.Length == 7 && nameSplitArray[2] == "Offset")
                    {
                        DerivativeCameraInfo der = new DerivativeCameraInfo();
                        der.baseCamLensID = nameSplitArray[1].Substring(0, nameSplitArray[1].Length - 1);
                        //der.baseCam = new BaseCameraInfo();
                        der.screenXOffset = Convert.ToSingle(nameSplitArray[4]);
                        der.followYOffset = Convert.ToSingle(nameSplitArray[5]);
                        der.dutch = Convert.ToSingle(nameSplitArray[6]);
                        cameraNameInfo.derivativeCamInfo = der;
                        cameraNameInfo.baseCamInfo = null;
                        cameraNameInfo.camType = CameraType.SingleDerivative;
                        return cameraNameInfo;
                    }
                    if (nameSplitArray.Length == 10 || nameSplitArray.Length == 9)
                    {
                        BaseCameraInfo baseInfo = new BaseCameraInfo();
                        baseInfo.lensID = nameSplitArray[1];
                        baseInfo.camAngle = (CameraAngleType)Enum.Parse(typeof(CameraAngleType), nameSplitArray[2]);
                        baseInfo.camShootingScale = ConvertStringToShootingScaleType(nameSplitArray[3]);
                        baseInfo.FOV = Convert.ToSingle(nameSplitArray[4]);
                        baseInfo.lookAtHanging = new HangingInfoStringOffsetByName()
                        {
                            baseObjName = nameSplitArray[5],
                            offset = nameSplitArray[6],
                            type = HangingType.ActorBaseHanging
                        };
                        baseInfo.followHanging = new HangingInfoStringOffsetByName()
                        {
                            baseObjName = nameSplitArray[7],
                            offset = nameSplitArray[8],
                            type = HangingType.ActorBaseHanging
                        };
                        baseInfo.slotOffset = "Null";
                        cameraNameInfo.baseCamInfo = baseInfo;
                        cameraNameInfo.derivativeCamInfo = null;
                        cameraNameInfo.camType = CameraType.Single;
                        return cameraNameInfo;
                    }
                    return null;
                case "Cam2":
                    if (nameSplitArray.Length == 9)
                    {
                        BaseCameraInfo baseInfo = new BaseCameraInfo();
                        baseInfo.lensID = nameSplitArray[1];
                        baseInfo.camAngle = (CameraAngleType)Enum.Parse(typeof(CameraAngleType), nameSplitArray[2]);
                        baseInfo.camShootingScale = ConvertStringToShootingScaleType(nameSplitArray[3]);
                        baseInfo.FOV = Convert.ToSingle(nameSplitArray[4]);
                        baseInfo.lookAtHanging = new HangingInfoStringOffsetByName()
                        {
                            baseObjName = nameSplitArray[5],
                            offset = nameSplitArray[6],
                            type = HangingType.TargetGroup
                        };
                        baseInfo.followHanging = new HangingInfoStringOffsetByName()
                        {
                            baseObjName = nameSplitArray[7],
                            offset = "0",
                            type = HangingType.FixedPoint
                        };
                        baseInfo.slotOffset = nameSplitArray[8];
                        cameraNameInfo.baseCamInfo = baseInfo;
                        cameraNameInfo.derivativeCamInfo = null;
                        cameraNameInfo.camType = CameraType.TwoPerson;
                        return cameraNameInfo;
                    }
                    return null;
                case "Cam3":
                    if (nameSplitArray.Length == 9)
                    {
                        BaseCameraInfo baseInfo = new BaseCameraInfo();
                        baseInfo.lensID = nameSplitArray[1];
                        baseInfo.camAngle = (CameraAngleType)Enum.Parse(typeof(CameraAngleType), nameSplitArray[2]);
                        baseInfo.camShootingScale = ConvertStringToShootingScaleType(nameSplitArray[3]);
                        baseInfo.FOV = Convert.ToSingle(nameSplitArray[4]);
                        baseInfo.lookAtHanging = new HangingInfoStringOffsetByName()
                        {
                            baseObjName = nameSplitArray[5],
                            offset = nameSplitArray[6],
                            type = HangingType.TargetGroup
                        };
                        baseInfo.followHanging = new HangingInfoStringOffsetByName()
                        {
                            baseObjName = nameSplitArray[7],
                            offset = "0",
                            type = HangingType.FixedPoint
                        };
                        baseInfo.slotOffset = nameSplitArray[8];
                        cameraNameInfo.baseCamInfo = baseInfo;
                        cameraNameInfo.derivativeCamInfo = null;
                        cameraNameInfo.camType = CameraType.ThreePerson;
                        return cameraNameInfo;
                    }
                    return null;
                case "Cam4":
                    if (nameSplitArray.Length == 9)
                    {
                        BaseCameraInfo baseInfo = new BaseCameraInfo();
                        baseInfo.lensID = nameSplitArray[1];
                        baseInfo.camAngle = (CameraAngleType)Enum.Parse(typeof(CameraAngleType), nameSplitArray[2]);
                        baseInfo.camShootingScale = ConvertStringToShootingScaleType(nameSplitArray[3]);
                        baseInfo.FOV = Convert.ToSingle(nameSplitArray[4]);
                        baseInfo.lookAtHanging = new HangingInfoStringOffsetByName()
                        {
                            baseObjName = nameSplitArray[5],
                            offset = nameSplitArray[6],
                            type = HangingType.TargetGroup
                        };
                        baseInfo.followHanging = new HangingInfoStringOffsetByName()
                        {
                            baseObjName = nameSplitArray[7],
                            offset = "0",
                            type = HangingType.FixedPoint
                        };
                        baseInfo.slotOffset = nameSplitArray[8];
                        cameraNameInfo.baseCamInfo = baseInfo;
                        cameraNameInfo.derivativeCamInfo = null;
                        cameraNameInfo.camType = CameraType.FourPerson;
                        return cameraNameInfo;
                    }
                    return null;
                default:
                    return null;
            }
        }

        public static CameraType AnalysisCameraTypeFromName(string cameraName)
        {
            if (!IsCamera(cameraName))
                return CameraType.Null;
            
            string[] nameSplitArray = cameraName.Split("_");
            string header = nameSplitArray[0];

            switch (header)
            {
                case "Cam1":
                    if (nameSplitArray.Length == 7 && nameSplitArray[2] == "Offset")
                        return CameraType.SingleDerivative;
                    if (nameSplitArray.Length == 10 || nameSplitArray.Length == 9)
                        return CameraType.Single;
                    return CameraType.Null;
                case "Cam2":
                    if (nameSplitArray.Length == 9)
                        return CameraType.TwoPerson;
                    return CameraType.Null;
                case "Cam3":
                    if (nameSplitArray.Length == 9)
                        return CameraType.ThreePerson;
                    return CameraType.Null;
                case "Cam4":
                    if (nameSplitArray.Length == 9)
                        return CameraType.FourPerson;
                    return CameraType.Null;
                default:
                    return CameraType.Null;
            }
        }

        public static int GetSlotCountByCameraType(CameraType type)
        {
            switch (type)
            {
                case CameraType.Null: return 0;
                case CameraType.Single: return 1;
                case CameraType.SingleDerivative: return 1;
                case CameraType.TwoPerson: return 2;
                case CameraType.ThreePerson: return 3;
                case CameraType.FourPerson: return 4;
            }
            return 0;
        }

        public static int GetSlotCountByCameraName(string camName)
        {
            return GetSlotCountByCameraType(AnalysisCameraTypeFromName(camName));
        }

        public static CameraControllerData AnalysisCameraControllerConfigFromName(string cameraName)
        {
            if (cameraName == null) return null;

            CameraControllerData ccd = new CameraControllerData();
            CameraNameInfo cni = AnalysisCameraInfoFromName(cameraName);

            switch (cni.camType)
            {
                case CameraType.Single:
                    ccd.FOVOffset = cni.baseCamInfo.FOV;
                    return ccd;
                case CameraType.SingleDerivative:
                    //ccd.FOVOffset = cni.derivativeCamInfo.baseCam.FOV;
                    ccd.screenRotateOffset = cni.derivativeCamInfo.dutch;
                    ccd.screenHorizontalOffset = cni.derivativeCamInfo.screenXOffset;
                    ccd.pitchOffset = cni.derivativeCamInfo.followYOffset;
                    return ccd;
                case CameraType.TwoPerson:
                    ccd.FOVOffset = cni.baseCamInfo.FOV;
                    return ccd;
                case CameraType.ThreePerson:
                    ccd.FOVOffset = cni.baseCamInfo.FOV;
                    return ccd;
                default: break;
            }
            return null;
        }

        public static string GetCameraLensID(string cameraName)
        {
            if (!IsCamera(cameraName))
                return null;

            string[] nameSplitArray = cameraName.Split("_");
            string header = nameSplitArray[0];

            switch (header)
            {
                case "Cam1":
                    if (nameSplitArray.Length == 7 && nameSplitArray[2] == "Offset")
                        return nameSplitArray[1];
                    if (nameSplitArray.Length == 10 || nameSplitArray.Length == 9)
                        return nameSplitArray[1];
                    return null;
                case "Cam2":
                    if (nameSplitArray.Length == 9)
                        return nameSplitArray[1];
                    return null;
                case "Cam3":
                    if (nameSplitArray.Length == 9)
                        return nameSplitArray[1];
                    return null;
                default:
                    return null;
            }
        }

        public static CinemachineVirtualCamera GetLivedVirturalCamera(CinemachineBrain brain)
        {
            if (brain != null)
            {
                if (brain.ActiveVirtualCamera != null)
                {
                    if (brain.ActiveVirtualCamera.VirtualCameraGameObject.GetComponent<CinemachineVirtualCamera>() != null)
                    {
                        return brain.ActiveVirtualCamera.VirtualCameraGameObject.GetComponent<CinemachineVirtualCamera>();
                    }
                    if (brain.ActiveVirtualCamera.VirtualCameraGameObject.GetComponent<CinemachineBlendListCamera>() != null)
                    {
                        foreach (CinemachineBlendListCamera.Instruction c in brain.ActiveVirtualCamera.VirtualCameraGameObject.GetComponent<CinemachineBlendListCamera>().m_Instructions)
                        {
                            if (brain.IsLive(c.m_VirtualCamera, true))
                                if (c.m_VirtualCamera.gameObject != null && c.m_VirtualCamera.gameObject.GetComponent<CinemachineVirtualCamera>() != null)
                                return c.m_VirtualCamera.gameObject.GetComponent<CinemachineVirtualCamera>();
                        }
                    }
                }
            }
            return null;
        }
    }


    [ExecuteAlways]
    public class HangingInfoFloatOffsetByName
    {
        public string baseObjName;
        public float offset;
        public HangingType type;
    }

    [ExecuteAlways]
    public class HangingInfoFloatOffsetByTransform
    {
        public Transform baseObjTransform;
        public float offset;
        public HangingType type;
    }

    [ExecuteAlways]
    public class HangingInfoStringOffsetByName
    {
        public string baseObjName;
        public string offset;
        public HangingType type;
    }

    [ExecuteAlways]
    public class HangingInfoStringOffsetByTransform
    {
        public Transform baseObjTransform;
        public string offset;
        public HangingType type;
    }

    [ExecuteAlways]
    public class TargetGroupHangingInfoStringOffsetByName
    {
        public int slotIndex;
        public string baseObjName;
        public string offset;
        public HangingType type;
    }

    public enum HangingType
    {
        ActorBaseHanging,
        TargetGroup,
        FixedPoint
    }


    [ExecuteAlways]
    [Serializable]
    public class CameraNameInfo
    {
        public BaseCameraInfo baseCamInfo;
        public DerivativeCameraInfo derivativeCamInfo;
        public CameraType camType;
    }

    [ExecuteAlways]
    [Serializable]
    public class BaseCameraInfo
    {
        public string lensID;
        public CameraAngleType camAngle;
        public CameraShootingScale camShootingScale;
        public float FOV;
        public HangingInfoStringOffsetByName lookAtHanging;
        public HangingInfoStringOffsetByName followHanging;
        public string slotOffset;
    }

    [ExecuteAlways]
    [Serializable]
    public class DerivativeCameraInfo
    {
        public string baseCamLensID;
        //public BaseCameraInfo baseCam;
        public float screenXOffset;
        public float followYOffset;
        public float dutch;
    }

    public enum CameraType
    {
        Single,
        SingleDerivative,
        TwoPerson,
        ThreePerson,
        FourPerson,
        Null
    }

    public enum CameraAngleType
    {
        L5,
        L4,
        L3,
        L2,
        L1,
        F,
        R1,
        R2,
        R3,
        R4,
        R5,
        B,
        SP
    }

    public enum CameraShootingScale
    {
        Far,
        Mid,
        Near,
        VeryNear,
        Null
    }
}

