#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using UnityEditor;
using UnityEngine;
using VGame.Framework;
using Cysharp.Text;

namespace CEditor
{
    [ExecuteAlways]
    public class ExcelModifyTools : MonoBehaviour
    {
        public string fileName = "CEditorAssetsTableRow.xlsx";
        public string saveFileName = "CEditorAssetsTableForProgram.xlsx";
        public string bodyAssetsSheetName = "BodyAssetsTable";
        public string bodyEmoteSheetName = "BodyEmote";
        public string faceEmoteSheetNmae = "FaceEmote";
        public string personalitySheetName = "Personality";
        public string eyeAndBrowAssetsSheetName = "EyeAndBrowAssets";
        public string mouthIdleAssetsSheetName = "MouthIdleAsset";
        public string mouthTalkAssetsSheetName = "MouthTalkAssets";
        
        public bool reloadAndSaveFromExcel = false;
        public bool saveToExcel = false;
        public List<BodyAssetDetail> bodyAssetsList { get; set; } = new List<BodyAssetDetail>();
        public List<EmoteAssetDetail> bodyEmoteList { get; set; } = new List<EmoteAssetDetail>();
        public List<EmoteAssetDetail> faceEmoteList { get; set; } = new List<EmoteAssetDetail>();
        public List<PersonalityAssetDetail> personalityList { get; set; } = new List<PersonalityAssetDetail>();

        public List<FaceAssetsDetail> eyeAndBrowAssetsList { get; set; } = new List<FaceAssetsDetail>();
        public List<FaceAssetsDetail> mouthIdleAssetsList { get; set; } = new List<FaceAssetsDetail>();
        public List<FaceAssetsDetail> mouthTalkAssetsList { get; set; } = new List<FaceAssetsDetail>();

        public string basePath = System.IO.Path.Combine(System.Environment.CurrentDirectory, "../", "Design/Data/");
        public string savePath = @"Assets/Data/";
        // Start is called before the first frame update
        void Start()
        {
            //LoadBodyAssetsData(false);
            //LoadBodyEmoteData(false);
            //LoadPersonalityData(false);
        }

        // Update is called once per frame
        void Update()
        {
            if (reloadAndSaveFromExcel)
            {
                LoadBodyAssetsData(true);
                LoadBodyEmoteData(true);
                LoadPersonalityData(true);
                LoadFaceEmoteData();
                LoadEyesAndBrowData();
                LoadMouthIdleData();
                LoadMouthTalkData();

                reloadAndSaveFromExcel = false;
            }

            if (saveToExcel)
            {
                saveDataListToExcel();

                saveToExcel = false;
            }
        }

        public void LoadBodyAssetsData(bool reload)
        {
            if (!reload)
            {
                string filePath = ZString.Concat(savePath, "CEditorBodyAssetsCfg.asset");
                BodyAssetsScriptableObject assets;
                if (System.IO.File.Exists(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), filePath)))
                {
                    assets = AssetDatabase.LoadAssetAtPath<BodyAssetsScriptableObject>(@filePath);
                    if (assets != null && assets.assets != null)
                    {
                        bodyAssetsList.Clear();
                        bodyAssetsList = assets.assets;
                        Debug.Log("LoadBodyAssetsData");
                        return;
                    }
                }
            }

            DataTable dt = CExcelManager.GetSheet(ZString.Concat(basePath, fileName), bodyAssetsSheetName);
            if (dt != null)
            {
                bodyAssetsList.Clear();
                for (int i = 1; i < dt.Rows.Count; i++)
                {
                    BodyAssetDetail detail = new BodyAssetDetail();

                    if (!string.IsNullOrEmpty(dt.Rows[i]["FileName"].ToString()))
                    {
                        detail.ID = CAssetsManagerToolKit.ConvertNumStringToInt(dt.Rows[i]["ID"].ToString(), -1);
                        detail.header = dt.Rows[i]["Header"].ToString();
                        detail.fileName = dt.Rows[i]["FileName"].ToString();
                        detail.detail = dt.Rows[i]["Detail"].ToString();
                        detail.personality = dt.Rows[i]["Personality"].ToString();
                        detail.emotion = dt.Rows[i]["Emotion"].ToString();
                        detail.isReady = CAssetsManagerToolKit.Convert01StringToBool(dt.Rows[i]["IsReady"].ToString(), false);
                        detail.isLoop = CAssetsManagerToolKit.Convert01StringToBool(dt.Rows[i]["IsLoop"].ToString(), false);
                        detail.hasPostureChange = CAssetsManagerToolKit.Convert01StringToBool(dt.Rows[i]["HasPostureChange"].ToString(), false);
                        detail.postrue = CAssetsManagerToolKit.StringToBodyPosture(dt.Rows[i]["Posture"].ToString(), detail.hasPostureChange);
                        detail.hasBodyRotate = CAssetsManagerToolKit.Convert01StringToBool(dt.Rows[i]["HasBodyRotate"].ToString(), false);
                        detail.hasBodyMove = CAssetsManagerToolKit.Convert01StringToBool(dt.Rows[i]["HasBodyMove"].ToString(), false);
                        detail.playRate = CAssetsManagerToolKit.ConvertNumStringToFloat(dt.Rows[i]["PlayRate"].ToString(), 1);
                        detail.canMirror = CAssetsManagerToolKit.Convert01StringToBool(dt.Rows[i]["CanMirror"].ToString(), false);

                        detail.avoidBodyModelGlitchParam = new AvoidBodyModelGlitchParam();
                        detail.bodyWithCameraParam = new BodyWithCameraParam();

                        bodyAssetsList.Add(detail);
                    }
                }
            }
        }

        private void SaveBodyAssetsData(bool reload)
        {
            if (reload)
            {
                LoadBodyAssetsData(true);
            }
            string filePath = ZString.Concat(savePath, "CEditorBodyAssetsCfg.asset");
            if (!System.IO.Directory.Exists(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), savePath)))
                System.IO.Directory.CreateDirectory(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), savePath));
            if (System.IO.File.Exists(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), filePath)))
                System.IO.File.Delete(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), filePath));
            Debug.Log(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), filePath));
            BodyAssetsScriptableObject setting = ScriptableObject.CreateInstance<BodyAssetsScriptableObject>();
            setting.assets = bodyAssetsList;

            AssetDatabase.CreateAsset(setting, filePath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        public void LoadBodyEmoteData(bool reload)
        {
            if (!reload)
            {
                string filePath = ZString.Concat(savePath, "CEditorBodyEmoteCfg.asset");
                BodyEmoteAssetsScriptableObject assets;
                if (System.IO.File.Exists(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), filePath)))
                {
                    assets = AssetDatabase.LoadAssetAtPath<BodyEmoteAssetsScriptableObject>(@filePath);
                    if (assets != null && assets.assets != null)
                    {
                        bodyEmoteList.Clear();
                        bodyEmoteList = assets.assets;
                        Debug.Log("LoadBodyEmoteData");
                        return;
                    }
                }
            }

            DataTable dt = CExcelManager.GetSheet(ZString.Concat(basePath, fileName), bodyEmoteSheetName);
            if (dt != null)
            {
                bodyEmoteList.Clear();
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    EmoteAssetDetail detail = new EmoteAssetDetail();
                    detail.ID = CAssetsManagerToolKit.ConvertNumStringToInt(dt.Rows[i]["ID"].ToString(), -1);
                    detail.emote = dt.Rows[i]["Emote"].ToString();
                    detail.detail = dt.Rows[i]["Detail"].ToString();
                    detail.emoteType = (EmoteType)Enum.Parse(typeof(EmoteType), dt.Rows[i]["Type"].ToString(), true);

                    bodyEmoteList.Add(detail);
                }
            }
        }

        private void SaveBodyEmoteData(bool reload)
        {
            if (reload)
            {
                LoadBodyEmoteData(true);
            }
            string filePath = ZString.Concat(savePath, "CEditorBodyEmoteCfg.asset");
            if (!System.IO.Directory.Exists(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), savePath)))
                System.IO.Directory.CreateDirectory(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), savePath));
            if (System.IO.File.Exists(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), filePath)))
                System.IO.File.Delete(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), filePath));

            BodyEmoteAssetsScriptableObject setting = ScriptableObject.CreateInstance<BodyEmoteAssetsScriptableObject>();
            setting.assets = bodyEmoteList;
            AssetDatabase.CreateAsset(setting, filePath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }


        /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        public void LoadPersonalityData(bool reload)
        {
            if (!reload)
            {
                string filePath = ZString.Concat(savePath, "CEditorPersonalityCfg.asset");
                PersonalityAssetsScriptableObject assets;
                if (System.IO.File.Exists(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), filePath)))
                {
                    assets = AssetDatabase.LoadAssetAtPath<PersonalityAssetsScriptableObject>(@filePath);
                    if (assets != null && assets.assets != null)
                    {
                        personalityList.Clear();
                        personalityList = assets.assets;
                        Debug.Log("LoadPersonalityData");
                        return;
                    }
                }
            }

            DataTable dt = CExcelManager.GetSheet(ZString.Concat(basePath, fileName), personalitySheetName);
            if (dt != null)
            {
                personalityList.Clear();
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    PersonalityAssetDetail detail = new PersonalityAssetDetail();
                    detail.personality = dt.Rows[i]["Personality"].ToString();
                    detail.detail = dt.Rows[i]["Detail"].ToString();

                    personalityList.Add(detail);
                }
            }
        }

        private void SavePersonalityData(bool reload)
        {
            if (reload)
            {
                LoadPersonalityData(true);
            }
            string filePath = ZString.Concat(savePath, "CEditorPersonalityCfg.asset");
            if (!System.IO.Directory.Exists(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), savePath)))
                System.IO.Directory.CreateDirectory(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), savePath));
            if (System.IO.File.Exists(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), filePath)))
                System.IO.File.Delete(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), filePath));
            Debug.Log(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), filePath));
            PersonalityAssetsScriptableObject setting = ScriptableObject.CreateInstance<PersonalityAssetsScriptableObject>();
            setting.assets = personalityList;

            AssetDatabase.CreateAsset(setting, filePath);

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        public void LoadFaceEmoteData()
        {
            DataTable dt = CExcelManager.GetSheet(ZString.Concat(basePath, fileName), faceEmoteSheetNmae);
            if (dt != null)
            {
                faceEmoteList.Clear();
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    EmoteAssetDetail detail = new EmoteAssetDetail();
                    detail.emote = dt.Rows[i]["Emote"].ToString();
                    detail.detail = dt.Rows[i]["Detail"].ToString();

                    faceEmoteList.Add(detail);
                }
            }
        }

        public void LoadEyesAndBrowData()
        {
            DataTable dt = CExcelManager.GetSheet(ZString.Concat(basePath, fileName), eyeAndBrowAssetsSheetName);
            if (dt != null)
            {
                eyeAndBrowAssetsList.Clear();
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    FaceAssetsDetail detail = new FaceAssetsDetail();
                    detail.ID = CAssetsManagerToolKit.ConvertNumStringToInt(dt.Rows[i]["ID"].ToString(), -1);
                    detail.fileName = dt.Rows[i]["FileName"].ToString();
                    detail.detail = dt.Rows[i]["Detail"].ToString();
                    detail.isReady = CAssetsManagerToolKit.Convert01StringToBool(dt.Rows[i]["IsReady"].ToString(), false);

                    eyeAndBrowAssetsList.Add(detail);
                }
            }
        }

        public void LoadMouthIdleData()
        {
            DataTable dt = CExcelManager.GetSheet(ZString.Concat(basePath, fileName), mouthIdleAssetsSheetName);
            if (dt != null)
            {
                mouthIdleAssetsList.Clear();
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    FaceAssetsDetail detail = new FaceAssetsDetail();
                    detail.ID = CAssetsManagerToolKit.ConvertNumStringToInt(dt.Rows[i]["ID"].ToString(), -1);
                    detail.fileName = dt.Rows[i]["FileName"].ToString();
                    detail.detail = dt.Rows[i]["Detail"].ToString();
                    detail.isReady = CAssetsManagerToolKit.Convert01StringToBool(dt.Rows[i]["IsReady"].ToString(), false);

                    mouthIdleAssetsList.Add(detail);
                }
            }
        }

        public void LoadMouthTalkData()
        {
            DataTable dt = CExcelManager.GetSheet(ZString.Concat(basePath, fileName), mouthTalkAssetsSheetName);
            if (dt != null)
            {
                mouthTalkAssetsList.Clear();
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    FaceAssetsDetail detail = new FaceAssetsDetail();
                    detail.ID = CAssetsManagerToolKit.ConvertNumStringToInt(dt.Rows[i]["ID"].ToString(), -1);
                    detail.fileName = dt.Rows[i]["FileName"].ToString();
                    detail.detail = dt.Rows[i]["Detail"].ToString();
                    detail.isReady = CAssetsManagerToolKit.Convert01StringToBool(dt.Rows[i]["IsReady"].ToString(), false);

                    mouthTalkAssetsList.Add(detail);
                }
            }
        }



        private void saveDataListToExcel()
        {
            DataSet ds = new DataSet();
            DataTable bodyAssetsDT = new DataTable();
            bodyAssetsDT.TableName = bodyAssetsSheetName;
            DataTable bodyEmoAssetsDt = new DataTable();
            bodyEmoAssetsDt.TableName = bodyEmoteSheetName;
            DataTable personalityDT = new DataTable();
            personalityDT.TableName = personalitySheetName;

            DataTable eyeAndBrowAssetsDT = new DataTable();
            eyeAndBrowAssetsDT.TableName = eyeAndBrowAssetsSheetName;
            DataTable mouthIdleAssetsDT = new DataTable();
            mouthIdleAssetsDT.TableName = mouthIdleAssetsSheetName;
            DataTable mouthTalkAssetsDT = new DataTable();
            mouthTalkAssetsDT.TableName = mouthTalkAssetsSheetName;

            DataTable faceEmoteAssetsDT = new DataTable();
            faceEmoteAssetsDT.TableName = faceEmoteSheetNmae;

            List<String> bodyAssetsTitles = new List<String>();
            List<string> bodyEmoTitles = new List<string>();
            List<string> personalityTitles = new List<string>();

            List<string> faceAssetsTitles = new List<string>();

            List<string> faceEmoteTitles = new List<string>();

            bodyAssetsTitles.Add("ID");
            bodyAssetsTitles.Add("Header");
            bodyAssetsTitles.Add("FileName");
            bodyAssetsTitles.Add("Detail");
            bodyAssetsTitles.Add("Personality");
            bodyAssetsTitles.Add("Emotion");
            bodyAssetsTitles.Add("IsReady");
            bodyAssetsTitles.Add("IsLoop");
            bodyAssetsTitles.Add("Posture");
            bodyAssetsTitles.Add("HasBodyRotate");
            bodyAssetsTitles.Add("HasBodyMove");
            bodyAssetsTitles.Add("PlayRate");
            bodyAssetsTitles.Add("HasPostureChange");
            bodyAssetsTitles.Add("CanMirror");
            bodyAssetsTitles.Add("EndPoseTouchTwoHand");
            bodyAssetsTitles.Add("EndPoseTouthRHandWithLArm");
            bodyAssetsTitles.Add("EndPoseTouthRHandWithBody");
            bodyAssetsTitles.Add("EndPoseTouthLHandWithRArm");
            bodyAssetsTitles.Add("EndPoseTouthLHandWithBody");
            bodyAssetsTitles.Add("UpperBodySignificantMovement");
            bodyAssetsTitles.Add("UpperBodyFastMovement");
            bodyAssetsTitles.Add("LowerBodyMovement");

            bodyEmoTitles.Add("ID");
            bodyEmoTitles.Add("Emote");
            bodyEmoTitles.Add("Detail");
            bodyEmoTitles.Add("Type");

            personalityTitles.Add("Personality");
            personalityTitles.Add("Detail");

            faceAssetsTitles.Add("ID");
            faceAssetsTitles.Add("FileName");
            faceAssetsTitles.Add("Detail");
            faceAssetsTitles.Add("IsReady");

            faceEmoteTitles.Add("Emote");
            faceEmoteTitles.Add("Detail");

            foreach (string titleName in bodyAssetsTitles)
            {
                bodyAssetsDT.Columns.Add(titleName);
            }
            foreach (BodyAssetDetail bodyAssetDetail in bodyAssetsList)
            {
                DataRow dr = bodyAssetsDT.NewRow();
                dr[0] = bodyAssetDetail.ID.ToString();
                dr[1] = bodyAssetDetail.header;
                dr[2] = bodyAssetDetail.fileName;
                dr[3] = bodyAssetDetail.detail;
                dr[4] = bodyAssetDetail.personality;
                dr[5] = bodyAssetDetail.emotion.ToString() == "null" ? "" : bodyAssetDetail.emotion.ToString();
                dr[6] = bodyAssetDetail.isReady ? "1" : "0";
                dr[7] = bodyAssetDetail.isLoop ? "1" : "0";
                dr[8] = bodyAssetDetail.postrue.ToString();
                dr[9] = bodyAssetDetail.hasBodyRotate ? "1" : "0";
                dr[10] = bodyAssetDetail.hasBodyMove ? "1" : "0";
                dr[11] = bodyAssetDetail.playRate.ToString();
                dr[12] = bodyAssetDetail.hasPostureChange ? "1" : "0";
                dr[13] = bodyAssetDetail.canMirror ? "1" : "0";
                dr[14] = bodyAssetDetail.avoidBodyModelGlitchParam.EndPoseTouchTwoHand ? "1" : "0";
                dr[15] = bodyAssetDetail.avoidBodyModelGlitchParam.EndPoseTouthRHandWithLArm ? "1" : "0";
                dr[16] = bodyAssetDetail.avoidBodyModelGlitchParam.EndPoseTouthRHandWithBody ? "1" : "0";
                dr[17] = bodyAssetDetail.avoidBodyModelGlitchParam.EndPoseTouthLHandWithRArm ? "1" : "0";
                dr[18] = bodyAssetDetail.avoidBodyModelGlitchParam.EndPoseTouthLHandWithBody ? "1" : "0";
                dr[19] = bodyAssetDetail.bodyWithCameraParam.UpperBodySignificantMovement ? "1" : "0";
                dr[20] = bodyAssetDetail.bodyWithCameraParam.UpperBodyFastMovement ? "1" : "0";
                dr[21] = bodyAssetDetail.bodyWithCameraParam.LowerBodyMovement ? "1" : "0";

                bodyAssetsDT.Rows.Add(dr);
            }
            ds.Tables.Add(bodyAssetsDT);

            foreach (string titleName in bodyEmoTitles)
            {
                bodyEmoAssetsDt.Columns.Add(titleName);
            }
            foreach (EmoteAssetDetail bodyEmote in bodyEmoteList)
            {
                DataRow dr = bodyEmoAssetsDt.NewRow();
                dr[0] = bodyEmote.ID.ToString();
                dr[1] = bodyEmote.emote;
                dr[2] = bodyEmote.detail;
                dr[3] = bodyEmote.emoteType.ToString();

                bodyEmoAssetsDt.Rows.Add(dr);
            }
            ds.Tables.Add(bodyEmoAssetsDt);

            foreach (string titleName in personalityTitles)
            {
                personalityDT.Columns.Add(titleName);
            }
            foreach (PersonalityAssetDetail personalityAssets in personalityList)
            {
                DataRow dr = personalityDT.NewRow();
                dr[0] = personalityAssets.personality;
                dr[1] = personalityAssets.detail;

                personalityDT.Rows.Add(dr);
            }
            ds.Tables.Add(personalityDT);


            foreach (string titleName in faceAssetsTitles)
            {
                eyeAndBrowAssetsDT.Columns.Add(titleName);
                mouthIdleAssetsDT.Columns.Add(titleName);
                mouthTalkAssetsDT.Columns.Add(titleName);
            }
            foreach (FaceAssetsDetail faceAssets in eyeAndBrowAssetsList)
            {
                DataRow dr = eyeAndBrowAssetsDT.NewRow();
                dr[0] = faceAssets.ID.ToString();
                dr[1] = faceAssets.fileName;
                dr[2] = faceAssets.detail;
                dr[3] = faceAssets.isReady ? "1" : "0";

                eyeAndBrowAssetsDT.Rows.Add(dr);
            }
            ds.Tables.Add(eyeAndBrowAssetsDT);
            foreach (FaceAssetsDetail faceAssets in mouthIdleAssetsList)
            {
                DataRow dr = mouthIdleAssetsDT.NewRow();
                dr[0] = faceAssets.ID.ToString();
                dr[1] = faceAssets.fileName;
                dr[2] = faceAssets.detail;
                dr[3] = faceAssets.isReady ? "1" : "0";

                mouthIdleAssetsDT.Rows.Add(dr);
            }
            ds.Tables.Add(mouthIdleAssetsDT);
            foreach (FaceAssetsDetail faceAssets in mouthTalkAssetsList)
            {
                DataRow dr = mouthTalkAssetsDT.NewRow();
                dr[0] = faceAssets.ID.ToString();
                dr[1] = faceAssets.fileName;
                dr[2] = faceAssets.detail;
                dr[3] = faceAssets.isReady ? "1" : "0";

                mouthTalkAssetsDT.Rows.Add(dr);
            }
            ds.Tables.Add(mouthTalkAssetsDT);

            foreach (string titleName in faceEmoteTitles)
            {
                faceEmoteAssetsDT.Columns.Add(titleName);
            }
            foreach (EmoteAssetDetail faceEmote in faceEmoteList)
            {
                DataRow dr = faceEmoteAssetsDT.NewRow();
                dr[0] = faceEmote.emote;
                dr[1] = faceEmote.detail;

                faceEmoteAssetsDT.Rows.Add(dr);
            }
            ds.Tables.Add(faceEmoteAssetsDT);


            CExcelManager.Create(ZString.Concat(basePath, "CEditorAssetsTableForProgram.xlsx"), ds);
        }
        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
        }
    }
}
#endif