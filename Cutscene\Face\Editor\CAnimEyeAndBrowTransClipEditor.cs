#if UNITY_EDITOR
using UnityEditor;
using Slate.ActionClips;
using CEditor;

// [CustomEditor(typeof(CAnimEyeAndBrowTransClip))]
public class CAnimEyeAndBrowTransClipEditor : Editor
{
    private SerializedObject obj; //序列化

    private SerializedProperty eyeAndBrowClip, eyeAndBrowWeight;
    private SerializedProperty eyesPlayingMode, eyesStartTime, eyesEndTime, eyesBlendType, eyesBlendInDuration, eyesStaticBlendType, eyesStaticBlendInDuration;
    private SerializedProperty eyebrowPlayingMode, eyebrowStartTime, eyebrowEndTime, eyebrowBlendType, eyebrowBlendInDuration, eyebrowStaticBlendType, eyebrowStaticBlendInDuration;
    private SerializedProperty useEyesOverwriteAnim, eyesOverwriteAnim, eyesOverwriteAnimFrameTime, eyesOverwriteAnimBlendInTime, eyesOverwriteAnimBlendOutTime;

    void OnEnable()
    {
        obj = new SerializedObject(target);

        eyeAndBrowClip = obj.FindProperty("eyeAndBrowClip");
        eyeAndBrowWeight = obj.FindProperty("eyeAndBrowWeight");

        eyesPlayingMode = obj.FindProperty("eyesPlayingMode");
        eyesStartTime = obj.FindProperty("eyesStartTime");
        eyesEndTime = obj.FindProperty("eyesEndTime");
        eyesBlendType = obj.FindProperty("eyesBlendType");
        eyesBlendInDuration = obj.FindProperty("eyesBlendInDuration");
        eyesStaticBlendType = obj.FindProperty("eyesStaticBlendType");
        eyesStaticBlendInDuration = obj.FindProperty("eyesStaticBlendInDuration");

        eyebrowPlayingMode = obj.FindProperty("eyebrowPlayingMode");
        eyebrowStartTime = obj.FindProperty("eyebrowStartTime");
        eyebrowEndTime = obj.FindProperty("eyebrowEndTime");
        eyebrowBlendType = obj.FindProperty("eyebrowBlendType");
        eyebrowBlendInDuration = obj.FindProperty("eyebrowBlendInDuration");
        eyebrowStaticBlendType = obj.FindProperty("eyebrowStaticBlendType");
        eyebrowStaticBlendInDuration = obj.FindProperty("eyebrowStaticBlendInDuration");

        useEyesOverwriteAnim = obj.FindProperty("useEyesOverwriteAnim");
        eyesOverwriteAnim = obj.FindProperty("eyesOverwriteAnim");
        eyesOverwriteAnimFrameTime = obj.FindProperty("eyesOverwriteAnimFrameTime");
        eyesOverwriteAnimBlendInTime = obj.FindProperty("eyesOverwriteAnimBlendInTime");
        eyesOverwriteAnimBlendOutTime = obj.FindProperty("eyesOverwriteAnimBlendOutTime");
    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        obj.Update();

        EditorGUILayout.PropertyField(eyeAndBrowClip);
        EditorGUILayout.PropertyField(eyeAndBrowWeight);

        EditorGUILayout.Space(10);

        EditorGUILayout.PropertyField(eyesBlendType);
        EditorGUILayout.PropertyField(eyesBlendInDuration);
        EditorGUILayout.PropertyField(eyesPlayingMode);

        if (eyesPlayingMode.enumValueIndex == ((int)PlayingMode.EndStop) || eyesPlayingMode.enumValueIndex == ((int)PlayingMode.BlendLoop)/* || eyesPlayingMode.enumValueIndex == ((int)PlayingMode.LoopThenStop)*/)
        {
            EditorGUILayout.PropertyField(eyesStartTime);
            EditorGUILayout.PropertyField(eyesEndTime);

            EditorGUILayout.PropertyField(eyesStaticBlendType);
            EditorGUILayout.PropertyField(eyesStaticBlendInDuration);
        }

        EditorGUILayout.Space(10);

        EditorGUILayout.PropertyField(eyebrowBlendType);
        EditorGUILayout.PropertyField(eyebrowBlendInDuration);
        EditorGUILayout.PropertyField(eyebrowPlayingMode);

        if (eyebrowPlayingMode.enumValueIndex == ((int)PlayingMode.EndStop) || eyebrowPlayingMode.enumValueIndex == ((int)PlayingMode.BlendLoop)/* || eyebrowPlayingMode.enumValueIndex == ((int)PlayingMode.LoopThenStop)*/)
        {
            EditorGUILayout.PropertyField(eyebrowStartTime);
            EditorGUILayout.PropertyField(eyebrowEndTime);

            EditorGUILayout.PropertyField(eyebrowStaticBlendType);
            EditorGUILayout.PropertyField(eyebrowStaticBlendInDuration);
        }

        EditorGUILayout.Space(10);

        EditorGUILayout.PropertyField(useEyesOverwriteAnim);
        if (useEyesOverwriteAnim.boolValue)
        {
            EditorGUILayout.PropertyField(eyesOverwriteAnim);
            EditorGUILayout.PropertyField(eyesOverwriteAnimFrameTime);
            EditorGUILayout.PropertyField(eyesOverwriteAnimBlendInTime);
            EditorGUILayout.PropertyField(eyesOverwriteAnimBlendOutTime);
        }

        obj.ApplyModifiedProperties();
    }
}
#endif