﻿using System;
using UnityEngine;
using UnityEngine.Events;
using Cysharp.Text;
using uLipSync;

#if UNITY_EDITOR
using UnityEditor.Events;
#endif

namespace CEditor
{
    [ExecuteAlways]
    public class LipSyncManager : MonoBehaviour
    {
        public uLipSyncBlendShape sULipSyncBlendShape;
        public uLipSyncBakedDataPlayer sULipSyncBakedDataPlayer;

        private void Start()
        {
            // Init();
        }

        private void Update()
        {
            // Debug.Log("Time.time: " + Time.time);
            // Debug.Log("Time.realtimeSinceStartup: " + Time.realtimeSinceStartup);

        }

        public void Init()
        {
            //create lipsyncBS
            if (gameObject.GetComponent<uLipSyncBlendShape>() == null)
            {
                sULipSyncBlendShape = gameObject.AddComponent<uLipSyncBlendShape>();
                
                // setup blend shape
                SkinnedMeshRenderer faceRenderer = GetFaceRenderer();
                if (faceRenderer == null)
                {
                    Debug.LogWarning("[LipSyncManager] Could not find face renderer contains \"Face\" in children");
                    return;
                }
                sULipSyncBlendShape.skinnedMeshRenderer = faceRenderer;
                sULipSyncBlendShape.ClearBlendShapes();
                // setup with default setting
                SetBlendShapeNodeWithPreset(sULipSyncBlendShape,PresetBlendShapeSettings.Default);

            }
            else
            {
                sULipSyncBlendShape = gameObject.GetComponent<uLipSyncBlendShape>();
            }
            
            // create baked data player
            if (gameObject.GetComponent<uLipSyncBakedDataPlayer>() == null)
            {
                sULipSyncBakedDataPlayer = gameObject.AddComponent<uLipSyncBakedDataPlayer>();
                // setup baked player
                sULipSyncBakedDataPlayer.playAudioSource = false;
                sULipSyncBakedDataPlayer.playOnAwake = false;
                sULipSyncBakedDataPlayer.onLipSyncUpdate.RemoveAllListeners();
                #if UNITY_EDITOR
                    UnityEventTools.AddPersistentListener(sULipSyncBakedDataPlayer.onLipSyncUpdate,sULipSyncBlendShape.OnLipSyncUpdate);
                    int listenerNum = sULipSyncBakedDataPlayer.onLipSyncUpdate.GetPersistentEventCount();
                    if (listenerNum!= 1)
                    {
                        Debug.LogError("[LipSyncManager] Add listener to onLipSyncUpdate Failed");
                        return;
                    }
                    sULipSyncBakedDataPlayer.onLipSyncUpdate.SetPersistentListenerState(0,UnityEventCallState.EditorAndRuntime);
                #else    
                    sULipSyncBakedDataPlayer.onLipSyncUpdate.AddListener(sULipSyncBlendShape.OnLipSyncUpdate);
                #endif
               
            }
            else
            {
                sULipSyncBakedDataPlayer = gameObject.GetComponent<uLipSyncBakedDataPlayer>();
            }

        }

        public void SetBlendShapeNodeWithPreset(uLipSyncBlendShape uLsbs, BlendShapeSetting bSs)
        {
            uLsbs.maxVolume = bSs.MaxVolume;
            uLsbs.minVolume = bSs.MinVolume;
            uLsbs.smoothness = bSs.Smoothness;
            uLsbs.ClearBlendShapes();
            foreach (var phonemeinfo in bSs.PhonemeBlendShapeTable)
            {
                uLipSyncBlendShape.BlendShapeInfo bs = uLsbs.AddBlendShape(phonemeinfo.Phoneme, phonemeinfo.BlendShape);
                bs.maxWeight = phonemeinfo.MaxWeight;
                if (!phonemeinfo.BlendShape.Equals("None") && bs.index < 0 )
                {
                    Debug.LogWarning(ZString.Format("[LipSyncManager] Could not find blendshape '{0}' in skinned mesh renderer '{1}'", phonemeinfo.BlendShape, uLsbs.skinnedMeshRenderer.name));
                }
            }
        }
        
        public SkinnedMeshRenderer GetFaceRenderer()
        {
            SkinnedMeshRenderer faceRenderer = null;
            foreach (SkinnedMeshRenderer smr in gameObject.GetComponentsInChildren<SkinnedMeshRenderer>())
            {
                if (smr.name.Contains("Face"))
                {
                    faceRenderer = smr;
                    break;
                }
            }

            return faceRenderer;
        }
    }
}