#if UNITY_EDITOR
using UnityEditor;
using Slate.ActionClips;

// [CustomEditor(typeof(CAnimEmoTransClip))]
public class CAnimEmoTransClipEditor : Editor
{
    private SerializedObject obj; //序列化

    private SerializedProperty emo;

    void OnEnable()
    {
        obj = new SerializedObject(target);

        emo = obj.FindProperty("emo");
    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        obj.Update();

        EditorGUILayout.PropertyField(emo);

        obj.ApplyModifiedProperties();
    }
}
#endif