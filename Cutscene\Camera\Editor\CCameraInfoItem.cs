﻿#if UNITY_EDITOR
using System;
using System.Collections;
using Sirenix.OdinInspector;
using Sirenix.Utilities.Editor;
using UnityEngine;
using UnityEngine.Networking;
using ObjectFieldAlignment = Sirenix.OdinInspector.ObjectFieldAlignment;

//using Unity.EditorCoroutines.Editor;

[Serializable]
public class CCameraInfoItem
{
    [TableColumnWidth(100)]
    [PreviewField(Height = 80, Alignment = ObjectFieldAlignment.Center)]
    public Texture2D thumbnail;

    [TableColumnWidth(200)]
    [FilePath(ParentFolder = "CEditorPreview")]
    [HideInInspector]
    public string filePath;
    
    [TableColumnWidth(100)]
    [LabelWidth(100)]
    [DisplayAsString]
    public int Id;
    
    [TableColumnWidth(200)]
    [LabelWidth(200)]
    [DisplayAsString]
    public string Detail;

    [TableColumnWidth(50)]
    [But<PERSON>("选择")]
    public void ConfirmAction()
    {
        if (GUIHelper.CurrentWindow is CImageBrowserWindow window)
        {
            window.OnItemSelected?.Invoke(this);
        }
    }
}

public struct SCameraInfoParams
{
    public int Id;
    public string Path;
    public string Detail;
}
#endif