using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace CEditor
{
    public static class LookAtDataCollector
    {
        public enum LookAtTargetType
        {
            None,
            ActorBody,
            Camera,
            GameObject
        }

        public enum LookAtBodyTarget
        {
            Head,
            Chest,
            Waist,
            Hip,
            RightArm,
            LeftArm,
            RightHand,
            LeftHand,
            RightLeg,
            LeftLeg,
            RightFoot,
            LeftFoot
        }

        public static Dictionary<LookAtBodyTarget, string> lookAtBodyBoneName = new Dictionary<LookAtBodyTarget, string>()
    {
        {LookAtBodyTarget.Head, "Bip01_Head"},
        {LookAtBodyTarget.Chest, "Bip01_Spine2"},
        {LookAtBodyTarget.Waist, "Bip01_Spine1"},
        {LookAtBodyTarget.Hip, "Bip01_Pelvis"},
        {LookAtBodyTarget.RightArm, "Bip01_R_Forearm"},
        {LookAtBodyTarget.LeftArm, "Bip01_L_Forearm"},
        {LookAtBodyTarget.RightHand, "Bip01_R_Hand"},
        {LookAtBodyTarget.LeftHand, "Bip01_L_Hand"},
        {LookAtBodyTarget.RightLeg, "Bip01_R_Calf"},
        {LookAtBodyTarget.LeftLeg, "Bip01_L_Calf"},
        {LookAtBodyTarget.RightFoot, "Bip01_R_Foot"},
        {LookAtBodyTarget.LeftFoot, "Bip01_L_Foot"},
    };
    }
}
