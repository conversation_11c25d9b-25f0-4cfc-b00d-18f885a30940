using UnityEngine;

namespace CEditor
{
    [ExecuteAlways]
    public static class AnimTransToolTik
    {
        public static float GetBlendTypeWeightFromTime(BlendType blendType, float time01)
        {
            switch (blendType)
            {
                case BlendType.Linear: return LinearBlend(time01);
                case BlendType.EaseInOut: return EaseInOut(time01);
                case BlendType.EaseIn: return EaseIn(time01);
                case BlendType.EaseOut: return EaseOut(time01);
                case BlendType.QuickEaseInSlowEaseOut: return QuickEaseInSlowEaseOut(time01);
                default: return EaseInOut(time01);
            }
        }

        public static float LinearBlend(float time01)
        {
            return Mathf.Clamp01(time01);
        }

        public static float EaseInOut(float time01)
        {
            return NormSigmoid(time01, 9, 0, 1);
        }

        public static float EaseOut(float time01)
        {
            return NormSigmoid(time01, 8, 0, 0.65f);
        }

        public static float EaseIn(float time01)
        {
            return Mathf.Pow(NormSigmoid(time01, 5, -0.2f, 0.8f), 1.7f);
        }

        public static float QuickEaseInSlowEaseOut(float time01)
        {
            return NormSigmoid(time01, 8, 0.15f, 1.1f);
        }

        public static float Sigmoid(float x, float sharpness, float offset, float pow)
        {
            x = (x - 0.5f + offset) * sharpness;
            return 1 / (1 + Mathf.Exp(-x));
        }

        public static float NormSigmoid(float x, float sharpness, float offset, float pow)
        {
            float sigmoid0 = Sigmoid(0, sharpness, offset, pow);
            float sigmoid1 = Sigmoid(1, sharpness, offset, pow);
            float sig = Sigmoid(x, sharpness, offset, pow);
            return (sig - sigmoid0) / (sigmoid1 - sigmoid0);
        }
    }
}

