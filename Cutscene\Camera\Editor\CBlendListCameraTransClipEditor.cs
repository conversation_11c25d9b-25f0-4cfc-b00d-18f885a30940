#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;
using CEditor;
using System.Collections.Generic;
using System.Linq;
using Sirenix.OdinInspector.Editor;
using VGame.Framework;
using Slate;
using Cysharp.Text;

[CustomEditor(typeof(CBlendListCameraTransClip))] //关联之前的脚本
public class CBlendListCameraTransClipEditor : OdinEditor
{
    private SerializedObject obj; //序列化

    //private SerializedProperty lensCutBlendTime, lensCutBlendType, lensCutCustomBlendCurve;

    private Texture2D previewTexture;
    private List<int> newCamIDList;
    private List<int> prevCamIDList;
    private string picName = "";
    private string picsBasePath = System.IO.Path.Combine(System.Environment.CurrentDirectory, "CEditorPreview");
    //private ReorderableList blendList;

    void OnEnable()
    {
        obj = new SerializedObject(target);

        //blendList = new ReorderableList(obj, obj.FindProperty("cameraPerformConfigsList"), true, true, true, true);
        //blendList.drawHeaderCallback = (Rect rect) =>
        //{
        //    GUI.Label(rect, "运镜元素列表");
        //};
        //blendList.elementHeight = EditorGUIUtility.singleLineHeight * 16;

        //blendList.drawElementCallback = (Rect rect, int index, bool selected, bool focused) =>
        //{
        //    //根据index获取对应元素 
        //    SerializedProperty item = blendList.serializedProperty.GetArrayElementAtIndex(index);
        //    rect.height -= 4;
        //    rect.y += 2;
        //    EditorGUI.PropertyField(rect, item, new GUIContent("Index " + index));
        //};

        //blendList.onRemoveCallback = (ReorderableList list) =>
        //{
        //    if (EditorUtility.DisplayDialog("Warnning", "是否删除该元素?", "删除", "取消"))
        //    {
        //        ReorderableList.defaultBehaviours.DoRemoveButton(list);
        //    }
        //};

        //lensCutBlendTime = obj.FindProperty("lensCutBlendTime");
        //lensCutBlendType = obj.FindProperty("lensCutBlendType");
        //lensCutCustomBlendCurve = obj.FindProperty("lensCutCustomBlendCurve");
        //cameraPerformConfigsList = obj.FindProperty("cameraPerformConfigsList");

    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        //EditorGUIUtility.labelWidth = 180;
        obj.Update();

        CBlendListCameraTransClip script = (CBlendListCameraTransClip)target;

        CCameraTransTrack track = script.parent as CCameraTransTrack;
        int templateID = 0;
        if (track != null)
            templateID = track.CamTemplateID;

        foreach (BlendListItemIDPerform i in script.cameraPerformConfigsList)
        {
            i.SetTargetTag(script.GetTargetTag());
            i.SetTemplateID(templateID);
        }

        newCamIDList = script.cameraPerformConfigsList.Select(c => c.camID).ToList();
        if (newCamIDList != null)
        {
            if (prevCamIDList == null)
            {
                prevCamIDList = new List<int>();
            }
            if (newCamIDList.Count == prevCamIDList.Count)
            {
                if (!CompareTwoList(newCamIDList, prevCamIDList))
                {
                    for (int i = 0; i < newCamIDList.Count; i++)
                    {
                        if (newCamIDList[i] != prevCamIDList[i])
                        {
                            picName = ZString.Concat(CfgManager.tables.TbCEditorCameraAsset.GetOrDefault(newCamIDList[i]).UnityName.Split("_")[0], "_", CfgManager.tables.TbCEditorCameraAsset.GetOrDefault(newCamIDList[i]).LensID.ToString(), ".png");
                            string camPicPath = System.IO.Path.Combine(picsBasePath, picName);
                            if (System.IO.Directory.Exists(picsBasePath) && System.IO.File.Exists(camPicPath))
                                previewTexture = CEditorUITools.LoadTextureFromFile(camPicPath);
                        }
                    }
                    prevCamIDList = CopyIDList(newCamIDList);
                }
            }
            else
            {
                prevCamIDList = CopyIDList(newCamIDList);
            }
        }
        if (previewTexture != null)
        {
            //EditorGUILayout.Space();
            EditorGUILayout.LabelField("基础镜头预览");
            // 显示图片，宽度为 200，高度自动适应
            float aspectRatio = (float)previewTexture.width / previewTexture.height;
            float previewWidth = Screen.width / 3;
            float previewHeight = previewWidth / aspectRatio;
            Rect rect = GUILayoutUtility.GetRect(128, 128);
            rect.width = previewWidth;
            rect.height = previewHeight;
            EditorGUI.DrawPreviewTexture(
                rect,
                previewTexture
            );
            EditorGUILayout.Space(40);
        }

        //EditorGUILayout.PropertyField(lensCutBlendTime, new GUIContent("过渡时长"));
        //EditorGUILayout.PropertyField(lensCutBlendType, new GUIContent("过渡曲线类型"));
        //if (lensCutBlendType.enumValueIndex == ((int)CinemachineBlendDefinition.Style.Custom))
        //    EditorGUILayout.PropertyField(lensCutCustomBlendCurve, new GUIContent("自定义曲线"));
        //EditorGUILayout.PropertyField(cameraPerformConfigsList, new GUIContent("镜头列表"));

        //blendList.DoLayoutList();
        //obj.ApplyModifiedProperties();
        //(CBlendListCameraTransClip)target;
        //foreach (BlendListItemIDPerform i in cameraPerformConfigsList)
        //{
        //    i.SetTargetTag(GetTargetTag());
        //}
    }

    private List<int> CopyIDList(List<int> list)
    {
        List<int> copy = new List<int>();
        if (list != null)
        {
            foreach (int i in list)
            {
                copy.Add(i);
            }
        }
        return copy;
    }

    private bool CompareTwoList(List<int> list1, List<int> list2)
    {
        if (list1 != null && list2 != null)
        {
            if (list1.Count == list2.Count)
            {
                for (int i = 0; i < list1.Count; i++)
                {
                    if (list2[i] != list1[i])
                        return false;
                }
                return true;
            }
            else
                return false;
        }
        else
        {
            if (list1 == null && list2 == null)
                return true;
            else
                return false;
        }
    }
}
#endif
