//#if UNITY_EDITOR
//using System.Collections;
//using System.Collections.Generic;
//using UnityEditor;
//using UnityEngine;

//namespace CEditor 
//{
//    [CustomPropertyDrawer(typeof(BlendListItemPerform))]
//    public class CCameraPropertyDraw : PropertyDrawer
//    {
//        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
//        {
//            using (new EditorGUI.PropertyScope(position, label, property))
//            {
//                //设置属性名宽度
//                EditorGUIUtility.labelWidth = 120;
//                position.height = EditorGUIUtility.singleLineHeight * 14;

//                float propertyH = EditorGUIUtility.singleLineHeight;
//                var camRect = new Rect(position.x, position.y, position.width, propertyH);

//                var blendStyleRect = new Rect(position.x, position.y + propertyH * 1, position.width / 2, propertyH);
//                var customBlendCurveRect = new Rect(position.x, position.y + propertyH * 2, position.width / 2, propertyH);
//                var blendTimeRect = new Rect(position.x, position.y + propertyH * 3, position.width / 2, propertyH);
//                var houldOnTimeRect = new Rect(position.x, position.y + propertyH * 4, position.width / 2, propertyH);

//                var useDynamicFollowRect = new Rect(position.x, position.y + propertyH * 5, position.width / 2, propertyH);
//                var useDynamicLookAtRect = new Rect(position.x, position.y + propertyH * 6, position.width / 2, propertyH);

//                var FOVOffsetRect = new Rect(position.x, position.y + propertyH * 7, position.width / 2, propertyH);

//                var screenRotateOffsetRect = new Rect(position.x, position.y + propertyH * 8, position.width / 2, propertyH);

//                var screenHorizontalOffsetRect = new Rect(position.x, position.y + propertyH * 9, position.width / 2, propertyH);
//                var screenVerticalOffsetRect = new Rect(position.x, position.y + propertyH * 10, position.width / 2, propertyH);

//                var horizontalAngleOffsetRect = new Rect(position.x, position.y + propertyH * 11, position.width / 2, propertyH);

//                var pitchOffsetRect = new Rect(position.x, position.y + propertyH * 12, position.width / 2, propertyH);

//                var distanceOffsetRect = new Rect(position.x, position.y + propertyH * 13, position.width / 2, propertyH);

//                var cameraPositionOffsetRect = new Rect(position.x, position.y + propertyH * 14, position.width, propertyH);



//                EditorGUI.PropertyField(camRect, property.FindPropertyRelative("cam"), new GUIContent("镜头引用"));
//                EditorGUI.PropertyField(blendStyleRect, property.FindPropertyRelative("blendStyle"), new GUIContent("过渡曲线类型"));
//                EditorGUI.PropertyField(customBlendCurveRect, property.FindPropertyRelative("customBlendCurve"), new GUIContent("自定义曲线"));
//                EditorGUI.PropertyField(blendTimeRect, property.FindPropertyRelative("blendTime"), new GUIContent("过渡时长"));
//                EditorGUI.PropertyField(houldOnTimeRect, property.FindPropertyRelative("houldOnTime"), new GUIContent("保持时长"));
//                EditorGUI.PropertyField(useDynamicFollowRect, property.FindPropertyRelative("useDynamicFollow"), new GUIContent("启用动态跟随"));
//                EditorGUI.PropertyField(useDynamicLookAtRect, property.FindPropertyRelative("useDynamicLookAt"), new GUIContent("启用动态注视"));
//                EditorGUI.PropertyField(FOVOffsetRect, property.FindPropertyRelative("FOVOffset"), new GUIContent("FOV偏移"));
//                EditorGUI.PropertyField(screenRotateOffsetRect, property.FindPropertyRelative("screenRotateOffset"), new GUIContent("屏幕空间旋转"));
//                EditorGUI.PropertyField(screenHorizontalOffsetRect, property.FindPropertyRelative("screenHorizontalOffset"), new GUIContent("屏幕空间水平偏移"));
//                EditorGUI.PropertyField(screenVerticalOffsetRect, property.FindPropertyRelative("screenVerticalOffset"), new GUIContent("屏幕空间垂直偏移"));
//                EditorGUI.PropertyField(horizontalAngleOffsetRect, property.FindPropertyRelative("horizontalAngleOffset"), new GUIContent("水平角度旋转"));
//                EditorGUI.PropertyField(pitchOffsetRect, property.FindPropertyRelative("pitchOffset"), new GUIContent("俯仰偏移"));
//                EditorGUI.PropertyField(distanceOffsetRect, property.FindPropertyRelative("distanceOffset"), new GUIContent("距离偏移"));
//                EditorGUI.PropertyField(cameraPositionOffsetRect, property.FindPropertyRelative("cameraPositionOffset"), new GUIContent("相机位置平移"));
//            }
//        }
//    }
//}

//#endif
