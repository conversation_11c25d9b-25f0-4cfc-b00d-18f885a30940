#if UNITY_EDITOR
using UnityEditor;
using Slate.ActionClips;
using System.Collections.Generic;
using Sirenix.OdinInspector.Editor;
using UnityEngine;

namespace CEditor
{
    [CustomEditor(typeof(CAnimBodyTransClip))]
    public class CAnimBodyTransClipEditor : OdinEditor
    {
        void OnEnable()
        {
          
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            CAnimBodyTransClip clip = target as CAnimBodyTransClip;
            if (GUILayout.Button("match anim length") && clip != null)
            {
                clip.MatchAnimLength().Coroutine();
            }
        }

    }
}

#endif