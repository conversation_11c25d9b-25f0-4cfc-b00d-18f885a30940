using Cinemachine;
using Sirenix.OdinInspector;
using System;
using UnityEngine;
using Cysharp.Text;

namespace CEditor
{
    [ExecuteAlways]
    [Serializable]
    public class CameraControllerData
    {
        //public bool runtimeAutoSave;
        public float FOVOffset;
        public float screenRotateOffset;
        public float screenHorizontalOffset;
        public float screenVerticalOffset;
        public float circleAngleOffset;
        public float pitchOffset;
        public float distanceOffset;
        public CinemachineVirtualCamera.BlendHint blendHint;

        public Vector3 cameraPositionOffset;

        public NoiseSettings camShakeType;
        public float camShakeAmpli;
        public float camShakeFreq;

        public bool customLookAtTarget;
        public float customLookATargetDistance;
    }

    [ExecuteAlways]
    public class CameraController : MonoBehaviour
    {
        private CinemachineVirtualCamera VC;

        //[HideInInspector]
        //[SerializeField, Tooltip("自动保存运行时更改")]
        //public bool runtimeAutoSave = true;

        [SerializeField, LabelText("使用动画控制镜头")]
        public bool animBaseCam = false;

        [HideIf("@!animBaseCam", 1)]
        [SerializeField, LabelText("镜头动画")]
        public AnimationClip camAnimClip;

        [SerializeField, LabelText("自定义LookAt点位")]
        public bool customLookAtTarget = false;

        [HideIf("@!customLookAtTarget", 1)]
        [SerializeField, LabelText("自定义LookAt点位距离")]
        public float customLookATargetDistance = 1;

        [HideIf("@animBaseCam", 1)]
        [SerializeField, Tooltip("FOV偏移")]
        public float FOVOffset = 0;

        [HideIf("@animBaseCam", 1)]
        [SerializeField, Tooltip("屏幕空间旋转")]
        public float screenRotateOffset = 0;

        [HideIf("@animBaseCam", 1)]
        [SerializeField, Tooltip("屏幕空间水平偏移")]
        public float screenHorizontalOffset = 0;

        [HideIf("@animBaseCam", 1)]
        [SerializeField, Tooltip("屏幕空间垂直偏移")]
        public float screenVerticalOffset = 0;

        [HideIf("@animBaseCam", 1)]
        [SerializeField, Tooltip("环绕角度旋转")]
        public float circleAngleOffset = 0;

        [HideIf("@animBaseCam", 1)]
        [SerializeField, Tooltip("俯仰偏移")]
        public float pitchOffset = 0;

        [HideIf("@animBaseCam", 1)]
        [SerializeField, Tooltip("距离偏移")]
        public float distanceOffset = 0;

        [HideIf("@animBaseCam", 1)]
        [SerializeField, Tooltip("切镜过渡时长")]
        public float lensCutBlendTime = 0;

        [HideIf("@animBaseCam", 1)]
        [SerializeField, Tooltip("过渡路径类型")]
        public CinemachineVirtualCamera.BlendHint blendHint = CinemachineVirtualCameraBase.BlendHint.None;

        [HideIf("@animBaseCam", 1)]
        [SerializeField, Tooltip("镜头抖动预设")]
        public NoiseSettings camShakeType = null;

        [HideIf("@animBaseCam", 1)]
        [SerializeField, Tooltip("镜头抖动强度")]
        public float camShakeAmpli = 0.2f;

        [HideIf("@animBaseCam", 1)]
        [SerializeField, Tooltip("镜头抖动频率")]
        public float camShakeFreq = 0.5f;

        [SerializeField, Tooltip("相机位置平移")]
        public Vector3 cameraPositionOffset;

        private Animator animator;
        private SimpleAnimPlayer animationComponent;

        private CameraNameInfo cameraNameInfo = null;
        private bool initReady = false;
        private bool noFollowLookAt = false;

        private float finalFOV;

        private Vector3 defaultFollowOffset;
        private float defaultVerticalFOV = 0f;
        private float defaultScreenRotate = 0;
        private float defaultScreenHorizontal = 0;
        private float defaultScreenVertical = 0;

        private float prevFOVOffset = 0;
        private float prevScreenRotateOffset = 0;
        private float prevScreenHorizontalOffset = 0;
        private float prevScreenVerticalOffset = 0;
        private float prevCircleAngleOffset = 0;
        private float prevPitchOffset = 0;
        private float prevDistanceOffset = 0;
        private CinemachineVirtualCamera.BlendHint prevBlendHint;

        private float horizontalRadian;
        private Vector3 horizontalRotatedAndPitchedFollowOffset;
        private float horizontalRotatedAndPitchedFollowOffsetDistance;
        private Vector3 distancedFollowOffset;
        private Vector3 FOVedFollowOffset;

        Vector3 defaultNoFollowLookAtPosition;
        private Quaternion defaultNoFollowLookAtRotation;
        private Vector3 defaultNoFollowLookAtScale;

        string directory = "Assets/Datas/";
        // Start is called before the first frame update
        void Start()
        {
            //Init();
        }

        // Update is called once per frame
        void Update()
        {
            //if (prevFOVOffset != FOVOffset ||
            //    prevScreenRotateOffset != screenRotateOffset ||
            //    prevScreenHorizontalOffset != screenHorizontalOffset ||
            //    prevScreenVerticalOffset != screenVerticalOffset ||
            //    prevCircleAngleOffset != circleAngleOffset ||
            //    prevPitchOffset != pitchOffset ||
            //    prevDistanceOffset != distanceOffset ||
            //    prevBlendHint != blendHint)
            //{
            //    prevFOVOffset = FOVOffset;
            //    prevScreenRotateOffset = screenRotateOffset;
            //    prevScreenHorizontalOffset = screenHorizontalOffset;
            //    prevScreenVerticalOffset = screenVerticalOffset;
            //    prevCircleAngleOffset = circleAngleOffset;
            //    prevPitchOffset = pitchOffset;
            //    prevDistanceOffset = distanceOffset;
            //    prevBlendHint = blendHint;

            //    Refresh();

            //    //if (runtimeAutoSave)
            //    //    SaveCameraManagerDataByJSON();
            //}
        }

        public void Init()
        {
            if (animBaseCam)
                InitAnimationBase();
            else
                InitProgramBase();
        }

        public void Init(CameraNameInfo camNameInfo)
        {
            if (animBaseCam)
                InitAnimationBase();
            else
                InitProgramBase(camNameInfo);
        }

        private void InitProgramBase()
        {
            initReady = false;
            noFollowLookAt = false;
            UnLoadAnimationBase();
            VC = this.GetComponentInParent<CinemachineVirtualCamera>();
            if (VC != null)
            {
                string camName = this.GetComponentInParent<Transform>().name;
                cameraNameInfo = CCameraTools.AnalysisCameraInfoFromName(camName);
                InitLogic();
            }
        }

        private void InitProgramBase(CameraNameInfo camNameInfo)
        {
            if (camNameInfo == null)
            {
                InitProgramBase();
                return;
            }

            initReady = false;
            noFollowLookAt = false;
            UnLoadAnimationBase();
            VC = this.GetComponentInParent<CinemachineVirtualCamera>();
            if (VC != null)
            {
                cameraNameInfo = camNameInfo;
                InitLogic();
            }
        }

        private void InitLogic()
        {
            defaultVerticalFOV = 20;
            if (cameraNameInfo == null || cameraNameInfo.camType == CameraType.SingleDerivative)
                return;

            defaultVerticalFOV = cameraNameInfo.baseCamInfo.FOV;
            defaultScreenRotate = VC.m_Lens.Dutch;

            if (VC.Follow == null || VC.LookAt == null ||
                string.IsNullOrEmpty(cameraNameInfo.baseCamInfo.followHanging.baseObjName) ||
                cameraNameInfo.baseCamInfo.followHanging.baseObjName.ToLower() == "null" ||
                string.IsNullOrEmpty(cameraNameInfo.baseCamInfo.lookAtHanging.baseObjName) ||
                cameraNameInfo.baseCamInfo.lookAtHanging.baseObjName.ToLower() == "null" ||
                customLookAtTarget)
            {
                noFollowLookAt = true;
                customLookAtTarget = true;
            }

            if (noFollowLookAt)
            {
                defaultNoFollowLookAtPosition = VC.transform.localPosition;
                defaultNoFollowLookAtRotation = VC.transform.localRotation;
                defaultNoFollowLookAtScale = VC.transform.localScale;
            }
            else
            {
                defaultFollowOffset = VC.GetCinemachineComponent<CinemachineTransposer>().m_FollowOffset;
                defaultScreenHorizontal = VC.GetCinemachineComponent<CinemachineComposer>().m_ScreenX;
                defaultScreenVertical = VC.GetCinemachineComponent<CinemachineComposer>().m_ScreenY;
            }
            initReady = true;
        }

        private void InitAnimationBase()
        {
            VC = this.GetComponentInParent<CinemachineVirtualCamera>();
            if (VC != null &&  camAnimClip != null)
            {
                var transposer = VC.GetCinemachineComponent<CinemachineComposer>();
                var composer = VC.GetCinemachineComponent<CinemachineComposer>();
                animationComponent = this.gameObject.GetComponent<SimpleAnimPlayer>();
                animator = this.gameObject.GetComponent<Animator>();

#if UNITY_EDITOR
                if (animationComponent != null) DestroyImmediate(animationComponent);
                if (transposer != null) DestroyImmediate(transposer);
                if (composer != null) DestroyImmediate(composer);
                if (animator != null) DestroyImmediate(animator);
#else
                if (animationComponent != null) Destroy(animationComponent);
                if (transposer != null) Destroy(transposer);
                if (composer != null) Destroy(composer);
                if (animator != null) Destroy(animator);
#endif
                animator = this.gameObject.AddComponent<Animator>();
                animationComponent = this.gameObject.AddComponent<SimpleAnimPlayer>();
                animationComponent.clip = camAnimClip;
                animationComponent.PlayAnimSystem();
            }
        }

        public void Refresh()
        {
            if (animBaseCam)
                RefreshAnimationBase();
            else
                RefreshProgramBase();
        }

        private void RefreshProgramBase()
        {
            if (VC != null && initReady)
            {
                finalFOV = defaultVerticalFOV + FOVOffset;
                finalFOV = Mathf.Clamp(finalFOV, 0, 200);
                FOVOffset = finalFOV - defaultVerticalFOV;

                screenRotateOffset = Mathf.Clamp(screenRotateOffset, -180, 180);
                screenHorizontalOffset = Mathf.Clamp(screenHorizontalOffset, -1, 1);
                screenVerticalOffset = Mathf.Clamp(screenVerticalOffset, -1, 1);

                // FOV
                VC.m_Lens.FieldOfView = finalFOV;
                // Dutch
                VC.m_Lens.Dutch = defaultScreenRotate + screenRotateOffset;
                // BlendHint
                VC.m_Transitions.m_BlendHint = blendHint;
                
                if (camShakeType != null)
                {
                    var noise = VC.GetCinemachineComponent<CinemachineBasicMultiChannelPerlin>();
                    if (noise == null)
                        noise = VC.AddCinemachineComponent<CinemachineBasicMultiChannelPerlin>();

                    noise.m_NoiseProfile = camShakeType;
                    noise.m_PivotOffset = new Vector3(0.0f, 0.0f, 0.0f);
                    noise.m_AmplitudeGain = camShakeAmpli;
                    noise.m_FrequencyGain = camShakeFreq;
                }
                else
                {
                    var noise = VC.GetCinemachineComponent<CinemachineBasicMultiChannelPerlin>();
                    if (noise != null)
                    {
#if UNITY_EDITOR
                        DestroyImmediate(noise);
#else
                        Destroy(noise);
#endif
                    }
                }

                if (noFollowLookAt)
                {
                    Vector3 customLookAtDirc = defaultNoFollowLookAtRotation * Vector3.forward;
                    Vector3 offset = - customLookAtDirc * customLookATargetDistance;
                    offset = new Vector3(offset.z, offset.y, offset.x);

                    horizontalRadian = circleAngleOffset * (Mathf.PI / 180);
                    Vector3 horizontalRotatedAndPitchedOffset = new Vector3(offset.x * Mathf.Cos(horizontalRadian) + offset.z * Mathf.Sin(horizontalRadian), offset.y + pitchOffset, offset.z * Mathf.Cos(horizontalRadian) - offset.x * Mathf.Sin(horizontalRadian));
                    float horizontalRotatedAndPitchedOffsetDistance = horizontalRotatedAndPitchedOffset.magnitude;
                    Vector3 distancedOffset = ((horizontalRotatedAndPitchedOffsetDistance + distanceOffset + cameraPositionOffset.z) / horizontalRotatedAndPitchedOffsetDistance) * horizontalRotatedAndPitchedOffset;
                    Vector3 FOVedFollowOffset = distancedOffset * (Mathf.Tan((defaultVerticalFOV / 2) * (Mathf.PI / 180)) / Mathf.Tan((finalFOV / 2) * (Mathf.PI / 180)));
                    Vector3 newPosition = defaultNoFollowLookAtPosition + customLookAtDirc * customLookATargetDistance + new Vector3(FOVedFollowOffset.z, FOVedFollowOffset.y, FOVedFollowOffset.x);

                    VC.gameObject.transform.rotation = Quaternion.LookRotation(((defaultNoFollowLookAtPosition + customLookAtDirc * customLookATargetDistance) - newPosition).normalized, Vector3.up);

                    Vector3 newLookAtDirc = VC.gameObject.transform.localRotation * Vector3.forward;
                    Vector3 newUpDirc = VC.gameObject.transform.localRotation * Vector3.up;
                    Vector3 newRightDirc = Vector3.Cross(newLookAtDirc, newUpDirc);
                    
                    newPosition += (screenHorizontalOffset + cameraPositionOffset.x) * newRightDirc + (screenVerticalOffset + cameraPositionOffset.y) * newUpDirc;

                    VC.gameObject.transform.localPosition = newPosition;
                    VC.gameObject.transform.localScale = defaultNoFollowLookAtScale;
                }
                else
                {
                    if (VC.LookAt)
                    {
                        // 屏幕空间水平偏移
                        VC.GetCinemachineComponent<CinemachineComposer>().m_ScreenX = defaultScreenHorizontal + screenHorizontalOffset;
                        // 屏幕空间垂直偏移
                        VC.GetCinemachineComponent<CinemachineComposer>().m_ScreenY = defaultScreenVertical + screenVerticalOffset;
                    }
                    if (VC.Follow)
                    {
                        // 水平旋转、俯仰偏移、水平偏移、距离、FOV_Distance
                        horizontalRadian = circleAngleOffset * (Mathf.PI / 180);
                        horizontalRotatedAndPitchedFollowOffset = new Vector3(defaultFollowOffset.x * Mathf.Cos(horizontalRadian) + defaultFollowOffset.z * Mathf.Sin(horizontalRadian), defaultFollowOffset.y + pitchOffset, defaultFollowOffset.z * Mathf.Cos(horizontalRadian) - defaultFollowOffset.x * Mathf.Sin(horizontalRadian));
                        horizontalRotatedAndPitchedFollowOffsetDistance = horizontalRotatedAndPitchedFollowOffset.magnitude;
                        distancedFollowOffset = ((horizontalRotatedAndPitchedFollowOffsetDistance + distanceOffset) / horizontalRotatedAndPitchedFollowOffsetDistance) * horizontalRotatedAndPitchedFollowOffset;
                        FOVedFollowOffset = distancedFollowOffset * (Mathf.Tan((defaultVerticalFOV / 2) * (Mathf.PI / 180)) / Mathf.Tan((finalFOV / 2) * (Mathf.PI / 180)));
                        VC.GetCinemachineComponent<CinemachineTransposer>().m_FollowOffset = FOVedFollowOffset;
                    }
                }
            }
        }

        private void UnLoadAnimationBase()
        {
#if UNITY_EDITOR
            if (animationComponent != null) DestroyImmediate(animationComponent);
            if (animator != null) DestroyImmediate(animator);
#else
            if (animationComponent != null) Destroy(animationComponent);
            if (animator != null) Destroy(animator);
#endif
        }

        private void OnDestroy()
        {
            UnLoadAnimationBase();
        }

        private void RefreshAnimationBase()
        {
                
        }

        //void SaveCameraManagerDataByJSON()
        //{
        //    CameraControllerData data = new CameraControllerData();
        //    data.runtimeAutoSave = runtimeAutoSave;
        //    data.FOVOffset = FOVOffset;
        //    data.screenRotateOffset = screenRotateOffset;
        //    data.screenHorizontalOffset = screenHorizontalOffset;
        //    data.screenVerticalOffset = screenVerticalOffset;
        //    data.circleAngleOffset = circleAngleOffset;
        //    data.pitchOffset = pitchOffset;
        //    data.distanceOffset = distanceOffset;

        //    if (System.IO.Directory.Exists(directory) == false)
        //    {
        //        System.IO.Directory.CreateDirectory(directory);
        //    }
        //    string json = JsonUtility.ToJson(data);
        //    StreamWriter sw = new StreamWriter(directory + "CameraControllerParam" + this.GetInstanceID().ToString() + ".json");
        //    sw.Write(json);
        //    sw.Close();
        //}

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

