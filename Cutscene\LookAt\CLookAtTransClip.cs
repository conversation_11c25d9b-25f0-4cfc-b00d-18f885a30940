using Cinemachine;
using Sirenix.OdinInspector;
using Slate;
using Slate.ActionClips;
using System.Collections;
using UnityEngine;
using CEditor;
using static CEditor.LookAtDataCollector;
using System.Linq;
using FIMSpace.FEyes;
using UnityEngine.Serialization;
using Cysharp.Text;

[Name("看向配置")]
[Attachable(typeof(CLookAtTransTrack))]
[Category("C类编辑器")]
public class CLookAtTransClip : ActorActionClip
{
    [Header("Look Target")]
    [LabelText("看向目标类型")]
    public LookAtTargetType lookAtTargetType;

    [LabelText("ActorBody看向目标槽位"), ValueDropdown("TargetTagConfig")]
    public string TargetTag = "";

    private IEnumerable TargetTagConfig()
    {
        CLookAtTransTrack track = parent as CLookAtTransTrack;
        if (track != null && track.CamTemplateID != 0)
            return StationTemplateManager.GetTargetSlotsListV3(track.CamTemplateID).Select(x => x.targetSlotGroupTag).Where(x => x.Length == 1);
        else if (StationTemplateManager.instance != null && StationTemplateManager.instance.targetSlotCameraGroups != null && StationTemplateManager.instance.targetSlotCameraGroups.Count > 0)
            return StationTemplateManager.instance.GetTargetSlotsList().Where(x => x.Length == 1); ;
        return StationTemplateManager.GetTargetSlotsListV3().Select(x => x.targetSlotGroupTag).Where(x => x.Length == 1);
    }
    public string GetTargetTag()
    {
        if (writeBodyTargetManually && !string.IsNullOrEmpty(manuallyTargetTag))
            return manuallyTargetTag.ToUpper();
        if (TargetTag != null) return TargetTag;
        return null;
    }

    [LabelText("手动填写ActorBody看向目标槽位")]
    public bool writeBodyTargetManually = false;

    [LabelText("手动填写ActorBody看向目标槽位")]
    public string manuallyTargetTag = "";
    
    [LabelText("ActorBody看向角色肢体部位")]
    public LookAtBodyTarget bodyTarget;

    //[LabelText("GameObject看向目标")]
    //public GameObject lookAtTarget = null;

    [LabelText("GameObject看向目标Tag")]
    public string lookAtTargetTag = "";

    [Header("Body Look Parameter 头部看向参数")]
    [LabelText("看向强度")]
    [Tooltip("1为完全扭转朝向目标，0为不扭转保持原动画")]
    [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
    public float bodyLookAtStrenth = 0.8f;

    [LabelText("看向速度")]
    [Tooltip("当物体移动时，头部跟随目标速度")]
    [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
    public float bodyRotateSpeed = 0.2f;
    
    [LabelText("看向角度范围")]
    [Tooltip("当物体在角色身体正前方多少度以内才看向目标")]
    [SliderSuffix(0f, 180f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
    public float bodyLookRange = 65f;
    
    [Header("Eye Look Parameter 眼睛看向参数")]
    [LabelText("看向强度")]
    [Tooltip("0为保持原动画，1为扭转头部完全朝向目标")]
    [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
    public float eyesLookAtStrenth = 1f;

    [LabelText("看向速度")]
    [Tooltip("当物体移动时，眼睛跟随目标速度")]
    [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
    public float eyesRotateSpeed = .7f;

    [LabelText("看向角度范围")]
    [Tooltip("当物体在角色头部正前方多少度以内才看向目标")]
    [SliderSuffix(0f, 180f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
    public float eyesLookRange = 18f;
    
    [Header("Eye Random Parameter 眼动参数")]
    [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
    [LabelText("闪动强度")]
    [Tooltip("闪动功能开启量")]
    public float eyesRandomMoveAmount = 1f;

    [LabelText("闪动频率")]
    [Tooltip("触发眼部闪动的频率")]
    [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
    public float eyesRandomMoveSpeed = 0.7f;

    [LabelText("闪动范围(纵向，横向)")]
    [Tooltip("眼部闪动在上下方向与左右方向的最大幅度")]
    public Vector2 eyesRandomScaleVH = new Vector2(0.5f, 0.5f);
    
    [LabelText("闪动情绪类型")]
    [Tooltip("不明确时选择default")]
    public FEyesAnimator.FERandomMovementType eyesRandomMoveType = FEyesAnimator.FERandomMovementType.Default; 

    private GameObject performer;
    private LookAtManager aimSystem;

    public override string info
    {
        get
        {
            switch (lookAtTargetType)
            {
                case LookAtTargetType.None:
                    return "None";
                case LookAtTargetType.ActorBody:
                    return ZString.Concat("Actor ", GetTargetTag(), ": ", bodyTarget.ToString());
                case LookAtTargetType.Camera:
                    return "Camera";
                case LookAtTargetType.GameObject:
                    if (string.IsNullOrEmpty(lookAtTargetTag))
                        return "GameObjectTag: Null";
                    else
                        return ZString.Concat("GameObject: ", lookAtTargetTag);
                    //if (lookAtTarget == null)
                    //    return "GameObject: Null";
                    //else
                    //    return "GameObject: " + lookAtTarget.name;
            }
            return "看向配置";
        }
    }

    public override bool isValid
    {
        get => true;
    }

    //Called in forward sampling when the clip is entered
    protected override void OnEnter()
    {
        performer = GetActor();
        aimSystem = performer.GetComponent<LookAtManager>();
        if (aimSystem == null) { return; }

        EyeBlinkManager blinkManager = performer.GetComponent<EyeBlinkManager>();
        if (blinkManager != null)
            blinkManager.BlinkOnce(EyeOverwriteType.CompletelyClose);

        Transform targetTrans = null;

        switch (lookAtTargetType)
        {
            case LookAtTargetType.None:
                break;
            case LookAtTargetType.ActorBody:
                if (StationTemplateManager.instance != null && !string.IsNullOrEmpty(GetTargetTag()))
                {
                    ActorSlotInfo slotInfo = StationTemplateManager.instance.actorSlots.Find(slot => ReferenceEquals(slot.actor, performer));
                    if (slotInfo != null)
                    {
                        int index = StationTemplateManager.instance.actorSlots.IndexOf(slotInfo);
                        int targetIndex = StationTemplateToolsKit.GetSlotIndexByStringName(GetTargetTag());
                        if (targetIndex != index || (targetIndex == index && bodyTarget != LookAtBodyTarget.Head))
                        {
                            targetTrans = StationTemplateManager.instance.GetLookAtBodyTarget(targetIndex, bodyTarget);
                        }
                    }
                }
                break;
            case LookAtTargetType.Camera:
                if (StationTemplateManager.instance != null && StationTemplateManager.instance.GetCinemachineBrain() != null)
                {
                    targetTrans = StationTemplateManager.instance.GetCinemachineBrain().transform;
                }
                break;
            case LookAtTargetType.GameObject:
                if (!string.IsNullOrEmpty(lookAtTargetTag))
                {
                    CEditorLookAtGameObjectMessage[] targetsByTag = GameObject.FindObjectsOfType<CEditorLookAtGameObjectMessage>();
                    //GameObject targetsByName = GameObject.Find(lookAtTargetTag);
                    foreach (CEditorLookAtGameObjectMessage o in targetsByTag)
                    {
                        if (o.gameObject != null && !string.IsNullOrEmpty(o.CEditorLookAtTag) && o.CEditorLookAtTag == lookAtTargetTag)
                        {
                            targetTrans = o.gameObject.transform;
                            break;
                        }
                    }
                }
                else targetTrans = null;
                //if (lookAtTarget != null)
                //    targetTrans = lookAtTarget.transform;
                //else targetTrans = null;
                break;
            default:
                targetTrans = null;
                break;
        }
        Debug.Log("ClookatransClip set once!");

        LookAtParams lookparam = new LookAtParams
        {
            s_lookAtTarget = targetTrans,
            s_bodyLookAtStrenth = bodyLookAtStrenth,
            s_bodyRotateSpeed = bodyRotateSpeed,
            s_eyesLookAtStrenth =  eyesLookAtStrenth,
            s_eyesRotateSpeed = eyesRotateSpeed,
            s_eyesRandomMoveAmount = eyesRandomMoveAmount,
            s_eyesRandomMoveSpeed = eyesRandomMoveSpeed,
            s_eyesRandomScaleVH = eyesRandomScaleVH,
            s_eyesRandomMoveType = eyesRandomMoveType,
            s_bodyLookRange = bodyLookRange,
            s_eyesLookRange = eyesLookRange
            
        };

        aimSystem.UpdateParams(lookparam);
    }

    //Called per frame while the clip is updating. Time is the local time within the clip.
    //So a time of 0 means the start of the clip.
    protected override void OnUpdate(float time, float previousTime)
    {

    }

    //Called in forwards sampling when the clip exits
    protected override void OnExit() { }

    //Called in backwards sampling when the clip is entered.
    protected override void OnReverseEnter() { }

    //Called in backwards sampling when the clip exits.
    protected override void OnReverse() { }
}
