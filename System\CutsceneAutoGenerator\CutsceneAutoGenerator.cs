using cfg.story;
using Slate;
using Slate.ActionClips;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using VGame.Framework;

namespace CEditor
{
    public class CutsceneAutoGenerator
    {
        public static void AutoGenerator(CutsceneGroup group, int camTemplateID, AutoGenerateDialogue cfg)
        {
            Debug.Log("------------------------------------");
            Debug.Log(cfg.EmoDetail);
            int emoID = GetEmoID(cfg);
            Debug.Log(emoID);
            CAnimEmoTransTrack emoTrack = group.GetOrAddTrack<CAnimEmoTransTrack>();
            SetEmoClip(emoTrack, emoID);

            CSitOffsetIKTransTrack sitOffsetIKTransTrack = group.GetOrAddTrack<CSitOffsetIKTransTrack>();
            CLeftHandheldTrack leftHandheldTrack = group.GetOrAddTrack<CLeftHandheldTrack>();
            CRightHandheldTrack rightHandheldTrack = group.GetOrAddTrack<CRightHandheldTrack>();
            CBlinkTrack blinkTrack = group.GetOrAddTrack<CBlinkTrack>();
            CCloseTrack closeTrack = group.GetOrAddTrack<CCloseTrack>();

            CProgrammaticMouthTrack programMouthTrack = group.GetOrAddTrack<CProgrammaticMouthTrack>();
            CAnimMouthTransTrack mouthTrack = group.GetOrAddTrack<CAnimMouthTransTrack>();
            CAnimEyeAndBrowTransTrack eyesTrack = group.GetOrAddTrack<CAnimEyeAndBrowTransTrack>();
            SetEmotionalFaseClip(eyesTrack, mouthTrack, emoID, "Com", 0);

            CAnimBodyTransTrack bodyTrack = group.GetOrAddTrack<CAnimBodyTransTrack>();
            SetEmotionalBodyClip(bodyTrack, emoID, "Com", 0);

            CLookAtTransTrack lookAtTrack = group.GetOrAddTrack<CLookAtTransTrack>();

            lookAtTrack.AddAction<CLookAtTransClip>(0);

            if (camTemplateID > 0)
                lookAtTrack.CamTemplateID = camTemplateID;
        }

        private static int GetEmoID(AutoGenerateDialogue cfg)
        {
            var emoList = CfgManager.tables.TbCEditorSentiment.DataList;

            if (emoList != null && emoList.Count > 0)
            {
                if (string.IsNullOrEmpty(cfg.EmoDetail))
                {
                    var defaultItem = emoList.Find(x => x.Sentiment == "Common");
                    if (defaultItem != null)
                    {
                        return defaultItem.ID;
                    }
                }
                else
                {
                    if (emoList != null)
                    {
                        var item = emoList.Find(x => x.Sentiment == cfg.EmoDetail);
                        if (item != null)
                        {
                            return item.ID;
                        }
                    }
                }
                return emoList[0].ID;
            }
            return 1000;
        }

        private static CAnimEmoTransClip SetEmoClip(CAnimEmoTransTrack track, int emoID)
        {
            CAnimEmoTransClip clip = track.AddAction<CAnimEmoTransClip>(0);
            clip.GetCfg().emo = emoID;
            return clip;
        }

        private static CAnimBodyTransClip SetEmotionalBodyClip(CAnimBodyTransTrack track, int emoID, string Personality, float time = 0)
        {
            CAnimBodyTransClip clip = track.AddAction<CAnimBodyTransClip>(time);
            var bodyEmoList = CfgManager.tables.TbCEditorBodyEmote.DataList;
            var bodyAnimList = CfgManager.tables.TbCEditorBodyAssets.DataList;
            List<string> validBodyEmoList = null;
            List<CEditorBodyAssetsTable> validBodyAnimList = null;
            if (bodyEmoList != null && bodyEmoList.Count > 0)
            {
                validBodyEmoList = bodyEmoList.Where(x => x.Sentiment == CfgManager.tables.TbCEditorSentiment.GetOrDefault(emoID).Sentiment).Select(x => x.Emote).ToList();
                if (validBodyEmoList.Count == 0)
                    validBodyEmoList.Add(CfgManager.tables.TbCEditorBodyEmote.DataList[0].Emote);
                if (string.IsNullOrEmpty(Personality))
                    Personality = "Com";
                validBodyAnimList = bodyAnimList.Where(x => validBodyEmoList.Intersect(x.Emotion.Split("#")).ToList().Count > 0)
                    .Where(x => x.Personality == Personality)
                    .Where(x => x.IsReady)
                    .ToList();
                if (validBodyAnimList.Count == 0 && bodyAnimList.Count > 0)
                    validBodyAnimList.Add(bodyAnimList[0]);
            }
            if (validBodyAnimList != null && validBodyAnimList.Count > 0)
                clip.GetCfg().animClipKey = validBodyAnimList.Random().ID;

            var breathList = CfgManager.tables.TbCEditorBreathAssets.DataList;
            if (breathList != null && breathList.Count > 0)
                clip.GetCfg().breathAnimClipKey = breathList[0].ID;

            return clip;
        }

        private static void SetEmotionalFaseClip(CAnimEyeAndBrowTransTrack eyeAndBrowTrack, CAnimMouthTransTrack mouthTrack, int emoID, string Personality, float time)
        {
            CAnimEyeAndBrowTransClip eyeAndBrowClip = eyeAndBrowTrack.AddAction<CAnimEyeAndBrowTransClip>(time);
            CAnimMouthTransClip mouthClip = mouthTrack.AddAction<CAnimMouthTransClip>(time);

            var faceEmoList = CfgManager.tables.TbCEditorFaceEmote.DataList;
            var faceAnimList = CfgManager.tables.TbCEditorFaceCombinAssetsTable.DataList;
            List<string> validFaceEmoList = null;
            List<CEditorFaceCombinAssetsTable> validFaceAnimList = null;
            if (faceEmoList != null && faceEmoList.Count > 0)
            {
                validFaceEmoList = faceEmoList.Where(x => x.Sentiment == CfgManager.tables.TbCEditorSentiment.GetOrDefault(emoID).Sentiment).Select(x => x.Emote).ToList();
                if (validFaceEmoList.Count == 0)
                    validFaceEmoList.Add(CfgManager.tables.TbCEditorFaceEmote.DataList[0].Emote);
                if (string.IsNullOrEmpty(Personality))
                    Personality = "Com";
                validFaceAnimList = faceAnimList.Where(x => validFaceEmoList.Intersect(x.Emotion.Split("#")).ToList().Count > 0)
                    .Where(x => x.Personality == Personality)
                    .Where(x => CfgManager.tables.TbCEditorEyeAndBrowAssets.GetOrDefault(x.EyesAndBrowID).IsReady && CfgManager.tables.TbCEditorMouthAssets.GetOrDefault(x.MouthIdleID).IsReady)
                    .ToList();
                if (validFaceAnimList.Count == 0 && faceAnimList.Count > 0)
                    validFaceAnimList.Add(faceAnimList[0]);
            }
            if (validFaceAnimList != null && validFaceAnimList.Count > 0)
            {
                CEditorFaceCombinAssetsTable faceAnim = validFaceAnimList.Random();
                eyeAndBrowClip.GetCfg().animClipKey = faceAnim.EyesAndBrowID;
                mouthClip.GetCfg().animClipKey = faceAnim.MouthIdleID;
            }
            return;
        }
    }
}