using UnityEngine;

namespace Slate
{
    [Name("布料控制")]
    [Attachable(typeof(ActorGroup))]
    [Category("C类编辑器")]

    public class CClothControlTrack : ActionTrack
    {
        protected override void OnCreate()
        {
            base.OnCreate();
            this.name = "布料控制";
        }

        protected override void OnSceneGUI()
        {
            base.OnSceneGUI();
            this.name = "布料控制";
        }

        protected override void OnEnter()
        {
            base.OnEnter();
        }
#if UNITY_EDITOR
        public override Texture icon
        {
            get
            {
                _icon = Resources.Load("PlayIcon") as Texture;
                return _icon as Texture;
            }
        }
#endif
        public override string info
        {
            get
            {
                return "cloth track info";
            }
        }
    }
}