﻿#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;

namespace Slate.GUIEditor
{
    [CustomPropertyDrawer(typeof(SliderSuffixAttribute))]
    public class SliderDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            SliderSuffixAttribute suffixAttribute = (SliderSuffixAttribute)attribute;

            // Debug log to check if the drawer is being called
            // Debug.Log($"Drawer called for property: {property.name}, type: {property.propertyType}");

            if (property.propertyType == SerializedPropertyType.Float)
            {
                DrawSliderWithSuffix(position, property, suffixAttribute, label);
            }
            else
            {
                EditorGUI.PropertyField(position, property, label);
            }
        }

        private void DrawSliderWithSuffix(Rect position, SerializedProperty property,
            SliderSuffixAttribute suffixAttribute, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            // Draw the label
            position = EditorGUI.PrefixLabel(position, GUIUtility.GetControlID(FocusType.Passive), label);

            // Calculate the width for the suffix
            float suffixWidth = 20f;
            float sliderWidth = position.width - suffixWidth - 5f;

            // Draw the slider
            Rect sliderRect = position;
            sliderRect.width = sliderWidth;
            float value = EditorGUI.Slider(sliderRect, property.floatValue, suffixAttribute.MinValue,
                suffixAttribute.MaxValue);

            // Draw the suffix
            if (!string.IsNullOrEmpty(suffixAttribute.Suffix))
            {
                Rect suffixRect = position;
                suffixRect.x += sliderWidth + 5f;
                suffixRect.width = suffixWidth;
                EditorGUI.LabelField(suffixRect, suffixAttribute.Suffix);
            }

            property.floatValue = value;
            EditorGUI.EndProperty();
        }
    }
}

#endif