using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace CEditor
{
    public static class CAssetsManagerToolKit
    {
        public static bool Convert01StringToBool(string str, bool defaultValue)
        {
            if (str == "0")
                return false;
            else if (str == "1")
                return true;
            else
                return defaultValue;
        }

        public static int ConvertNumStringToInt(string str, int DefaultValue)
        {
            return string.IsNullOrEmpty(str) ? DefaultValue : ((str == "null" || str == "Null" || str == "NULL") ? DefaultValue : Convert.ToInt32(str));
        }

        public static float ConvertNumStringToFloat(string str, float DefaultValue)
        {
            return string.IsNullOrEmpty(str) ? DefaultValue : ((str == "null" || str == "Null" || str == "NULL") ? DefaultValue : Convert.ToSingle(str));
        }

        public static BodyPosture StringToBodyPosture(string tag, bool hasPostureChange)
        {
            if (hasPostureChange)
                return BodyPosture.StandUpOrSetDown;
            switch (tag)
            {
                case "0":
                    return BodyPosture.Stand;
                case "1":
                    return BodyPosture.Set;
                case "2":
                    return BodyPosture.Others;
                default: return BodyPosture.Others;
            }
        }
    }
}

