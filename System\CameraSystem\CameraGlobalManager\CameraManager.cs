using UnityEngine;

namespace CEditor
{
    [ExecuteAlways]
    public class CameraManager : MonoBehaviour
    {
        public CameraSwitchSystem cameraSwitchSystem { get; set; }

        // Start is called before the first frame update
        void Start()
        {
            cameraSwitchSystem = this.gameObject.GetComponent<CameraSwitchSystem>();
        }

        // Update is called once per frame
        void Update()
        {

        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

