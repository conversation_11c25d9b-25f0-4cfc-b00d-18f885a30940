
using UnityEngine;

using UnityEngine.Playables;

using UnityEngine.Animations;

namespace CEditor
{
    [RequireComponent(typeof(Animator))]
    [ExecuteInEditMode]
    public class PlayAnimInEditorMode : MonoBehaviour

    {

        public AnimationClip clip;

        private AnimationClip prevClip;
        private bool first = true;

        private PlayableGraph playableGraph;

        private AnimationPlayableOutput playableOutput;

        private AnimationClipPlayable clipPlayable;

        private AnimationLayerMixerPlayable layerPlayable;

        void Start()

        {

        }

        private void Update()
        {
            if (clip != prevClip && clip != null && playableGraph.IsValid())
            {
                clipPlayable = AnimationClipPlayable.Create(playableGraph, clip);

                playableGraph.Disconnect(layerPlayable, 0);

                playableGraph.Connect(clipPlayable, 0, layerPlayable, 0);


                prevClip = clip;
            }
            if (first)
            {
                playableGraph = PlayableGraph.Create();
                playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);

                playableOutput = AnimationPlayableOutput.Create(playableGraph, "Animation", GetComponent<Animator>());

                layerPlayable = AnimationLayerMixerPlayable.Create(playableGraph, 1);
                layerPlayable.SetInputWeight(0, 1);

                clipPlayable = AnimationClipPlayable.Create(playableGraph, clip);

                playableGraph.Connect(clipPlayable, 0, layerPlayable, 0);

                playableOutput.SetSourcePlayable(layerPlayable);

                playableGraph.Play();

                prevClip = null;

                first = false;
            }

        }

        void OnDisable()

        {

            //销毁该图创建的所有可播放项和 PlayableOutput。

            playableGraph.Destroy();

        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
        // Ensure continuous Update calls.
        if (!Application.isPlaying)
        {
            UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
            UnityEditor.SceneView.RepaintAll();
        }
#endif
        }

    }
}
