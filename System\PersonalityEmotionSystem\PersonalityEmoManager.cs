using cfg.common;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace CEditor
{


    [ExecuteAlways]
    public class PersonalityEmoManager : MonoBehaviour
    {
        //public PersonalityEmoteCollector dataCollector;

        // Start is called before the first frame update
        void Start()
        {
            //dataCollector = this.gameObject.GetComponent<PersonalityEmoteCollector>();
            //if (dataCollector == null)
            //    dataCollector = this.gameObject.AddComponent<PersonalityEmoteCollector>();
            //dataCollector.LoadBodyEmoteData();
            //dataCollector.LoadPersonalityData();
        }

        // Update is called once per frame
        void Update()
        {
            
        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

