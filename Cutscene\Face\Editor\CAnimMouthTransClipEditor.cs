#if UNITY_EDITOR
using UnityEditor;
using Slate.ActionClips;
using CEditor;

// [CustomEditor(typeof(CAnimMouthTransClip))]
public class CAnimMouthTransClipEditor : Editor
{
    private SerializedObject obj; //序列化

    private SerializedProperty mouthClip, mouthWeight;
    private SerializedProperty mouthPlayingMode, mouthStartTime, mouthEndTime, mouthBlendType, mouthBlendInDuration, mouthStaticBlendType, mouthStaticBlendInDuration;

    void OnEnable()
    {
        obj = new SerializedObject(target);

        mouthClip = obj.FindProperty("mouthClip");
        mouthWeight = obj.FindProperty("mouthWeight");
        mouthPlayingMode = obj.FindProperty("mouthPlayingMode");
        mouthStartTime = obj.FindProperty("mouthStartTime");
        mouthEndTime = obj.FindProperty("mouthEndTime");
        mouthBlendType = obj.FindProperty("mouthBlendType");
        mouthBlendInDuration = obj.FindProperty("mouthBlendInDuration");
        mouthStaticBlendType = obj.FindProperty("mouthStaticBlendType");
        mouthStaticBlendInDuration = obj.FindProperty("mouthStaticBlendInDuration");
    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        obj.Update();

        EditorGUILayout.PropertyField(mouthClip);
        EditorGUILayout.PropertyField(mouthWeight);

        EditorGUILayout.Space(10);

        EditorGUILayout.PropertyField(mouthBlendType);
        EditorGUILayout.PropertyField(mouthBlendInDuration);

        EditorGUILayout.PropertyField(mouthPlayingMode);

        if (mouthPlayingMode.enumValueIndex == ((int)PlayingMode.EndStop) || mouthPlayingMode.enumValueIndex == ((int)PlayingMode.BlendLoop)/* || mouthPlayingMode.enumValueIndex == ((int)PlayingMode.LoopThenStop)*/)
        {
            EditorGUILayout.PropertyField(mouthStartTime);
            EditorGUILayout.PropertyField(mouthEndTime);

            EditorGUILayout.PropertyField(mouthStaticBlendType);
            EditorGUILayout.PropertyField(mouthStaticBlendInDuration);
        }

        obj.ApplyModifiedProperties();
    }
}
#endif