//using CEditor;
//using Slate.ActionClips;
//using Slate;
//using System.Collections;
//using System.Collections.Generic;
//using UnityEngine;

//public static class StationTemplateManagerUtils
//{


//    // 初始化
//    public void Init()
//    {
//        stm = this.gameObject.GetComponent<StationTemplateManager>();
//        stm.Init();
//    }
//    public void Init(StationTemplateManager newStm)
//    {
//        stm = newStm;
//        stm.Init();
//    }
//    public void Init(Camera camera)
//    {
//        stm = this.gameObject.GetComponent<StationTemplateManager>();
//        stm.Init(camera);
//    }
//    public void Init(StationTemplateManager newStm, Camera camera)
//    {
//        stm = newStm;
//        stm.Init(camera);
//    }

//    // Cutscene相关
//    private ActorGroup CreatCutsceneGroup(Cutscene cutscene, GameObject gameObject)
//    {
//        ActorGroup actorGroup = cutscene.AddGroup<ActorGroup>(gameObject);
//        CleanUpCutsceneGroupTracks(actorGroup);
//        return actorGroup;
//    }

//    //private void CreatGroupTracks(AssetsContext context, CutsceneGroup group)
//    //{
//    //    if (group == null)
//    //        return;
//    //    switch (context.assetsType)
//    //    {
//    //        case AssetsType.Actor:
//    //            CAnimEmoTransTrack emoTrack = group.AddTrack<CAnimEmoTransTrack>();
//    //            emoTrack.AddAction<CAnimEmoTransClip>(0);
//    //            CAnimMouthTransTrack mouthTrack = group.AddTrack<CAnimMouthTransTrack>();
//    //            mouthTrack.AddAction<CAnimMouthTransClip>(0);
//    //            CAnimEyeAndBrowTransTrack eyesTrack = group.AddTrack<CAnimEyeAndBrowTransTrack>();
//    //            eyesTrack.AddAction<CAnimEyeAndBrowTransClip>(0);
//    //            CAnimBodyTransTrack bodyTrack = group.AddTrack<CAnimBodyTransTrack>();
//    //            bodyTrack.AddAction<CAnimBodyTransClip>(0);
//    //            break;
//    //        case AssetsType.StationTemplate:
//    //            CCameraTransTrack cameraTrack = group.AddTrack<CCameraTransTrack>();
//    //            cameraTrack.AddAction<CSingleCameraTransClip>(0);
//    //            break;
//    //        default: break;
//    //    }
//    //}

//    private void RemoveCutsceneGroup(Cutscene cutscene, CutsceneGroup group)
//    {
//        if (cutscene != null && group != null)
//            cutscene.DeleteGroup(group);
//    }

//    private void CleanUpCutsceneGroupTracks(CutsceneGroup group)
//    {
//        if (group.tracks.Count > 0)
//            for (int i = 0; i < group.tracks.Count; i++)
//                group.DeleteTrack(group.tracks[i]);
//    }

//    // 对外manager操作接口
//    public void SetCharacterSlots(List<GameObject> objs)
//    {

//    }

//    //生成动画轨道
//    static void GenerateActorTracks(CutsceneGroup group);
//        //生成镜头轨道
//        static void GenerateCameraTracks(Cutscene cutscene);



//}
