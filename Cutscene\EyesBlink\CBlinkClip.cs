using System.Collections;
using System.Linq;
using CEditor;
using Sirenix.OdinInspector;
using UnityEngine;
using VGame.Framework;

namespace Slate.ActionClips
{
    [System.Serializable]
    public class CBlinkClipParams
    {
        [SerializeField, LabelText("半闭眼概率")]
        public float halfBlinkProportion = 0.5f;

        [SerializeField, LabelText("眨眼频率")]
        public float blinkFrequency = 1.0f;

        [SerializeField, LabelText("眨眼频率随机强度")]
        public float blinkFrequencyRandomStrenght = 0.3f;

        [SerializeField, LabelText("眨眼速度")]
        public float blinkSpeed = 0.8f;

        [SerializeField, LabelText("眨眼速度随机强度")]
        public float blinkSpeedRandomStrenght = 0.1f;

        [SerializeField, LabelText("闭眼持续时长")]
        public float blinkHoldTime = 0.0f;

        [SerializeField, LabelText("闭眼持续时长随机强度")]
        public float blinkHoldTimeRandomStrenght = 0.1f;
    }

    [Name("眨眼配置")]
    [Attachable(typeof(CBlinkTrack))]
    [Category("C类编辑器")]
    public class CBlinkClip : BaseDialogueActorActionClip<CBlinkClipParams>
    {
        private GameObject performer;
        private EyeBlinkManager blinkManager;

        [SerializeField, HideInInspector]
        private float _length = 1;

        public override float length
        {
            get { return _length; }
            set { _length = value; }
        }

        public override string info
        {
            get
            {
                return "眨眼片段";
            }
        }

        protected override void OnCreate()
        {
            base.OnCreate();
        }

        private void SetEnter()
        {
            performer = GetActor();
            blinkManager = performer.GetComponent<EyeBlinkManager>();

            if (performer != null && blinkManager != null)
            {
                blinkManager.halfBlinkProportion = GetCfg().halfBlinkProportion;
                blinkManager.blinkFrequency = GetCfg().blinkFrequency;
                blinkManager.blinkFrequencyRandomStrenght = GetCfg().blinkFrequencyRandomStrenght;
                blinkManager.blinkSpeed = GetCfg().blinkSpeed;
                blinkManager.blinkSpeedRandomStrenght = GetCfg().blinkSpeedRandomStrenght;
                blinkManager.blinkHoldTime = GetCfg().blinkHoldTime;
                blinkManager.blinkHoldTimeRandomStrenght = GetCfg().blinkHoldTimeRandomStrenght;
            }
        }

        //Called in forward sampling when the clip is entered
        protected override void OnEnter()
        {
            base.OnEnter();
            SetEnter();
        }

        //Called in backwards sampling when the clip is entered.
        protected override void OnReverseEnter()
        {
            base.OnReverseEnter();
            SetEnter();
        }

        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime)
        {
            if (performer != null && blinkManager != null)
            {
                blinkManager.useKeepBlinkControl = true;
                blinkManager.keepBlinkingTrigger = true;
            }
        }

        //Called in forwards sampling when the clip exits
        protected override void OnExit() 
        {

        }

        

        //Called in backwards sampling when the clip exits.
        protected override void OnReverse() { }
    }
}