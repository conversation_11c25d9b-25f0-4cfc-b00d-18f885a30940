﻿#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEngine;

namespace CEditor
{
    public class StationTransporterManager : MonoBehaviour
    {
        [Head<PERSON>("传递对象")]
        [Tooltip("Actors transform to be Transported, From Right to Left")]
        public List<Transform> Actors;
        
        [Toolt<PERSON>("Camera Info to be Transported")]
        public List<Transform> Cameras;

        [HideInInspector]
        [Tooltip("保存路径")]
        public string SavePath = "Assets/StationTransporterPrefabs";
        
        [HideInInspector]
        [Tooltip("保存文件名")]
        public string SaveFileName = "StationTransporter";
    }
}

#endif