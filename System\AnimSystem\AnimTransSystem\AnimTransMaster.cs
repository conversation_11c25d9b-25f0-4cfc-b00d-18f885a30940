using Animancer;
using System;
using UnityEngine;
using UnityEngine.Playables;
using Cysharp.Text;
using System.Diagnostics;
using System.Collections;

namespace CEditor
{
    [ExecuteAlways]
    public struct HandRelativeArmPosition
    {
        public bool up;
        public bool front;
        public bool inner;
        public bool cover;
        public bool far;
    }

    public enum MasterMode
    {
        AnimTrans,
        Debug,
        AutoTagCalculate
    }

    [ExecuteAlways]
    public partial class AnimTransMaster : MonoBehaviour
    {
        // Playable System引用
        public AnimTransPlayableSystem animTransPlayableSystem;

        private Animator animator;

        // Master模式选择：动画混合模式、debug模式、tag自动化标注模式
        [HideInInspector]
        [SerializeField, Tooltip("编辑器动作模式")]
        public MasterMode currentMode = MasterMode.AnimTrans;

        //////////////////////////////////////////////////////////
        ///////////////////////轨道参数配置///////////////////////
        //////////////////////////////////////////////////////////

        /////////////////肢体轨道参数配置/////////////////
        // 动画资产
        public AnimationClip currentAnimClip { get; set; }
        // 资产形式
        public PlayingMode playingMode { get; set; }
        // 开始结束时间
        public float animStartTime { get; set; }
        public float animEndTime { get; set; }
        // blend模式
        public BlendType blendInType { get; set; }
        public float blendInDuration { get; set; }
        public BlendType staticBlendInType { get; set; }
        public float staticBlendInDuration { get; set; }
        // 呼吸叠加
        public AnimationClip breathClip { get; set; }
        public AvatarMask breathAvatarMask { get; set; }
        public float breathStrength { get; set; }
        // 播放速率
        public float animPlayRate { get; set; }
        // 镜像
        public bool mirrorAnim { get; set; }
        // 局部手部覆写
        public bool overWriteLocalRightHand { get; set; }
        public bool overWriteLocalLeftHand { get; set; }
        public AnimationClip overWriteLocalRightHandClip { get; set; }
        public AnimationClip overWriteLocalLeftHandClip { get; set; }

        // 自动化防穿模Tag
        public Vector3 rightHandCurrectPosition { get; set; }
        public Vector3 leftHandCurrectPosition { get; set; }
        public Vector3 rightElbowCurrectPosition { get; set; }
        public Vector3 leftElbowCurrectPosition { get; set; }

        /////////////////面部轨道参数配置/////////////////
        public AnimationClip currentFaceEyebrowAnimClip { get; set; }
        public AnimationClip currentFaceEyesAnimClip { get; set; }
        public AnimationClip currentFaceMouthAnimClip { get; set; }

        public float currentFaceEyebrowWeight { get; set; }
        public float currentFaceEyesWeight { get; set; }
        public float currentFaceMouthWeight { get; set; }

        public PlayingMode eyebrowPlayingMode { get; set; }
        public PlayingMode eyesPlayingMode { get; set; }
        public PlayingMode mouthPlayingMode { get; set; }

        public float eyebrowStartTime { get; set; }
        public float eyebrowEndTime { get; set; }
        public float eyesStartTime { get; set; }
        public float eyesEndTime { get; set; }
        public float mouthStartTime { get; set; }
        public float mouthEndTime { get; set; }

        public BlendType faceEyebrowBlendType { get; set; }
        public float faceEyebrowBlendInDuration { get; set; }
        public BlendType faceEyesBlendType { get; set; }
        public float faceEyesBlendInDuration { get; set; }
        public BlendType faceMouthBlendType { get; set; }
        public float faceMouthBlendInDuration { get; set; }

        public BlendType faceEyebrowStaticBlendType { get; set; }
        public float faceEyebrowStaticBlendInDuration { get; set; }
        public BlendType faceEyesStaticBlendType { get; set; }
        public float faceEyesStaticBlendInDuration { get; set; }
        public BlendType faceMouthStaticBlendType { get; set; }
        public float faceMouthStaticBlendInDuration { get; set; }

        public bool useEyesOverwriteAnim { get; set; }
        public AnimationClip eyesOverwriteAnim { get; set; }
        public float eyesOverwriteAnimFrameTime { get; set; } = 0f;
        public float eyesOverwriteAnimBlendInTime { get; set; } = 0.15f;
        public float eyesOverwriteAnimBlendOutTime { get; set; } = 0.3f;

        /////////////////情绪轨道参数配置/////////////////
        public int currentEmo { get; set; }

        //////////////////////////////////////////////////////////
        /////////////////动画混合模式宏观参数配置/////////////////
        //////////////////////////////////////////////////////////
        // 自动化防穿模型
        [HideInInspector]
        [SerializeField, Tooltip("启用自动化混合时长防穿模")]
        public bool useAutoTransTime;

        [HideInInspector]
        [SerializeField, Tooltip("启用自动化镜像防穿模")]
        public bool useAutoMirror;

        private bool transRiskCalculateThrowHeadTailAway = true;

        [HideInInspector]
        [SerializeField, Tooltip("优化双臂贴近动作风险计算值")]
        public bool transRiskCalculateOptimizeArmApproachingPose = true;

        [HideInInspector]
        [SerializeField, Tooltip("穿模风险计算步进次数")]
        public int fullAutoTransCheckStep = 10;

        [HideInInspector]
        [SerializeField, Tooltip("自动化混合时长范围")]
        public Vector2 TransTimeRange = new Vector2(0.6f, 1f);

        [HideInInspector]
        [SerializeField, Tooltip("使用面部骨骼动画")]
        public bool useFaceBone = false;

        //[HideInInspector]
        //[SerializeField, Tooltip("面部叠加资产")]
        //public AnimationClip faceAddictiveClip;

        // 手部覆写
        //public bool overWriteGlobalRightHand = false;
        //public bool overWriteGlobalLeftHand = false;
        //public AnimationClip overWriteGlobalRightHandClip;
        //public AnimationClip overWriteGlobalLeftHandClip;
        // 宏观addictive
        //public AnimationClip globalAddictiveClip;
        //public AvatarMask globalAddictiveMask;
        //public float globalAddictiveWeight = 0f;

        //////////////////////////////////////////////////////////
        ///////////////////////AC类表演过渡///////////////////////
        //////////////////////////////////////////////////////////
        public float ACTransBlendInOutTime { get; set; } = 1f;

        //////////////////////////////////////////////////////////
        ////////////////////////Rootmotion////////////////////////
        //////////////////////////////////////////////////////////
        public bool applyRootmotionControl { get; set; } = true;

        //////////////////////////////////////////////////////////
        ///////////////////////坐姿IK矫正参数/////////////////////
        //////////////////////////////////////////////////////////
        public float pelvisHeightOffset { get; private set; } = 0f;
        public float pelvisHeightBlendTime { get; private set; } = 0f;
        public bool useIkFootOffset { get; private set; } = false;
        
        [Tooltip("enable hand IK clipping fix")]
        public bool useIKHandFix  = false;
        //////////////////////////////////////////////////////////
        ///////////////////Debug模式宏观参数配置//////////////////
        //////////////////////////////////////////////////////////
        [HideInInspector]
        [SerializeField, Tooltip("显示自动化防穿Debug网格")]
        public bool showGrid = false;
        [HideInInspector]
        [SerializeField, Tooltip("左视图")]
        public bool viewLeft = false;
        [HideInInspector]
        [SerializeField, Tooltip("逐步显示过渡过程")]
        public bool showDebugStep = false;
        [HideInInspector]
        [SerializeField, Tooltip("步进位置")]
        public int debugStep = 10;
        [HideInInspector]
        [SerializeField, Tooltip("debug可视化Object")]
        public DebugShowScript debugObject;
        [HideInInspector]
        [SerializeField, Tooltip("debug相机Object")]
        public GameObject CameraHolder;
        [HideInInspector]
        [SerializeField, Tooltip("前视debug网格Object")]
        public GameObject FrontGrid;
        [HideInInspector]
        [SerializeField, Tooltip("左视debug网格Object")]
        public GameObject LeftGrid;

        //////////////////////////////////////////////////////////
        /////////////////Tag标注模式宏观参数配置//////////////////
        //////////////////////////////////////////////////////////
        [HideInInspector]
        [SerializeField, Tooltip("手臂空间位置X值范围")]
        public Vector2 XRange;
        [HideInInspector]
        [SerializeField, Tooltip("手臂空间位置Y值范围")]
        public Vector2 YRange;
        [HideInInspector]
        [SerializeField, Tooltip("手臂空间位置Z值范围")]
        public Vector2 ZRange;
        [HideInInspector]
        [SerializeField, Tooltip("前胸局部空间X轴网格划分值")]
        public float[] XClipLine;
        [HideInInspector]
        [SerializeField, Tooltip("前胸局部空间Y轴网格划分值")]
        public float[] YClipLine;
        [HideInInspector]
        [SerializeField, Tooltip("前胸局部空间Z轴网格划分值")]
        public float[] ZClipLine;
        [HideInInspector]
        [SerializeField, Tooltip("右手位置组件")]
        public Transform rightHandPosition;
        [HideInInspector]
        [SerializeField, Tooltip("左手位置组件")]
        public Transform leftHandPosition;
        [HideInInspector]
        [SerializeField, Tooltip("右手肘位置组件")]
        public Transform rightElbowPosition;
        [HideInInspector]
        [SerializeField, Tooltip("左手肘位置组件")]
        public Transform leftElbowPosition;

        //////////////////////////////////////////////////////////
        /////////////////////运行时辅助参数///////////////////////
        //////////////////////////////////////////////////////////

        /////////////////动画混合模式运行时辅助参数/////////////////
        private Vector3 rightHandPrevPosition;
        private Vector3 leftHandPrevPosition;
        private Vector3 rightElbowPrevPosition;
        private Vector3 leftElbowPrevPosition;

        private HandRelativeArmPosition rightHandCurrentRelativePosition;
        private HandRelativeArmPosition leftHandCurrentRelativePosition;
        private HandRelativeArmPosition rightHandPrevRelativePosition;
        private HandRelativeArmPosition leftHandPrevRelativePosition;

        private bool armPositionInit = true;

        private float prevRotateY = 0f;
        //private bool faceFront = true;

        private bool delayBodyTrans = false;
        private bool delayEyebrowTrans = false;
        private bool delayEyesTrans = false;
        private bool delayMouthTrans = false;
        private bool delayMouthBlankTrans = false;
        private bool delayMouthRestoreTrans = false;

        private bool prevUseFaceBone = false;

        private bool bodyDelayEvaluateImmediately = false;

        private MouthPerformConfig prevMouthPerformConfig;
        private bool inProgramMouthAnim = false;

        /////////////////Debug模式运行时辅助参数/////////////////
        private bool debug = false;
        private bool prevDebug = false;
        private bool prevShowGrid = false;
        private bool prevViewLeft = false;
        private int prevDebugStep = 10;
        private bool prevShowDebugStep = false;

        /////////////////Tag标注模式运行时辅助参数/////////////////
        private bool autoCalculateTags = false;
        private bool prevAutoCalculateTags = false;
        private bool animBeginRefresh = false;
        private bool animRefreshed = false;

        //////////////////////AC类表演过渡///////////////////////
        private bool inACPerformanceTrans = false;
        private bool APerformanceIsRootmotion = false;
        private bool CPerformanceIsRootmotion = false;

        private bool transA2C = false;
        private float A2CTransInDurationTime = 0f;
        private float CPerformanceSyetemWeight = 0f;

        private bool transC2A = false;
        private float C2ATransInDurationTime = 0f;
        private float APerformanceSyetemWeight = 0f;
        private PlayableGraph APerformancePlayableGraph;

        private Vector3 slotPosition;
        private Quaternion slotRotation;

        private float prevAPerformanceSyetemWeight = 0f;
        private float prevCPerformanceSyetemWeight = 0f;

        //////////////////////坐姿IK矫正///////////////////////
        //private bool updatePelvisOffset = false;
        private Transform rootBone = null;
        //private float prevPelvisHeightOffset = 0f;
        private bool canUpdateFootOffset = false;
        private float transPelvisHeightDuration = 0f;
        private float realPelvisHeightOffset = 0f;
        //private float prevRealPelvisHeightOffset = 0f;

        //////////////////////rootmotion///////////////////////
        private Vector3 actorSlotWorldPosition;
        private Quaternion actorSlotWorldRotation;
        private Vector3 prevActorSlotWorldPosition;
        private Quaternion prevActorSlotWorldRotation;

        private Vector3 actorOriWorldPosition;
        private Quaternion actorOriWorldRotation;

        private int currentTemplateID = -1;
        private int prevTemplateID = -1;

        private bool hasInitRootmotion = false;
        private bool hasTriggerContinue = false;

        [Header("手身穿插IK规避")]
        [Tooltip("The downward offset of the collision center from the spine")]
        [Range(-1, 1)]
        public float colliCenterOffsetDown = -0.077f;

        [Tooltip("The downward offset of the collision center from the spine")]
        [Range(-1, 1)]
        public float colliCenterOffsetBack = 0.156f;
        
        
        [Tooltip("The radius of the collision area")]
        [Range(0, 1)]
        public float colliRadius = 0.162f;
        
        [Tooltip("The weight of the IK")]
        [Range(0, 1)]
        public float weight = 1f; // IK weight

        [Tooltip("DrawDebugInfo")]
        public bool DrawDebugInfo = false;
        
        private void DrawHandIKDebugSphere()
        {            
            if (!animator)
            {
                return;
            }
            if (!animator.avatar.isHuman)
            {
                return;
            }

            Transform spineTransform = animator.GetBoneTransform(HumanBodyBones.Spine);
            Vector3 pos_center = spineTransform.position + spineTransform.right * colliCenterOffsetDown + -1 * spineTransform.up * colliCenterOffsetBack;

            
            // Set the color with custom alpha.
            Gizmos.color = new Color(1f, 0f, 0f, 0.7f); // Red with custom alpha

            // Draw the sphere.
            Gizmos.DrawSphere(pos_center, colliRadius);

            // Draw wire sphere outline.
            Gizmos.color = Color.white;
            Gizmos.DrawWireSphere(pos_center, colliRadius);
        }
        private void HandIKClipEvade()
        {
            // check is humanoid animator
            if (!animator)
            {
                return;
            }
            if (!animator.avatar.isHuman)
            {
                return;
            }

            Transform spineTransform = animator.GetBoneTransform(HumanBodyBones.Spine);
            Vector3 pos_center = spineTransform.position + spineTransform.right * colliCenterOffsetDown + -1 * spineTransform.up * colliCenterOffsetBack;
            
            // Get all required bone positions
            Vector3 pos_rhand = animator.GetBoneTransform(HumanBodyBones.RightHand).position;
            Vector3 pos_lhand = animator.GetBoneTransform(HumanBodyBones.LeftHand).position;
            Vector3 pos_relbow = animator.GetBoneTransform(HumanBodyBones.RightLowerArm).position;
            Vector3 pos_lelbow = animator.GetBoneTransform(HumanBodyBones.LeftLowerArm).position;
            Vector3 pos_rshoulder = animator.GetBoneTransform(HumanBodyBones.RightUpperArm).position;
            Vector3 pos_lshoulder = animator.GetBoneTransform(HumanBodyBones.LeftUpperArm).position;

            // Calculate original pole vectors
            Vector3 rightElbowPoleVector = (pos_relbow - (pos_rshoulder + pos_rhand) * 0.5f);
            Vector3 leftElbowPoleVector = (pos_lelbow - (pos_lshoulder + pos_lhand) * 0.5f);
            
            
            // Check if right hand is in collision area
            float dist_rhand = Vector3.Distance(pos_center, pos_rhand);
            if (dist_rhand < colliRadius)
            {
                Vector3 direction = (pos_rhand - pos_center).normalized;
                Vector3 newPos = pos_center + direction * colliRadius;
                
                // Set right hand new IK position
                animator.SetIKPositionWeight(AvatarIKGoal.RightHand, weight);
                animator.SetIKPosition(AvatarIKGoal.RightHand, newPos);
                // use hand original rotation
                animator.SetIKRotationWeight(AvatarIKGoal.RightHand, weight);

            
                // Calculate and set right hand IKHint position
                Vector3 midPoint = (pos_rshoulder + newPos) * 0.5f;
                Vector3 newHintPos = midPoint + rightElbowPoleVector * 1.2f;
                animator.SetIKHintPositionWeight(AvatarIKHint.RightElbow, weight);
                animator.SetIKHintPosition(AvatarIKHint.RightElbow, newHintPos);
            }
            
            // Check if left hand is in collision area
            float dist_lhand = Vector3.Distance(pos_center, pos_lhand);
            if (dist_lhand < colliRadius)
            {
                Vector3 direction = (pos_lhand - pos_center).normalized;
                Vector3 newPos = pos_center + direction * colliRadius;
            
                // Set left hand IK position
                animator.SetIKPositionWeight(AvatarIKGoal.LeftHand, weight);
                animator.SetIKPosition(AvatarIKGoal.LeftHand, newPos);
                // use hand original rotation
                animator.SetIKRotationWeight(AvatarIKGoal.LeftHand, weight);
                
                // Calculate and set left hand IKHint position
                Vector3 midPoint = (pos_lshoulder + newPos) * 0.5f;
                Vector3 newHintPos = midPoint + leftElbowPoleVector * 1.2f;
                animator.SetIKHintPositionWeight(AvatarIKHint.LeftElbow, weight);
                animator.SetIKHintPosition(AvatarIKHint.LeftElbow, newHintPos);
            }
            // 打印IK权重信息
            // Debug.Log("================================================");
            // Debug.Log($"右手当前位置: {animator.GetBoneTransform(HumanBodyBones.RightHand).position.ToString("F8")}");
            // Debug.Log($"右手当前旋转: {animator.GetBoneTransform(HumanBodyBones.RightHand).rotation.eulerAngles.ToString("F8")}");
            // Debug.Log($"右手IK目标位置: {animator.GetIKPosition(AvatarIKGoal.RightHand).ToString("F8")}");
            // Debug.Log($"右手IK目标旋转: {animator.GetIKRotation(AvatarIKGoal.RightHand).eulerAngles.ToString("F8")}");
            // Debug.Log($"右手IK权重: {animator.GetIKPositionWeight(AvatarIKGoal.RightHand)}");
            // Debug.Log($"左手IK权重: {animator.GetIKPositionWeight(AvatarIKGoal.LeftHand)}"); 
            // Debug.Log($"右手IKRot权重: {animator.GetIKRotationWeight(AvatarIKGoal.RightHand)}");
            // Debug.Log($"左手IKRot权重: {animator.GetIKRotationWeight(AvatarIKGoal.LeftHand)}"); 
            // Debug.Log($"右肘IKHint权重: {animator.GetIKHintPositionWeight(AvatarIKHint.RightElbow)}");
            // Debug.Log($"左肘IKHint权重: {animator.GetIKHintPositionWeight(AvatarIKHint.LeftElbow)}");

        }
        private void Awake()
        {
            hasInitRootmotion = false;
            hasTriggerContinue = false;
            armPositionInit = true;
            InitACTransSystem();
        }

        // Start is called before the first frame update
        void Start()
        {
            
        }

        // Update is called once per frame
        void Update()
        {
            MasterModeMaster();

            if (animTransPlayableSystem != null)
            {
                if (delayBodyTrans && animTransPlayableSystem.CheckExecuteBodyTransValid() /*&& faceFront*/ && animTransPlayableSystem.AnimTransSystemIsWorking())
                {
                    delayBodyTrans = false;
                    BodyTransAnim(bodyDelayEvaluateImmediately);
                }
                if (delayEyebrowTrans && animTransPlayableSystem.CheckExecuteEyebrowTransValid() && animTransPlayableSystem.AnimTransSystemIsWorking())
                {
                    delayEyebrowTrans = false;
                    EyebrowTransAnim();
                }
                if (delayEyesTrans && animTransPlayableSystem.CheckExecuteEyesTransValid() && animTransPlayableSystem.AnimTransSystemIsWorking())
                {
                    delayEyesTrans = false;
                    EyesTransAnim();
                }
                if (delayMouthTrans && animTransPlayableSystem.CheckExecuteMouthTransValid() && animTransPlayableSystem.AnimTransSystemIsWorking())
                {
                    delayMouthTrans = false;
                    MouthTransAnim();
                }
                if (delayMouthBlankTrans && animTransPlayableSystem.CheckExecuteMouthTransValid() && animTransPlayableSystem.AnimTransSystemIsWorking())
                {
                    delayMouthBlankTrans = false;
                    TransMouthToBlank();
                }
                if (delayMouthRestoreTrans && animTransPlayableSystem.CheckExecuteMouthTransValid() && animTransPlayableSystem.AnimTransSystemIsWorking())
                {
                    delayMouthRestoreTrans = false;
                    RestoreMouthAnimState();
                }
            }
            DrawDebug();
            if (animRefreshed)
            {
                AutoCalculateTags();
                animRefreshed = false;
            }
            if (animBeginRefresh)
            {
                animBeginRefresh = false;
                animRefreshed = true;
            }

            if (inACPerformanceTrans)
                UpdateACTransWeight();
        }

        private void LateUpdate()
        {
            UpdateActorPelvisOffset();
        }

        private void OnAnimatorIK(int layerIndex)
        {
            if (useIkFootOffset)
                UpdateActorFootOffset();

            if (useIKHandFix)
                HandIKClipEvade();
            //float currentRotateY = animator.bodyRotation.eulerAngles.y - 180;
            //float rawDeltaRotateY = Mathf.Abs(currentRotateY - prevRotateY);
            //float deltaRotateY = (rawDeltaRotateY > 180) ? (360 - rawDeltaRotateY) : rawDeltaRotateY;

            //faceFront = (((Mathf.Abs(currentRotateY) / 30) * ((deltaRotateY / Time.deltaTime) / 30)) + (Mathf.Abs(currentRotateY) / 90)) < 1;
            //faceFront = true;

            //prevRotateY = currentRotateY;
        }

        void OnDisable()
        {
            animTransPlayableSystem = null;
        }

        public bool InitTransSystem()
        {
            animTransPlayableSystem = GetComponent<AnimTransPlayableSystem>();
            animator = GetComponent<Animator>();

            if (animTransPlayableSystem != null && animator != null)
            {
                BodyPerformConfig bodyConfig = new BodyPerformConfig();
                bodyConfig.bodyPerformConfig.animationClip = currentAnimClip;
                bodyConfig.bodyPerformConfig.startCutTime = animStartTime;
                bodyConfig.bodyPerformConfig.endCutTime = animEndTime;
                bodyConfig.bodyPerformConfig.playingMode = playingMode;
                bodyConfig.bodyPerformConfig.transABBlendType = blendInType;
                bodyConfig.bodyPerformConfig.transABFullTime = 0;
                bodyConfig.bodyPerformConfig.transStaticBlendType = staticBlendInType;
                bodyConfig.bodyPerformConfig.transStaticFullTime = staticBlendInDuration;
                bodyConfig.bodyPerformConfig.playRate = animPlayRate;
                bodyConfig.bodyPerformConfig.mirror = mirrorAnim;
                bodyConfig.bodyPerformConfig.weight = 1;

                bodyConfig.breathPerformConfig.breathClip = breathClip;
                bodyConfig.breathPerformConfig.breathAvatarMask = breathAvatarMask;
                bodyConfig.breathPerformConfig.breathWeight = breathStrength;

                EyebrowPerformConfig eyebrowConfig = new EyebrowPerformConfig();
                eyebrowConfig.eyebrowPerformConfig.animationClip = currentFaceEyebrowAnimClip;
                eyebrowConfig.eyebrowPerformConfig.startCutTime = eyebrowStartTime;
                eyebrowConfig.eyebrowPerformConfig.endCutTime = eyebrowEndTime;
                eyebrowConfig.eyebrowPerformConfig.playingMode = eyebrowPlayingMode;
                eyebrowConfig.eyebrowPerformConfig.transABBlendType = faceEyebrowBlendType;
                eyebrowConfig.eyebrowPerformConfig.transABFullTime = 0;
                eyebrowConfig.eyebrowPerformConfig.transStaticBlendType = faceEyebrowStaticBlendType;
                eyebrowConfig.eyebrowPerformConfig.transStaticFullTime = faceEyebrowStaticBlendInDuration;
                eyebrowConfig.eyebrowPerformConfig.weight = currentFaceEyebrowWeight;
                eyebrowConfig.eyebrowPerformConfig.playRate = 1;
                eyebrowConfig.eyebrowPerformConfig.mirror = false;

                EyesPerformConfig eyesConfig = new EyesPerformConfig();
                eyesConfig.eyesPerformConfig.animationClip = currentFaceEyesAnimClip;
                eyesConfig.eyesPerformConfig.startCutTime = eyesStartTime;
                eyesConfig.eyesPerformConfig.endCutTime = eyesEndTime;
                eyesConfig.eyesPerformConfig.playingMode = eyesPlayingMode;
                eyesConfig.eyesPerformConfig.transABBlendType = faceEyesBlendType;
                eyesConfig.eyesPerformConfig.transABFullTime = 0;
                eyesConfig.eyesPerformConfig.transStaticBlendType = faceEyesStaticBlendType;
                eyesConfig.eyesPerformConfig.transStaticFullTime = faceEyesStaticBlendInDuration;
                eyesConfig.eyesPerformConfig.weight = currentFaceEyesWeight;
                eyesConfig.eyesPerformConfig.playRate = 1;
                eyesConfig.eyesPerformConfig.mirror = false;

                MouthPerformConfig mouthConfig = new MouthPerformConfig();
                mouthConfig.mouthPerformConfig.animationClip = currentFaceMouthAnimClip;
                mouthConfig.mouthPerformConfig.startCutTime = mouthStartTime;
                mouthConfig.mouthPerformConfig.endCutTime = mouthEndTime;
                mouthConfig.mouthPerformConfig.playingMode = mouthPlayingMode;
                mouthConfig.mouthPerformConfig.transABBlendType = faceMouthBlendType;
                mouthConfig.mouthPerformConfig.transABFullTime = 0;
                mouthConfig.mouthPerformConfig.transStaticBlendType = faceMouthStaticBlendType;
                mouthConfig.mouthPerformConfig.transStaticFullTime = faceMouthStaticBlendInDuration;
                mouthConfig.mouthPerformConfig.weight = currentFaceMouthWeight;
                mouthConfig.mouthPerformConfig.playRate = 1;
                mouthConfig.mouthPerformConfig.mirror = false;

                if (animTransPlayableSystem.InitAndStartPlayableSystem(bodyConfig, eyebrowConfig, eyesConfig, mouthConfig))
                {
                    UnityEngine.Debug.Log("InitAndStartPlayableSystem");
                    animTransPlayableSystem.OverWriteLocalHand(overWriteLocalRightHand, overWriteLocalLeftHand, overWriteLocalRightHandClip, overWriteLocalLeftHandClip);
                    animTransPlayableSystem.ModifyPlayableGraphStructFaceBone(useFaceBone);
                    //animTransPlayableSystem.ChangeFaceAddictiveAnimClip(faceAddictiveClip);
                    //animTransPlayableSystem.OverWriteGlobalHand(overWriteGlobalRightHand, overWriteGlobalLeftHand, overWriteGlobalRightHandClip, overWriteGlobalLeftHandClip);
                    //animTransPlayableSystem.ChangeGlobalAddictiveAnimClip(globalAddictiveClip);
                    //animTransPlayableSystem.ChangeGlobalAddictiveMask(globalAddictiveMask);
                    //animTransPlayableSystem.ChangeGlobalAddictiveWeight(globalAddictiveWeight);
                    //if (StationTemplateManager.instance != null)
                    //    StationTemplateManager.instance.SyncStaticHangingFromDynamicForSlotActorGameObject(this.gameObject);

                    return true;
                }
                else
                    return false;
            }
            else
                return false;
        }

        public void BodyTransAnim(bool evaluateImmediately)
        {
            UnityEngine.Debug.Log("BodyTransAnim");
            //UnityEngine.Debug.Break();
            if ((animTransPlayableSystem == null) || !animTransPlayableSystem.AnimTransSystemIsWorking())
            {
                InitTransSystem();
            }

            if (!(animTransPlayableSystem.CheckExecuteBodyTransValid() /*&& faceFront*/))
            {
                UnityEngine.Debug.Log("BodyTransAnim Delay");
                delayBodyTrans = true;
                bodyDelayEvaluateImmediately = evaluateImmediately;
                return;
            }

            if (animTransPlayableSystem != null)
            {
                if (currentMode != MasterMode.AutoTagCalculate)
                {
                    if (useAutoTransTime && (!useAutoMirror))
                    {
                        TransTimeRange.x = (TransTimeRange.x >= 0f) ? TransTimeRange.x : 0f;
                        TransTimeRange.y = (TransTimeRange.y >= 0f) ? TransTimeRange.y : 0f;
                        blendInDuration = Mathf.Lerp(TransTimeRange.y, TransTimeRange.x, CalculateArmCrossPossibility(fullAutoTransCheckStep, (int)(XRange.y - XRange.x + 1), (int)(YRange.y - YRange.x + 1), (int)(ZRange.y - ZRange.x + 1), (int)(XRange.x), (int)(YRange.x), (int)(ZRange.x), debug));
                    }
                    if (useAutoMirror)
                    {
                        float noMirrorCrossPossibility = CalculateArmCrossPossibility(fullAutoTransCheckStep, (int)(XRange.y - XRange.x + 1), (int)(YRange.y - YRange.x + 1), (int)(ZRange.y - ZRange.x + 1), (int)(XRange.x), (int)(YRange.x), (int)(ZRange.x), false);
                        float mirrorCrossPossibility = CalculateArmCrossPossibility(GetMirrorPosition(leftHandCurrectPosition), GetMirrorPosition(leftElbowCurrectPosition), GetMirrorPosition(rightHandCurrectPosition), GetMirrorPosition(rightElbowCurrectPosition), fullAutoTransCheckStep, (int)(XRange.y - XRange.x + 1), (int)(YRange.y - YRange.x + 1), (int)(ZRange.y - ZRange.x + 1), (int)(XRange.x), (int)(YRange.x), (int)(ZRange.x), false);
                        //Debug.Log("noMirrorCrossPossibility: " + noMirrorCrossPossibility.ToString() + "  mirrorCrossPossibility: " + mirrorCrossPossibility.ToString());
                        if ((mirrorCrossPossibility < noMirrorCrossPossibility) && mirrorCrossPossibility < 0.5)
                        {
                            mirrorAnim = !mirrorAnim;

                            rightHandCurrectPosition = GetMirrorPosition(leftHandCurrectPosition);
                            leftHandCurrectPosition = GetMirrorPosition(rightHandCurrectPosition);
                            rightElbowCurrectPosition = GetMirrorPosition(leftElbowCurrectPosition);
                            leftElbowCurrectPosition = GetMirrorPosition(rightElbowCurrectPosition);

                            HandRelativeArmPosition tempHandRelativeArmPosition = rightHandCurrentRelativePosition;
                            rightHandCurrentRelativePosition = leftHandCurrentRelativePosition;
                            leftHandCurrentRelativePosition = tempHandRelativeArmPosition;

                            blendInDuration = Mathf.Lerp(TransTimeRange.y, TransTimeRange.x, mirrorCrossPossibility);
                        }
                        else
                        {
                            blendInDuration = Mathf.Lerp(TransTimeRange.y, TransTimeRange.x, noMirrorCrossPossibility);
                        }
                    }
                }
                else
                {
                    mirrorAnim = false;
                }

                animTransPlayableSystem.OverWriteLocalHand(overWriteLocalRightHand, overWriteLocalLeftHand, overWriteLocalRightHandClip, overWriteLocalLeftHandClip);
                //animTransPlayableSystem.OverWriteGlobalHand(overWriteGlobalRightHand, overWriteGlobalLeftHand, overWriteGlobalRightHandClip, overWriteGlobalLeftHandClip);
                //animTransPlayableSystem.ChangeGlobalAddictiveAnimClip(globalAddictiveClip);
                //animTransPlayableSystem.ChangeGlobalAddictiveMask(globalAddictiveMask);
                //animTransPlayableSystem.ChangeGlobalAddictiveWeight(globalAddictiveWeight);

                BodyPerformConfig bodyConfig = new BodyPerformConfig();
                bodyConfig.bodyPerformConfig.animationClip = currentAnimClip;
                bodyConfig.bodyPerformConfig.startCutTime = animStartTime;
                bodyConfig.bodyPerformConfig.endCutTime = animEndTime;
                bodyConfig.bodyPerformConfig.playingMode = playingMode;
                bodyConfig.bodyPerformConfig.transABBlendType = blendInType;
                bodyConfig.bodyPerformConfig.transABFullTime = blendInDuration;
                bodyConfig.bodyPerformConfig.transStaticBlendType = staticBlendInType;
                bodyConfig.bodyPerformConfig.transStaticFullTime = staticBlendInDuration;
                bodyConfig.bodyPerformConfig.playRate = animPlayRate;
                bodyConfig.bodyPerformConfig.mirror = mirrorAnim;
                bodyConfig.bodyPerformConfig.weight = 1;

                bodyConfig.breathPerformConfig.breathClip = breathClip;
                bodyConfig.breathPerformConfig.breathAvatarMask = breathAvatarMask;
                bodyConfig.breathPerformConfig.breathWeight = breathStrength;

                EyeBlinkManager blinkManager = this.gameObject.GetComponent<EyeBlinkManager>();
                if (blinkManager != null)
                    blinkManager.BlinkOnce(EyeOverwriteType.CompletelyClose);

                //UnityEngine.Debug.Log(ZString.Concat("BodyTransAnim playableGraph GetWeight: ", animTransPlayableSystem.playableGraph.GetOutput(0).GetWeight()));
                //UnityEngine.Debug.Break();
                animTransPlayableSystem.BodyTransAnim(bodyConfig, evaluateImmediately);

                if (autoCalculateTags)
                    animBeginRefresh = true;
            }
        }

        public void EyebrowTransAnim()
        {
            if ((animTransPlayableSystem == null) || !animTransPlayableSystem.AnimTransSystemIsWorking())
            {
                InitTransSystem();
            }

            if (!(animTransPlayableSystem.CheckExecuteEyebrowTransValid()))
            {
                delayEyebrowTrans = true;
                return;
            }

            if (animTransPlayableSystem != null)
            {
                EyebrowPerformConfig eyebrowConfig = new EyebrowPerformConfig();
                eyebrowConfig.eyebrowPerformConfig.animationClip = currentFaceEyebrowAnimClip;
                eyebrowConfig.eyebrowPerformConfig.startCutTime = eyebrowStartTime;
                eyebrowConfig.eyebrowPerformConfig.endCutTime = eyebrowEndTime;
                eyebrowConfig.eyebrowPerformConfig.playingMode = eyebrowPlayingMode;
                eyebrowConfig.eyebrowPerformConfig.transABBlendType = faceEyebrowBlendType;
                eyebrowConfig.eyebrowPerformConfig.transABFullTime = faceEyebrowBlendInDuration;
                eyebrowConfig.eyebrowPerformConfig.transStaticBlendType = faceEyebrowStaticBlendType;
                eyebrowConfig.eyebrowPerformConfig.transStaticFullTime = faceEyebrowStaticBlendInDuration;
                eyebrowConfig.eyebrowPerformConfig.weight = currentFaceEyebrowWeight;
                eyebrowConfig.eyebrowPerformConfig.playRate = 1;
                eyebrowConfig.eyebrowPerformConfig.mirror = false;

                animTransPlayableSystem.EyebrowTransAnim(eyebrowConfig);
            }
        }

        public void EyesTransAnim()
        {
            if ((animTransPlayableSystem == null) || !animTransPlayableSystem.AnimTransSystemIsWorking())
            {
                InitTransSystem();
            }

            if (!(animTransPlayableSystem.CheckExecuteEyesTransValid()))
            {
                delayEyesTrans = true;
                return;
            }

            if (animTransPlayableSystem != null)
            {
                EyesPerformConfig eyesConfig = new EyesPerformConfig();
                eyesConfig.eyesPerformConfig.animationClip = currentFaceEyesAnimClip;
                eyesConfig.eyesPerformConfig.startCutTime = eyesStartTime;
                eyesConfig.eyesPerformConfig.endCutTime = eyesEndTime;
                eyesConfig.eyesPerformConfig.playingMode = eyesPlayingMode;
                eyesConfig.eyesPerformConfig.transABBlendType = faceEyesBlendType;
                eyesConfig.eyesPerformConfig.transABFullTime = faceEyesBlendInDuration;
                eyesConfig.eyesPerformConfig.transStaticBlendType = faceEyesStaticBlendType;
                eyesConfig.eyesPerformConfig.transStaticFullTime = faceEyesStaticBlendInDuration;
                eyesConfig.eyesPerformConfig.weight = currentFaceEyesWeight;
                eyesConfig.eyesPerformConfig.playRate = 1;
                eyesConfig.eyesPerformConfig.mirror = false;

                bool useBlink = false;
                if (useEyesOverwriteAnim)
                {
                    EyeBlinkManager blinkManager = this.gameObject.GetComponent<EyeBlinkManager>();
                    if (blinkManager != null)
                        useBlink = blinkManager.BlinkOnce(EyeOverwriteType.CompletelyClose);
                }
                animTransPlayableSystem.EyesTransAnim(eyesConfig, useBlink);
            }
        }

        public void MouthTransAnim()
        {
            if ((animTransPlayableSystem == null) || !animTransPlayableSystem.AnimTransSystemIsWorking())
            {
                InitTransSystem();
            }

            if (inProgramMouthAnim)
            {
                MouthPerformConfig mouthConfig = new MouthPerformConfig();
                mouthConfig.mouthPerformConfig.animationClip = currentFaceMouthAnimClip;
                mouthConfig.mouthPerformConfig.startCutTime = mouthStartTime;
                mouthConfig.mouthPerformConfig.endCutTime = mouthEndTime;
                mouthConfig.mouthPerformConfig.playingMode = mouthPlayingMode;
                mouthConfig.mouthPerformConfig.transABBlendType = faceMouthBlendType;
                mouthConfig.mouthPerformConfig.transABFullTime = faceMouthBlendInDuration;
                mouthConfig.mouthPerformConfig.transStaticBlendType = faceMouthStaticBlendType;
                mouthConfig.mouthPerformConfig.transStaticFullTime = faceMouthStaticBlendInDuration;
                mouthConfig.mouthPerformConfig.weight = currentFaceMouthWeight;
                mouthConfig.mouthPerformConfig.playRate = 1;
                mouthConfig.mouthPerformConfig.mirror = false;

                prevMouthPerformConfig = mouthConfig;
                return;
            }

            if (!(animTransPlayableSystem.CheckExecuteMouthTransValid()))
            {
                delayMouthTrans = true;
                return;
            }

            if (animTransPlayableSystem != null)
            {
                MouthPerformConfig mouthConfig = new MouthPerformConfig();
                mouthConfig.mouthPerformConfig.animationClip = currentFaceMouthAnimClip;
                mouthConfig.mouthPerformConfig.startCutTime = mouthStartTime;
                mouthConfig.mouthPerformConfig.endCutTime = mouthEndTime;
                mouthConfig.mouthPerformConfig.playingMode = mouthPlayingMode;
                mouthConfig.mouthPerformConfig.transABBlendType = faceMouthBlendType;
                mouthConfig.mouthPerformConfig.transABFullTime = faceMouthBlendInDuration;
                mouthConfig.mouthPerformConfig.transStaticBlendType = faceMouthStaticBlendType;
                mouthConfig.mouthPerformConfig.transStaticFullTime = faceMouthStaticBlendInDuration;
                mouthConfig.mouthPerformConfig.weight = currentFaceMouthWeight;
                mouthConfig.mouthPerformConfig.playRate = 1;
                mouthConfig.mouthPerformConfig.mirror = false;

                animTransPlayableSystem.MouthTransAnim(mouthConfig);
            }
        }

        public void TransMouthToBlank()
        {
            if (inProgramMouthAnim)
                return;

            if ((animTransPlayableSystem == null) || !animTransPlayableSystem.AnimTransSystemIsWorking())
            {
                InitTransSystem();
                inProgramMouthAnim = true;
                return;
            }

            if (!(animTransPlayableSystem.CheckExecuteMouthTransValid()))
            {
                delayMouthBlankTrans = true;
                return;
            }

            if (animTransPlayableSystem != null)
            {
                prevMouthPerformConfig = animTransPlayableSystem.TransMouthToBlank();
                inProgramMouthAnim = true;
            }
        }

        public void RestoreMouthAnimState()
        {
            if (!inProgramMouthAnim)
                return;
            if (ReferenceEquals(prevMouthPerformConfig, null))
                return;
            if ((animTransPlayableSystem == null) || !animTransPlayableSystem.AnimTransSystemIsWorking())
                return;

            if (!(animTransPlayableSystem.CheckExecuteMouthTransValid()))
            {
                delayMouthRestoreTrans = true;
                return;
            }

            if (animTransPlayableSystem != null)
            {
                prevMouthPerformConfig.mouthPerformConfig.startCutTime = prevMouthPerformConfig.mouthPerformConfig.endCutTime;
                prevMouthPerformConfig.mouthPerformConfig.playingMode = PlayingMode.EndStop;
                prevMouthPerformConfig.mouthPerformConfig.transABBlendType = BlendType.EaseInOut;
                prevMouthPerformConfig.mouthPerformConfig.transABFullTime = 0.3f;
                prevMouthPerformConfig.mouthPerformConfig.transStaticBlendType = BlendType.EaseInOut;
                prevMouthPerformConfig.mouthPerformConfig.transStaticFullTime = 0;

                animTransPlayableSystem.MouthTransAnim(prevMouthPerformConfig);
                inProgramMouthAnim = false;
            }
        }

        public void OverwriteRightHand(bool overwrite, AnimationClip anim)
        {
            if (animTransPlayableSystem != null && animTransPlayableSystem.AnimTransSystemIsWorking())
                animTransPlayableSystem.OverWriteRightGlobalHand(overwrite, anim);
        }

        public void OverwriteLeftHand(bool overwrite, AnimationClip anim)
        {
            if (animTransPlayableSystem != null && animTransPlayableSystem.AnimTransSystemIsWorking())
                animTransPlayableSystem.OverWriteLeftGlobalHand(overwrite, anim);
        }

        public bool BlinkOnce(EyesOverwritePerformConfig blinkConfig)
        {
            if (!blinkConfig.eyesOverwriteConfig.useEyesOverwriteAnim)
                return false;

            if ((animTransPlayableSystem == null) || !animTransPlayableSystem.AnimTransSystemIsWorking())
            {
                InitTransSystem();
            }
            else
            {
                if (animTransPlayableSystem != null)
                {
                    //EyesOverwritePerformConfig blinkConfig = new EyesOverwritePerformConfig();
                    //blinkConfig.blinkType = blinkType;
                    //blinkConfig.eyesOverwriteConfig.useEyesOverwriteAnim = true;
                    //blinkConfig.eyesOverwriteConfig.eyesOverwriteAnim = eyesOverwriteAnim;
                    //blinkConfig.eyesOverwriteConfig.eyesOverwriteAnimFrameTime = eyesOverwriteAnimFrameTime;
                    //blinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime = 0.15f;
                    //blinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendOutTime = 0.3f;

                    return animTransPlayableSystem.ExecuteBlink(blinkConfig);
                }
            }
            return false;
        }

        public bool CloseEyes(EyesOverwritePerformConfig eyeCloseConfig)
        {
            if (animTransPlayableSystem != null && animTransPlayableSystem.AnimTransSystemIsWorking())
            {
                return animTransPlayableSystem.ExecuteCloseEyes(eyeCloseConfig);
            }
            return false;
        }

        public bool OpenEyes()
        {
            if (animTransPlayableSystem != null && animTransPlayableSystem.AnimTransSystemIsWorking())
            {
                return animTransPlayableSystem.ExecuteOpenEye();
            }
            return false;
        }

        public bool CheckInCloseEyes()
        {
            if (animTransPlayableSystem != null && animTransPlayableSystem.AnimTransSystemIsWorking())
            {
                return animTransPlayableSystem.CheckInCloseEyes();
            }
            return false;
        }

        public bool CheckIsOverWriteRightHand()
        {
            if (animTransPlayableSystem != null)
                return animTransPlayableSystem.CheckIsGlobalOverwriteRightHand();
            return false;
        }

        public bool CheckIsOverWriteLeftHand()
        {
            if (animTransPlayableSystem != null)
                return animTransPlayableSystem.CheckIsGlobalOverwriteLeftHand();
            return false;
        }

        public string GetRightHandOverwriteAnimName()
        {
            if (animTransPlayableSystem != null)
                return animTransPlayableSystem.GetRightHandGlobalOverwriteAnimName();
            return "";
        }

        public string GetLeftHandOverwriteAnimName()
        {
            if (animTransPlayableSystem != null)
                return animTransPlayableSystem.GetLeftHandGlobalOverwriteAnimName();
            return "";
        }

        /////////////////////////////////////////////////////////////////////////
        ///////////////////////// Rootmotion Manager ////////////////////////////
        /////////////////////////////////////////////////////////////////////////
        private void ZeroActorRootTransform()
        {
            if (!applyRootmotionControl) return;
            UnityEngine.Debug.Log(ZString.Concat(this.gameObject.name, "  Rootmotion ZeroActorRootTransform"));
            this.gameObject.transform.position = Vector3.zero;
            this.gameObject.transform.rotation = Quaternion.identity;
        }

        private void ContinueRootMotion()
        {
            if (!applyRootmotionControl) return;
            if (!hasInitRootmotion) return;
            if (!hasTriggerContinue) return;

            UnityEngine.Debug.Log(ZString.Concat(this.gameObject.name, "  Rootmotion ContinueRootMotion", actorOriWorldPosition.ToString(), actorOriWorldRotation.ToString()));
            this.gameObject.transform.position = actorOriWorldPosition;
            this.gameObject.transform.rotation = actorOriWorldRotation;

            hasTriggerContinue = false;
        }

        public void ResetActorToSlot()
        {
            if (!applyRootmotionControl) return;
            UnityEngine.Debug.Log(ZString.Concat(this.gameObject.name, "  Rootmotion ResetActorToSlot"));
            this.gameObject.transform.localPosition = Vector3.zero;
            this.gameObject.transform.localRotation = Quaternion.identity;
            hasInitRootmotion = false;
            hasTriggerContinue = false;
        }

        public void TriggerRecordRootmotion(Vector3 newWorldPosition, Quaternion newWorldRotation)
        {
            //if (newWorldPosition.z < -9000) return; // todo
            if (!applyRootmotionControl) return;
            UnityEngine.Debug.Log(ZString.Concat(this.gameObject.name, "  Rootmotion TriggerRecordRootmotion", newWorldPosition.ToString(), newWorldRotation.ToString()));
            actorOriWorldPosition = newWorldPosition;
            actorOriWorldRotation = newWorldRotation;
            hasInitRootmotion = true;
            hasTriggerContinue = true;
        }

        public void TriggerCutsceneChange(GameObject slotRef, int templateID)
        {
            if (!applyRootmotionControl) return;
            UnityEngine.Debug.Log(ZString.Concat(this.gameObject.name, "  Rootmotion TriggerCutsceneChange Enter"));
            prevTemplateID = currentTemplateID;
            currentTemplateID = templateID;

            prevActorSlotWorldPosition = actorSlotWorldPosition;
            prevActorSlotWorldRotation = actorSlotWorldRotation;

            actorSlotWorldPosition = slotRef.transform.position;
            actorSlotWorldRotation = slotRef.transform.rotation;

            if ((currentTemplateID != prevTemplateID) || Vector3.Distance(actorSlotWorldPosition, prevActorSlotWorldPosition) > 0.001)
            {
                UnityEngine.Debug.Log(ZString.Concat(this.gameObject.name, "  Rootmotion TriggerCutsceneChange currentTemplateID: ", currentTemplateID, "  prevTemplateID: ", prevTemplateID, "  actorSlotWorldPosition: ", actorSlotWorldPosition, "  prevSlotPosition: ", prevActorSlotWorldPosition));
                ResetActorToSlot();
                animTransPlayableSystem.SetAnimStateToJustInit();
            }
            else
            {
                ContinueRootMotion();
            }
        }

        public void SetRootMotion(Vector3 pos, Quaternion rot)
        {
            TriggerRecordRootmotion(pos, rot);
            ContinueRootMotion();
        }

        public bool CheckBodyJustInit()
        {
            if (animTransPlayableSystem != null && animTransPlayableSystem.AnimTransSystemIsWorking())
                return animTransPlayableSystem.CheckBodyJustInit();
            return true;
        }

        /////////////////////////////////////////////////////////////////////////
        /////////////////////////// AC互转相关接口 //////////////////////////////
        /////////////////////////////////////////////////////////////////////////

        private void InitACTransSystem()
        {
            inACPerformanceTrans = false;

            APerformanceIsRootmotion = false;
            CPerformanceIsRootmotion = false;

            transA2C = false;
            A2CTransInDurationTime = 0f;
            CPerformanceSyetemWeight = 1f;

            transC2A = false;
            C2ATransInDurationTime = 0f;
            APerformanceSyetemWeight = 0f;
        }

        private void SetCPerformancePlayableGraphWeight(float weight)
        {
            if (animTransPlayableSystem != null && animTransPlayableSystem.AnimTransSystemIsWorking())
                animTransPlayableSystem.playableGraph.GetOutput(0).SetWeight(weight);
        }

        private void SetAPerformancePlayableGraphWeight(float weight)
        {
            if (!APerformancePlayableGraph.IsValid())
            {
                var APerformanceAnimSystem = this.gameObject.GetComponent<AnimancerComponent>();
                if (APerformanceAnimSystem == null) return;

                APerformancePlayableGraph = APerformanceAnimSystem.Graph;
            }
            if (APerformancePlayableGraph.IsValid())
                APerformancePlayableGraph.GetOutput(0).SetWeight(weight);
        }

        private void InitA2CTransParam()
        {
            UnityEngine.Debug.Log(ZString.Concat(this.gameObject.name, "  InitA2CTransParam"));
            A2CTransInDurationTime = 0f;
            CPerformanceSyetemWeight = 0f;
            SetCPerformancePlayableGraphWeight(0);

            if (!APerformanceIsRootmotion)
                ZeroActorRootTransform();
            else
            {
                //TriggerRecordRootmotion(actorOriWorldPositionInit, actorOriWorldRotationInit);
                SetRootMotion(this.gameObject.transform.position, this.gameObject.transform.rotation);
            }

            slotPosition = this.gameObject.transform.parent.position;
            slotRotation = this.gameObject.transform.parent.rotation;

            transA2C = true;
        }

        private void InitC2ATransParam()
        {
            C2ATransInDurationTime = 0f;
            APerformanceSyetemWeight = 0f;
            SetAPerformancePlayableGraphWeight(0);

            slotPosition = this.gameObject.transform.parent.position;
            slotRotation = this.gameObject.transform.parent.rotation;

            transC2A = true;
        }

        private void LoadAPerformanceComponent()
        {
            var APerformanceAnimSystem = this.gameObject.GetComponent<AnimancerComponent>();
            if (APerformanceAnimSystem != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(APerformanceAnimSystem);
#else
                Destroy(APerformanceAnimSystem);
#endif
            }
            APerformanceAnimSystem = this.gameObject.AddComponent<AnimancerComponent>();
            APerformancePlayableGraph = APerformanceAnimSystem.Graph;
        }

        private void UnLoadCPerformanceComponent()
        {
            if (StationTemplateManager.instance == null) return;

            int index = StationTemplateManager.instance.GetActorIndexByGameObject(this.gameObject);
            if (index == -1) return;

            StationTemplateManager.instance.ClearSlotHard(index);
            StationTemplateManager.instance.LateToAutoDestroyStationTemplate();
        }

        private void UnLoadAPerformanceComponent()
        {
            var APerformanceAnimSystem = this.gameObject.GetComponent<AnimancerComponent>();
            if (APerformanceAnimSystem != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(APerformanceAnimSystem);
#else
                Destroy(APerformanceAnimSystem);
#endif
            }
        }

        private void UpdateACTransWeight()
        {
            if (!inACPerformanceTrans)
                return;

            if (transA2C)
            {
                if (CPerformanceSyetemWeight < 1)
                {
                    prevCPerformanceSyetemWeight = CPerformanceSyetemWeight;
                    A2CTransInDurationTime += Time.deltaTime;
                    CPerformanceSyetemWeight = AnimTransToolTik.GetBlendTypeWeightFromTime(BlendType.EaseInOut, A2CTransInDurationTime / (ACTransBlendInOutTime <= 0 ? 0.001f : ACTransBlendInOutTime));
                    CPerformanceSyetemWeight = Mathf.Clamp(CPerformanceSyetemWeight, 0, 1);
                }
                SetCPerformancePlayableGraphWeight(CPerformanceSyetemWeight);

                if ((!APerformanceIsRootmotion && CPerformanceIsRootmotion) || (APerformanceIsRootmotion && !CPerformanceIsRootmotion))
                {
                    this.gameObject.transform.position += (CPerformanceSyetemWeight - prevCPerformanceSyetemWeight) * slotPosition;
                    slotRotation.ToAngleAxis(out float angle, out Vector3 axis);
                    this.gameObject.transform.rotation *= Quaternion.AngleAxis((CPerformanceSyetemWeight - prevCPerformanceSyetemWeight) * angle, axis);
                }
                else if (!APerformanceIsRootmotion && !CPerformanceIsRootmotion)
                {
                    this.gameObject.transform.position = Vector3.Lerp(Vector3.zero, slotPosition, CPerformanceSyetemWeight);
                    this.gameObject.transform.rotation = Quaternion.Slerp(Quaternion.identity, slotRotation, CPerformanceSyetemWeight);
                }

                if (CPerformanceSyetemWeight == 1)
                    OnEndTransA2C();
            }

            if (transC2A)
            {
                if (APerformanceSyetemWeight < 1)
                {
                    C2ATransInDurationTime += Time.deltaTime;
                    APerformanceSyetemWeight = AnimTransToolTik.GetBlendTypeWeightFromTime(BlendType.EaseInOut, C2ATransInDurationTime / (ACTransBlendInOutTime <= 0 ? 0.001f : ACTransBlendInOutTime));
                    APerformanceSyetemWeight = Mathf.Clamp(APerformanceSyetemWeight, 0, 1);
                }
                SetAPerformancePlayableGraphWeight(APerformanceSyetemWeight);

                if ((!APerformanceIsRootmotion && CPerformanceIsRootmotion) || (APerformanceIsRootmotion && !CPerformanceIsRootmotion))
                {
                    this.gameObject.transform.position -= (APerformanceSyetemWeight - prevAPerformanceSyetemWeight) * slotPosition;
                    slotRotation.ToAngleAxis(out float angle, out Vector3 axis);
                    this.gameObject.transform.rotation *= Quaternion.AngleAxis(-(APerformanceSyetemWeight - prevAPerformanceSyetemWeight) * angle, axis);
                }
                else if (!APerformanceIsRootmotion && !CPerformanceIsRootmotion)
                {
                    this.gameObject.transform.position = Vector3.Lerp(slotPosition, Vector3.zero, APerformanceSyetemWeight);
                    this.gameObject.transform.rotation = Quaternion.Lerp(slotRotation, Quaternion.identity, APerformanceSyetemWeight);
                }

                if (APerformanceSyetemWeight == 1)
                    OnEndTransC2A();
            }
        }

        private void OnBeginTransA2C()
        {
            UnityEngine.Debug.Log(ZString.Concat(this.gameObject.name, "  OnBeginTransA2C"));
            if (!inACPerformanceTrans)
                return;

            InitA2CTransParam();
        }

        private void OnBeginTransC2A()
        {
            if (!inACPerformanceTrans)
                return;

            LoadAPerformanceComponent();
            InitC2ATransParam();
        }

        private void OnEndTransA2C()
        {
            inACPerformanceTrans = false;
            transA2C = false;
            // 卸载A类动画组件
            UnLoadAPerformanceComponent();
        }

        private void OnEndTransC2A()
        {
            inACPerformanceTrans = false;
            transC2A = false;
            // 卸载C类组件 && 将角色从站为模板中释放出来
            UnLoadCPerformanceComponent();
        }

        public void TransToCPerformance(bool _APerformanceIsRootmotion = true, bool _CPerformanceIsRootmotion = true)
        {
            UnityEngine.Debug.Log(ZString.Concat(this.gameObject.name, "  TransToCPerformance"));
            //UnityEngine.Debug.Break();
            if (inACPerformanceTrans)
                return;

            APerformanceIsRootmotion = _APerformanceIsRootmotion;
            CPerformanceIsRootmotion = _CPerformanceIsRootmotion;

            inACPerformanceTrans = true;
            OnBeginTransA2C();
        }

        public void TransToAPerformance(bool _APerformanceIsRootmotion = true, bool _CPerformanceIsRootmotion = true)
        {
            if (inACPerformanceTrans)
                return;

            APerformanceIsRootmotion = _APerformanceIsRootmotion;
            CPerformanceIsRootmotion = _CPerformanceIsRootmotion;
            inACPerformanceTrans = true;
            OnBeginTransC2A();
        }

        /////////////////////////////////////////////////////////////////////////
        ///////////////////////////// Catch Pose ////////////////////////////////
        /////////////////////////////////////////////////////////////////////////
        public GameObject CatchPose(StationTemplatePostureType postureType, AnimationClip custromAnimClip, float cutstomAnimTime, Func<GameObject, GameObject> func)
        {
            if (animTransPlayableSystem != null && animTransPlayableSystem.AnimTransSystemIsWorking() || func != null)
            {
                return animTransPlayableSystem.ExecuteCatchPose(postureType, custromAnimClip, cutstomAnimTime, func);
            }
            return null;
        }


        /////////////////////////////////////////////////////////////////////////
        /////////////////////////// 坐姿IK纠正相关 //////////////////////////////
        /////////////////////////////////////////////////////////////////////////

        public void SetNewSitPelvisHeightOffset(float heightOffset, float blendTime, bool useIkFoot)
        {
            InitSitPelvisHeightOffsetParam();
            //prevPelvisHeightOffset = pelvisHeightOffset;
            pelvisHeightOffset = heightOffset;
            pelvisHeightBlendTime = blendTime <= 0 ? 0.0001f : blendTime;
            useIkFootOffset = useIkFoot;
            //updatePelvisOffset = true;
        }

        private void InitSitPelvisHeightOffsetParam()
        {
            if (rootBone == null)
                rootBone = this.gameObject.transform.Find("Root");
            transPelvisHeightDuration = 0.0f;
        }

        private void UpdateActorPelvisOffset()
        {
            if (transPelvisHeightDuration < pelvisHeightBlendTime)
            {
                transPelvisHeightDuration += Time.deltaTime;
                //prevRealPelvisHeightOffset = realPelvisHeightOffset;
                realPelvisHeightOffset = Mathf.Lerp(realPelvisHeightOffset, pelvisHeightOffset, AnimTransToolTik.GetBlendTypeWeightFromTime(BlendType.EaseInOut, transPelvisHeightDuration / pelvisHeightBlendTime));
                //this.gameObject.transform.position += new Vector3(0, realPelvisHeightOffset - prevRealPelvisHeightOffset, 0);
            }
            if (rootBone != null)
            {
                rootBone.position += new Vector3(0, realPelvisHeightOffset, 0);
                canUpdateFootOffset = true;
            }
        }

        private void UpdateActorFootOffset()
        {
            if (animator == null) return;
            if (canUpdateFootOffset && useIkFootOffset)
            {
                var footOffset = -realPelvisHeightOffset < 0 ? 0 : -realPelvisHeightOffset;
                animator.SetIKPosition(AvatarIKGoal.RightFoot, animator.GetIKPosition(AvatarIKGoal.RightFoot) + new Vector3(0, footOffset, 0));
                animator.SetIKPosition(AvatarIKGoal.LeftFoot, animator.GetIKPosition(AvatarIKGoal.LeftFoot) + new Vector3(0, footOffset, 0));
                animator.SetIKPositionWeight(AvatarIKGoal.RightFoot, 1);
                animator.SetIKPositionWeight(AvatarIKGoal.LeftFoot, 1);
            }
            canUpdateFootOffset = false;
        }


        /////////////////////////////////////////////////////////////////////////
        /////////////////////////// 自动化防穿相关 //////////////////////////////
        /////////////////////////////////////////////////////////////////////////

        public void SetNewArmPosition(Vector3 rightHandPosition, Vector3 leftHandPosition, Vector3 rightElbowPosition, Vector3 leftElbowPosition)
        {
            if (armPositionInit)
            {
                rightHandPrevPosition = new Vector3((int)XRange.y, (int)YRange.x + 1, 0);
                leftHandPrevPosition = new Vector3((int)XRange.x, (int)YRange.x + 1, 0);
                rightElbowPrevPosition = new Vector3((int)XRange.y - 1, (int)YRange.x + 3, 0);
                leftElbowPrevPosition = new Vector3((int)XRange.x + 1, (int)YRange.x + 3, 0);

                armPositionInit = false;
            }
            else
            {
                rightHandPrevPosition = rightHandCurrectPosition;
                leftHandPrevPosition = leftHandCurrectPosition;
                rightElbowPrevPosition = rightElbowCurrectPosition;
                leftElbowPrevPosition = leftElbowCurrectPosition;
            }

            if (mirrorAnim)
            {
                rightHandCurrectPosition = GetMirrorPosition(leftHandPosition);
                leftHandCurrectPosition = GetMirrorPosition(rightHandPosition);
                rightElbowCurrectPosition = GetMirrorPosition(leftElbowPosition);
                leftElbowCurrectPosition = GetMirrorPosition(rightElbowPosition);
            }
            else
            {
                rightHandCurrectPosition = rightHandPosition;
                leftHandCurrectPosition = leftHandPosition;
                rightElbowCurrectPosition = rightElbowPosition;
                leftElbowCurrectPosition = leftElbowPosition;
            }

            if (transRiskCalculateOptimizeArmApproachingPose)
            {
                HandRelativeArmPosition[] twoHandRelativeArmPosition = CalculateCurrentHandRelativeArmPosition(rightHandCurrectPosition, rightElbowCurrectPosition, leftHandCurrectPosition, leftElbowCurrectPosition);
                rightHandPrevRelativePosition = rightHandCurrentRelativePosition;
                leftHandPrevRelativePosition = leftHandCurrentRelativePosition;
                rightHandCurrentRelativePosition = twoHandRelativeArmPosition[0];
                leftHandCurrentRelativePosition = twoHandRelativeArmPosition[1];
            }
        }

        private Vector3 GetMirrorPosition(Vector3 oriPositon)
        {
            return new Vector3(-oriPositon.x, oriPositon.y, oriPositon.z);
        }

        private float CalculateArmCrossPossibility(int stepNum, int spaceXLenght, int spaceYLenght, int spaceZLenght, int xMin, int yMin, int zMin, bool useDebug)
        {
            if (transRiskCalculateOptimizeArmApproachingPose)
                if (!CalculateArmCrossWithRelativePosition())
                    return 0;

            return CalculateSurfaceCrossProbability(rightHandCurrectPosition, rightElbowCurrectPosition, leftHandCurrectPosition, leftElbowCurrectPosition, rightHandPrevPosition, rightElbowPrevPosition, leftHandPrevPosition, leftElbowPrevPosition, stepNum, spaceXLenght, spaceYLenght, spaceZLenght, xMin, yMin, zMin, useDebug);
        }

        private float CalculateArmCrossPossibility(Vector3 rightHand, Vector3 rightElbow, Vector3 leftHand, Vector3 leftElbow, int stepNum, int spaceXLenght, int spaceYLenght, int spaceZLenght, int xMin, int yMin, int zMin, bool useDebug)
        {
            if (transRiskCalculateOptimizeArmApproachingPose)
                if (!CalculateArmCrossWithRelativePosition(rightHand, rightElbow, leftHand, leftElbow))
                    return 0;

            return CalculateSurfaceCrossProbability(rightHand, rightElbow, leftHand, leftElbow, rightHandPrevPosition, rightElbowPrevPosition, leftHandPrevPosition, leftElbowPrevPosition, stepNum, spaceXLenght, spaceYLenght, spaceZLenght, xMin, yMin, zMin, useDebug);
        }

        private float CalculateSurfaceCrossProbability(Vector3 rightHandCurrent, Vector3 rightElbowCurrent, Vector3 leftHandCurrent, Vector3 leftElbowCurrent, Vector3 rightHandPrev, Vector3 rightElbowPrev, Vector3 leftHandPrev, Vector3 leftElbowPrev, int stepNum, int spaceXLenght, int spaceYLenght, int spaceZLenght, int xMin, int yMin, int zMin, bool useDebug)
        {
            stepNum = stepNum >= 2 ? stepNum : 2;
            if (!((rightHandCurrent == Vector3.zero && leftHandCurrent == Vector3.zero && rightElbowCurrent == Vector3.zero && leftElbowCurrent == Vector3.zero) || (rightHandPrev == Vector3.zero && leftHandPrev == Vector3.zero && rightElbowPrev == Vector3.zero && leftElbowPrev == Vector3.zero)))
            {
                float[,,] voxelSpaceWithTime = new float[spaceZLenght, spaceYLenght, spaceXLenght];
                float crossStrength = 0;
                for (int i = 0; i < stepNum; i++)
                {
                    if (!(transRiskCalculateThrowHeadTailAway && (i == 0 || i == stepNum - 1)))
                    {
                        Vector3 rightHandLerpPosition = new Vector3(
                            Mathf.Lerp(rightHandPrev.x, rightHandCurrent.x, ((float)i) / (stepNum - 1)),
                            Mathf.Lerp(rightHandPrev.y, rightHandCurrent.y, ((float)i) / (stepNum - 1)),
                            Mathf.Lerp(rightHandPrev.z, rightHandCurrent.z, ((float)i) / (stepNum - 1)));
                        Vector3 rightElbowLerpPosition = new Vector3(
                            Mathf.Lerp(rightElbowPrev.x, rightElbowCurrent.x, ((float)i) / (stepNum - 1)),
                            Mathf.Lerp(rightElbowPrev.y, rightElbowCurrent.y, ((float)i) / (stepNum - 1)),
                            Mathf.Lerp(rightElbowPrev.z, rightElbowCurrent.z, ((float)i) / (stepNum - 1)));
                        Vector3 leftHandLerpPosition = new Vector3(
                            Mathf.Lerp(leftHandPrev.x, leftHandCurrent.x, ((float)i) / (stepNum - 1)),
                            Mathf.Lerp(leftHandPrev.y, leftHandCurrent.y, ((float)i) / (stepNum - 1)),
                            Mathf.Lerp(leftHandPrev.z, leftHandCurrent.z, ((float)i) / (stepNum - 1)));
                        Vector3 leftElbowLerpPosition = new Vector3(
                            Mathf.Lerp(leftElbowPrev.x, leftElbowCurrent.x, ((float)i) / (stepNum - 1)),
                            Mathf.Lerp(leftElbowPrev.y, leftElbowCurrent.y, ((float)i) / (stepNum - 1)),
                            Mathf.Lerp(leftElbowPrev.z, leftElbowCurrent.z, ((float)i) / (stepNum - 1)));
                        float[,,] rightArmCrossVoxel = CalculateCrossVoxel(rightElbowLerpPosition, rightHandLerpPosition, spaceXLenght, spaceYLenght, spaceZLenght, xMin, yMin, zMin);
                        float[,,] leftArmCrossVoxel = CalculateCrossVoxel(leftElbowLerpPosition, leftHandLerpPosition, spaceXLenght, spaceYLenght, spaceZLenght, xMin, yMin, zMin);

                        for (int m = 0; m < spaceXLenght; m++)
                        {
                            for (int n = 0; n < spaceYLenght; n++)
                            {
                                for (int k = 0; k < spaceZLenght; k++)
                                {
                                    if (useDebug && i <= debugStep)
                                    {
                                        if (rightArmCrossVoxel[k, n, m] * leftArmCrossVoxel[k, n, m] > voxelSpaceWithTime[k, n, m])
                                        {
                                            debugObject.SetVoxelColor(new Vector3(m + xMin, n + yMin, k + zMin), Color.Lerp(Color.black, Color.Lerp(Color.green, Color.red, ((float)i) / (stepNum - 1)), Mathf.Pow(Mathf.Clamp01((rightArmCrossVoxel[k, n, m] * leftArmCrossVoxel[k, n, m]) / 2.5f), 2)));
                                        }
                                        else
                                        {
                                            if (voxelSpaceWithTime[k, n, m] < 0.1f)
                                            {
                                                debugObject.SetVoxelColor(new Vector3(m + xMin, n + yMin, k + zMin), Color.black);
                                            }
                                        }
                                    }
                                    voxelSpaceWithTime[k, n, m] = MathF.Max(voxelSpaceWithTime[k, n, m], rightArmCrossVoxel[k, n, m] * leftArmCrossVoxel[k, n, m]);
                                }
                            }
                        }
                    }
                    else
                    {
                        if (useDebug && i == 0 && transRiskCalculateThrowHeadTailAway)
                        {
                            debugObject.BlackAllVoxel();
                        }
                    }
                }

                int voxelNum = 0;
                for (int m = 0; m < spaceXLenght; m++)
                {
                    for (int n = 0; n < spaceYLenght; n++)
                    {
                        for (int k = 0; k < spaceZLenght; k++)
                        {
                            if (voxelSpaceWithTime[k, n, m] > 0.125f)
                            {
                                crossStrength += voxelSpaceWithTime[k, n, m];
                                voxelNum++;
                            }
                        }
                    }
                }
                if (voxelNum == 0)
                    voxelNum = 1;
                crossStrength = ((crossStrength / stepNum) / voxelNum) * 10;
                return MathF.Pow(Mathf.Clamp01(crossStrength), 0.7f);
            }
            else
            {
                return 1f;
            }
        }

        private float[,,] CalculateCrossVoxel(Vector3 start, Vector3 end, int spaceXLenght, int spaceYLenght, int spaceZLenght, int xMin, int yMin, int zMin)
        {
            float[,,] voxelSpace = new float[spaceZLenght, spaceYLenght, spaceXLenght];
            float[,,] blurWeight = new float[3, 3, 3] {
            {{0.037f, 0.111f, 0.037f},{0.111f, 0.333f, 0.111f},{0.037f, 0.111f, 0.037f}},
            {{0.111f, 0.333f, 0.111f},{0.333f, 1.000f, 0.333f},{0.111f, 0.333f, 0.111f}},
            {{0.037f, 0.111f, 0.037f},{0.111f, 0.333f, 0.111f},{0.037f, 0.111f, 0.037f}}};
            //float[,,] blurWeight = new float[3, 3, 3] {
            //    {{0.000f, 0.000f, 0.000f},{0.000f, 0.000f, 0.000f},{0.000f, 0.000f, 0.000f}},
            //    {{0.000f, 0.000f, 0.000f},{0.000f, 1.000f, 0.000f},{0.000f, 0.000f, 0.000f}},
            //    {{0.000f, 0.000f, 0.000f},{0.000f, 0.000f, 0.000f},{0.000f, 0.000f, 0.000f}}};
            void updateVoxelSpace(int x_temp, int y_temp, int z_temp)
            {
                for (int m = -1; m <= 1; m++)
                {
                    for (int n = -1; n <= 1; n++)
                    {
                        for (int k = -1; k <= 1; k++)
                        {
                            if ((z_temp + k) >= 0 && (z_temp + k) < spaceZLenght && (y_temp + n) >= 0 && (y_temp + n) < spaceYLenght && (x_temp + m) >= 0 && (x_temp + m) < spaceXLenght)
                            {
                                voxelSpace[z_temp + k, y_temp + n, x_temp + m] += blurWeight[k + 1, n + 1, m + 1];
                            }
                        }
                    }
                }
            }

            if ((Mathf.Abs(start.x - end.x) >= Mathf.Abs(start.y - end.y)) && (Mathf.Abs(start.x - end.x) >= Mathf.Abs(start.z - end.z)))
            {
                float ky = (start.y - end.y) / (start.x - end.x);
                float kz = (start.z - end.z) / (start.x - end.x);
                float y = start.y;
                float z = start.z;
                if (start.x >= end.x)
                {
                    for (float i = start.x; i >= end.x; i--)
                    {
                        int xTemp = (int)i - xMin;
                        int yTemp = Mathf.RoundToInt(y) - yMin;
                        int zTemp = Mathf.RoundToInt(z) - zMin;
                        updateVoxelSpace(xTemp, yTemp, zTemp);
                        y -= ky;
                        z -= kz;
                    }
                }
                else
                {
                    for (float i = start.x; i <= end.x; i++)
                    {
                        int xTemp = (int)i - xMin;
                        int yTemp = Mathf.RoundToInt(y) - yMin;
                        int zTemp = Mathf.RoundToInt(z) - zMin;
                        updateVoxelSpace(xTemp, yTemp, zTemp);
                        y += ky;
                        z += kz;
                    }
                }
            }
            else if ((Mathf.Abs(start.y - end.y) >= Mathf.Abs(start.x - end.x)) && (Mathf.Abs(start.y - end.y) >= Mathf.Abs(start.z - end.z)))
            {
                float kx = (start.x - end.x) / (start.y - end.y);
                float kz = (start.z - end.z) / (start.y - end.y);
                float x = start.x;
                float z = start.z;
                if (start.y >= end.y)
                {
                    for (float i = start.y; i >= end.y; i--)
                    {
                        int xTemp = Mathf.RoundToInt(x) - xMin;
                        int yTemp = (int)i - yMin;
                        int zTemp = Mathf.RoundToInt(z) - zMin;
                        updateVoxelSpace(xTemp, yTemp, zTemp);
                        x -= kx;
                        z -= kz;
                    }
                }
                else
                {
                    for (float i = start.y; i <= end.y; i++)
                    {
                        int xTemp = Mathf.RoundToInt(x) - xMin;
                        int yTemp = (int)i - yMin;
                        int zTemp = Mathf.RoundToInt(z) - zMin;
                        updateVoxelSpace(xTemp, yTemp, zTemp);
                        x += kx;
                        z += kz;
                    }
                }
            }
            else
            {
                float kx = (start.x - end.x) / (start.z - end.z);
                float ky = (start.y - end.y) / (start.z - end.z);
                float x = start.x;
                float y = start.y;
                if (start.z >= end.z)
                {
                    for (float i = start.z; i >= end.z; i--)
                    {
                        int xTemp = Mathf.RoundToInt(x) - xMin;
                        int yTemp = Mathf.RoundToInt(y) - yMin;
                        int zTemp = (int)i - zMin;
                        updateVoxelSpace(xTemp, yTemp, zTemp);
                        x -= kx;
                        y -= ky;
                    }
                }
                else
                {
                    for (float i = start.z; i <= end.z; i++)
                    {
                        int xTemp = Mathf.RoundToInt(x) - xMin;
                        int yTemp = Mathf.RoundToInt(y) - yMin;
                        int zTemp = (int)i - zMin;
                        updateVoxelSpace(xTemp, yTemp, zTemp);
                        x += kx;
                        y += ky;
                    }
                }
            }

            return voxelSpace;
        }

        private HandRelativeArmPosition[] CalculateCurrentHandRelativeArmPosition(Vector3 rightHand, Vector3 rightElbow, Vector3 leftHand, Vector3 leftElbow)
        {
            Vector3 leftArmTargetVector = new Vector3(1f, 0f, 0f);
            Vector3 leftArmVector = Vector3.Normalize(leftHand - leftElbow);
            Vector3 rightHandWorldPosition = rightHand - leftElbow;
            Quaternion leftArmRotateQuat = Quaternion.FromToRotation(leftArmVector, leftArmTargetVector);
            Matrix4x4 leftArmRot = new Matrix4x4();
            leftArmRot.SetTRS(new Vector3(0, 0, 0), leftArmRotateQuat, new Vector3(1, 1, 1));
            Vector3 rightHandRelativePosition = leftArmRot * rightHandWorldPosition;

            HandRelativeArmPosition rightHandRelativeArmPosition = new HandRelativeArmPosition();
            rightHandRelativeArmPosition.far = MathF.Abs(rightHandRelativePosition.y) >= 2.0f || MathF.Abs(rightHandRelativePosition.z) >= 2.0f;
            rightHandRelativeArmPosition.cover = MathF.Abs(rightHandRelativePosition.y) < 1f && MathF.Abs(rightHandRelativePosition.z) < 1f;
            rightHandRelativeArmPosition.up = rightHandRelativePosition.y > 0f;
            rightHandRelativeArmPosition.front = rightHandRelativePosition.z > 0f;
            rightHandRelativeArmPosition.inner = rightHandRelativePosition.x > -1f;

            Vector3 rightArmTargetVector = new Vector3(-1f, 0f, 0f);
            Vector3 rightArmVector = Vector3.Normalize(rightHand - rightElbow);
            Vector3 leftHandWorldPosition = leftHand - rightElbow;
            Quaternion rightArmRotateQuat = Quaternion.FromToRotation(rightArmVector, rightArmTargetVector);
            Matrix4x4 rightArmRot = new Matrix4x4();
            rightArmRot.SetTRS(new Vector3(0, 0, 0), rightArmRotateQuat, new Vector3(1, 1, 1));
            Vector3 leftHandRelativePosition = rightArmRot * leftHandWorldPosition;

            HandRelativeArmPosition leftHandRelativeArmPosition = new HandRelativeArmPosition();
            leftHandRelativeArmPosition.far = MathF.Abs(leftHandRelativePosition.y) >= 2.0f || MathF.Abs(leftHandRelativePosition.z) >= 2.0f;
            leftHandRelativeArmPosition.cover = MathF.Abs(leftHandRelativePosition.y) < 1f && MathF.Abs(leftHandRelativePosition.z) < 1f;
            leftHandRelativeArmPosition.up = leftHandRelativePosition.y > 0f;
            leftHandRelativeArmPosition.front = leftHandRelativePosition.z > 0f;
            leftHandRelativeArmPosition.inner = leftHandRelativePosition.x < 1f;

            return new HandRelativeArmPosition[2] { rightHandRelativeArmPosition, leftHandRelativeArmPosition };
        }

        private bool CalculateArmCrossWithRelativePosition()
        {
            bool upDircLayer = (rightHandCurrentRelativePosition.up == rightHandPrevRelativePosition.up) && (leftHandCurrentRelativePosition.up == leftHandPrevRelativePosition.up) && (rightHandCurrentRelativePosition.up != leftHandCurrentRelativePosition.up);
            bool frontDircLayer = (rightHandCurrentRelativePosition.front == rightHandPrevRelativePosition.front) && (leftHandCurrentRelativePosition.front == leftHandPrevRelativePosition.front) && (rightHandCurrentRelativePosition.front != leftHandCurrentRelativePosition.front);
            bool handInner = rightHandCurrentRelativePosition.inner && leftHandCurrentRelativePosition.inner && rightHandPrevRelativePosition.inner && leftHandPrevRelativePosition.inner;
            //bool rightBase = (rightHandCurrentRelativePosition.up == rightHandPrevRelativePosition.up) || (rightHandCurrentRelativePosition.front == rightHandPrevRelativePosition.front);
            //bool leftBase = (leftHandCurrentRelativePosition.up == leftHandPrevRelativePosition.up) || (leftHandCurrentRelativePosition.front == leftHandPrevRelativePosition.front);
            //bool armCross = (rightHandCurrentRelativePosition.up == leftHandPrevRelativePosition.up) && (rightHandCurrentRelativePosition.front == leftHandPrevRelativePosition.front);
            //bool handCover = rightHandCurrentRelativePosition.cover && leftHandCurrentRelativePosition.cover && rightHandPrevRelativePosition.cover && leftHandPrevRelativePosition.cover;
            //bool handFar = (rightHandCurrentRelativePosition.far && leftHandCurrentRelativePosition.far) || (rightHandPrevRelativePosition.far && leftHandPrevRelativePosition.far);
            //if ((rightBase && leftBase) && (!armCross) && (handCover || (!handFar)))
            if (upDircLayer || (frontDircLayer && handInner))
                return false;
            else return true;
        }

        private bool CalculateArmCrossWithRelativePosition(Vector3 rightHand, Vector3 rightElbow, Vector3 leftHand, Vector3 leftElbow)
        {
            HandRelativeArmPosition[] twoHandRelativeArmPosition = CalculateCurrentHandRelativeArmPosition(rightHand, rightElbow, leftHand, leftElbow);

            bool upDircLayer = (twoHandRelativeArmPosition[0].up == rightHandPrevRelativePosition.up) && (twoHandRelativeArmPosition[1].up == leftHandPrevRelativePosition.up) && (twoHandRelativeArmPosition[0].up != twoHandRelativeArmPosition[1].up);
            bool frontDircLayer = (twoHandRelativeArmPosition[0].front == rightHandPrevRelativePosition.front) && (twoHandRelativeArmPosition[1].front == leftHandPrevRelativePosition.front) && (twoHandRelativeArmPosition[0].front != twoHandRelativeArmPosition[1].front);
            bool handInner = twoHandRelativeArmPosition[0].inner && twoHandRelativeArmPosition[1].inner && rightHandPrevRelativePosition.inner && leftHandPrevRelativePosition.inner;
            //bool rightBase = (twoHandRelativeArmPosition[0].up == rightHandPrevRelativePosition.up) || (twoHandRelativeArmPosition[0].front == rightHandPrevRelativePosition.front);
            //bool leftBase = (twoHandRelativeArmPosition[1].up == leftHandPrevRelativePosition.up) || (twoHandRelativeArmPosition[1].front == leftHandPrevRelativePosition.front);
            //bool armCross = (twoHandRelativeArmPosition[0].up == leftHandPrevRelativePosition.up) && (twoHandRelativeArmPosition[0].front == leftHandPrevRelativePosition.front);
            //bool handCover = twoHandRelativeArmPosition[0].cover && twoHandRelativeArmPosition[1].cover && rightHandPrevRelativePosition.cover && leftHandPrevRelativePosition.cover;
            //bool handFar = (twoHandRelativeArmPosition[0].far && twoHandRelativeArmPosition[1].far) || (rightHandPrevRelativePosition.far && leftHandPrevRelativePosition.far);
            //if ((rightBase && leftBase) && (!armCross) && (handCover || (!handFar)))
            if (upDircLayer || (frontDircLayer && handInner))
                return false;
            else return true;
        }

        private void DrawDebug()
        {
            debugStep = Mathf.Clamp(debugStep, 0, 9);
            if (debugStep != prevDebugStep || debug != prevDebug || showDebugStep != prevShowDebugStep)
            {
                if (debug)
                {
                    if (showDebugStep)
                    {
                        float debugBlendInTime = Mathf.Lerp(TransTimeRange.y, TransTimeRange.x, CalculateArmCrossPossibility(fullAutoTransCheckStep, (int)(XRange.y - XRange.x + 1), (int)(YRange.y - YRange.x + 1), (int)(ZRange.y - ZRange.x + 1), (int)(XRange.x), (int)(YRange.x), (int)(ZRange.x), debug));
                        animTransPlayableSystem.SetDebugState(true, true, fullAutoTransCheckStep, debugStep);
                    }
                    else
                    {
                        debugStep = fullAutoTransCheckStep - 1;
                        animTransPlayableSystem.SetDebugState(true, false, fullAutoTransCheckStep, debugStep);
                    }
                }
                else
                {
                    if (prevDebug)
                    {
                        debugStep = fullAutoTransCheckStep - 1;
                        animTransPlayableSystem.SetDebugState(false, false, fullAutoTransCheckStep, debugStep);
                        debugObject.BlackAllVoxel();
                    }
                }
            }

            if (showGrid != prevShowGrid)
            {
                if (LeftGrid != null && FrontGrid != null)
                {
                    if (showGrid)
                    {
                        LeftGrid.SetActive(true);
                        FrontGrid.SetActive(true);
                    }
                    else
                    {
                        LeftGrid.SetActive(false);
                        FrontGrid.SetActive(false);
                    }
                }
            }
            if (viewLeft != prevViewLeft)
            {
                if (CameraHolder != null)
                {
                    if (viewLeft)
                        CameraHolder.transform.rotation = Quaternion.Euler(0f, 90f, 0f);
                    else
                        CameraHolder.transform.rotation = Quaternion.Euler(0f, 0f, 0f);
                }
            }
            prevDebugStep = debugStep; prevShowDebugStep = showDebugStep; prevDebug = debug; prevShowGrid = showGrid; prevViewLeft = viewLeft;
        }

        private void AutoCalculateTags()
        {
            if (autoCalculateTags)
            {
                animTransPlayableSystem.SetDebugState(true, true, 10, 9);
                Vector3 rightHandPositionTemp = new Vector3(XRange.x, YRange.x, ZRange.x);
                Vector3 leftHandPositionTemp = new Vector3(XRange.x, YRange.x, ZRange.x);
                Vector3 rightElbowPositionTemp = new Vector3(XRange.x, YRange.x, ZRange.x);
                Vector3 leftElbowPositionTemp = new Vector3(XRange.x, YRange.x, ZRange.x);
                bool[] flags = new bool[12];

                for (int x = 0; x < XClipLine.Length; x++)
                {
                    if (rightHandPosition.position.x < XClipLine[x] && !flags[0])
                    {
                        rightHandPositionTemp.x = XRange.y - x;
                        flags[0] = true;
                    }
                    if (leftHandPosition.position.x < XClipLine[x] && !flags[1])
                    {
                        leftHandPositionTemp.x = XRange.y - x;
                        flags[1] = true;
                    }
                    if (rightElbowPosition.position.x < XClipLine[x] && !flags[2])
                    {
                        rightElbowPositionTemp.x = XRange.y - x;
                        flags[2] = true;
                    }
                    if (leftElbowPosition.position.x < XClipLine[x] && !flags[3])
                    {
                        leftElbowPositionTemp.x = XRange.y - x;
                        flags[3] = true;
                    }
                }
                for (int y = 0; y < YClipLine.Length; y++)
                {
                    if (rightHandPosition.position.y > YClipLine[y] && !flags[4])
                    {
                        rightHandPositionTemp.y = YRange.y - y;
                        flags[4] = true;
                    }
                    if (leftHandPosition.position.y > YClipLine[y] && !flags[5])
                    {
                        leftHandPositionTemp.y = YRange.y - y;
                        flags[5] = true;
                    }
                    if (rightElbowPosition.position.y > YClipLine[y] && !flags[6])
                    {
                        rightElbowPositionTemp.y = YRange.y - y;
                        flags[6] = true;
                    }
                    if (leftElbowPosition.position.y > YClipLine[y] && !flags[7])
                    {
                        leftElbowPositionTemp.y = YRange.y - y; flags[7] = true;
                    }
                }
                for (int z = 0; z < ZClipLine.Length; z++)
                {
                    if (rightHandPosition.position.z < ZClipLine[z] && !flags[8])
                    {
                        rightHandPositionTemp.z = ZRange.y - z;
                        flags[8] = true;
                    }
                    if (leftHandPosition.position.z < ZClipLine[z] && !flags[9])
                    {
                        leftHandPositionTemp.z = ZRange.y - z;
                        flags[9] = true;
                    }
                    if (rightElbowPosition.position.z < ZClipLine[z] && !flags[10])
                    {
                        rightElbowPositionTemp.z = ZRange.y - z;
                        flags[10] = true;
                    }
                    if (leftElbowPosition.position.z < ZClipLine[z] && !flags[11])
                    {
                        leftElbowPositionTemp.z = ZRange.y - z;
                        flags[11] = true;
                    }
                }
                if (rightHandPositionTemp == leftElbowPositionTemp)
                {
                    float xOffset = rightHandPosition.position.x - leftElbowPosition.position.x;
                    //xOffset = (xOffset > 0) ? Mathf.Clamp(xOffset - 0.5f, 0, xOffset) : xOffset;
                    float yOffset = rightHandPosition.position.y - leftElbowPosition.position.y;
                    float zOffset = rightHandPosition.position.z - leftElbowPosition.position.z;
                    if (Mathf.Abs(yOffset) >= Mathf.Abs(xOffset) && Mathf.Abs(yOffset) >= Mathf.Abs(zOffset))
                    {
                        if (yOffset >= 0)
                        {
                            if (rightHandPositionTemp.y + 1 <= YRange.y)
                                rightHandPositionTemp.y += 1;
                            else
                                leftElbowPositionTemp.y -= 1;
                        }
                        else
                        {
                            if (rightHandPositionTemp.y - 1 >= YRange.x)
                                rightHandPositionTemp.y -= 1;
                            else
                                leftElbowPositionTemp.y += 1;
                        }
                    }
                    else if (Mathf.Abs(zOffset) >= Mathf.Abs(xOffset) && Mathf.Abs(zOffset) >= Mathf.Abs(yOffset))
                    {
                        if (zOffset >= 0)
                        {
                            if (rightHandPositionTemp.z - 1 >= ZRange.x)
                                rightHandPositionTemp.z -= 1;
                            else
                                leftElbowPositionTemp.z += 1;
                        }
                        else
                        {
                            if (rightHandPositionTemp.z + 1 <= ZRange.y)
                                rightHandPositionTemp.z += 1;
                            else
                                leftElbowPositionTemp.z -= 1;
                        }
                    }
                    else
                    {
                        if (xOffset >= 0)
                        {
                            if (rightHandPositionTemp.x - 1 >= XRange.x)
                                rightHandPositionTemp.x -= 1;
                            else
                                leftElbowPositionTemp.x += 1;
                        }
                        else
                        {
                            if (rightHandPositionTemp.x + 1 <= XRange.y)
                                rightHandPositionTemp.x += 1;
                            else
                                leftElbowPositionTemp.x -= 1;
                        }
                    }
                }
                if (leftHandPositionTemp == rightElbowPositionTemp)
                {
                    float xOffset = leftHandPosition.position.x - rightElbowPosition.position.x;
                    //xOffset = (xOffset < 0) ? Mathf.Clamp(xOffset + 0.5f, xOffset, 0) : xOffset;
                    float yOffset = leftHandPosition.position.y - rightElbowPosition.position.y;
                    float zOffset = leftHandPosition.position.z - rightElbowPosition.position.z;
                    if (Mathf.Abs(yOffset) >= Mathf.Abs(xOffset) && Mathf.Abs(yOffset) >= Mathf.Abs(zOffset))
                    {
                        if (yOffset >= 0)
                        {
                            if (leftHandPositionTemp.y + 1 <= YRange.y)
                                leftHandPositionTemp.y += 1;
                            else
                                rightElbowPositionTemp.y -= 1;
                        }
                        else
                        {
                            if (leftHandPositionTemp.y - 1 >= YRange.x)
                                leftHandPositionTemp.y -= 1;
                            else
                                rightElbowPositionTemp.y += 1;
                        }
                    }
                    else if (Mathf.Abs(zOffset) >= Mathf.Abs(xOffset) && Mathf.Abs(zOffset) >= Mathf.Abs(yOffset))
                    {
                        if (zOffset >= 0)
                        {
                            if (leftHandPositionTemp.z - 1 >= ZRange.x)
                                leftHandPositionTemp.z -= 1;
                            else
                                rightElbowPositionTemp.z += 1;
                        }
                        else
                        {
                            if (leftHandPositionTemp.z + 1 <= ZRange.y)
                                leftHandPositionTemp.z += 1;
                            else
                                rightElbowPositionTemp.z -= 1;
                        }
                    }
                    else
                    {
                        if (xOffset >= 0)
                        {
                            if (leftHandPositionTemp.x - 1 >= XRange.x)
                                leftHandPositionTemp.x -= 1;
                            else
                                rightElbowPositionTemp.x += 1;
                        }
                        else
                        {
                            if (leftHandPositionTemp.x + 1 <= XRange.y)
                                leftHandPositionTemp.x += 1;
                            else
                                rightElbowPositionTemp.x -= 1;
                        }
                    }
                }
            }
            else
            {
                if (prevAutoCalculateTags)
                {
                    animTransPlayableSystem.SetDebugState(false, false, 10, 9);
                }
            }
        }

        private void MasterModeMaster()
        {
            switch (currentMode)
            {
                case MasterMode.AnimTrans:
                    {
                        debug = false;
                        autoCalculateTags = false;
                        break;
                    }
                case MasterMode.Debug:
                    {
                        debug = true;
                        autoCalculateTags = false;
                        break;
                    }
                case MasterMode.AutoTagCalculate:
                    {
                        debug = false;
                        autoCalculateTags = true;
                        break;
                    }
                default:
                    {
                        debug = false;
                        autoCalculateTags = false;
                        break;
                    }
            }
        }

        private void ModifyPlayableGraphStruct()
        {
            if(useFaceBone != prevUseFaceBone)
            {
                animTransPlayableSystem.ModifyPlayableGraphStructFaceBone(useFaceBone);
            }
            prevUseFaceBone = useFaceBone;
        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...
            
#if UNITY_EDITOR
            if (DrawDebugInfo)
            {
                DrawHandIKDebugSphere();
            }
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

