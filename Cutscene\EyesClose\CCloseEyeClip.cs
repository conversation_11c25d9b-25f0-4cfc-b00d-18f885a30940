using System.Collections;
using System.Linq;
using CEditor;
using Sirenix.OdinInspector;
using UnityEngine;
using VGame.Framework;

namespace Slate.ActionClips
{
    [System.Serializable]
    public class CCloseEyeClipParams
    {
        [SerializeField, LabelText("闭眼类型")]
        public EyeOverwriteType closeEyeType = EyeOverwriteType.CompletelyClose;

        [SerializeField, LabelText("自定义闭眼资产")]
        public AnimationClip customCloseEyeAnim;

        [SerializeField, LabelText("闭眼资产时间点")]
        public float customCloseEyeAnimFrameTime = 0;

        [SerializeField, LabelText("闭眼速度")]
        public float eyesCloseBlendInTime = 0.15f;

        [SerializeField, LabelText("睁眼速度")]
        public float eyesCloseBlendOutTime = 0.3f;
    }

    [Name("闭眼配置")]
    [Attachable(typeof(CCloseTrack))]
    [Category("C类编辑器")]
    public class CCloseEyeClip : BaseDialogueActorActionClip<CCloseEyeClipParams>
    {
        private GameObject performer;
        private EyeBlinkManager blinkManager;

        [Ser<PERSON>ize<PERSON>ield, HideInInspector]
        private float _length = 1;

        public override float length
        {
            get { return _length; }
            set { _length = value; }
        }

        public override string info
        {
            get
            {
                switch (GetCfg().closeEyeType)
                {
                    case EyeOverwriteType.CompletelyClose:
                        return "全闭眼";
                    case EyeOverwriteType.HalfClose:
                        return "半闭眼";
                    case EyeOverwriteType.Custom:
                        return "自定义闭眼";
                    case EyeOverwriteType.None:
                        return "无闭眼";
                    default:
                        return "无闭眼";
                }
            }
        }

        protected override void OnCreate()
        {
            base.OnCreate();
        }

        private void SetEnter()
        {
            performer = GetActor();
            blinkManager = performer.GetComponent<EyeBlinkManager>();

            if (performer != null && blinkManager != null)
            {
                blinkManager.closeEyesType = GetCfg().closeEyeType;
                blinkManager.eyesCloseBlendInTime = GetCfg().eyesCloseBlendInTime;
                blinkManager.eyesCloseBlendOutTime = GetCfg().eyesCloseBlendOutTime;
                if (GetCfg().closeEyeType == EyeOverwriteType.Custom)
                {
                    blinkManager.customBlinkAnim = GetCfg().customCloseEyeAnim;
                    blinkManager.customBlinkAnimTime = GetCfg().customCloseEyeAnimFrameTime;
                }
            }
        }

        //Called in forward sampling when the clip is entered
        protected override void OnEnter()
        {
            base.OnEnter();
            SetEnter();
        }

        //Called in backwards sampling when the clip is entered.
        protected override void OnReverseEnter()
        {
            base.OnReverseEnter();
            SetEnter();
        }

        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime)
        {
            if (performer != null && blinkManager != null)
            {
                blinkManager.useKeepCloseEyesControl = true;
                blinkManager.keepClosingEyesTrigger = true;
            }
        }

        //Called in forwards sampling when the clip exits
        protected override void OnExit() 
        {

        }

        //Called in backwards sampling when the clip exits.
        protected override void OnReverse() { }
    }
}