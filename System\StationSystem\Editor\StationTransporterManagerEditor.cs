#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using UnityEditor.Formats.Fbx.Exporter;
using System.IO;
using CEditor;
using Cinemachine;
using Cysharp.Text;
using System.Collections.Generic;
using VGame.Framework;
using System.Linq;

[CustomEditor(typeof(StationTransporterManager))]
public class StationTransporterManagerEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        StationTransporterManager mgr = (StationTransporterManager)target;

        EditorGUILayout.Space();
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.PrefixLabel("保存路径");
        mgr.SavePath = EditorGUILayout.TextField(mgr.SavePath);
        if (GUILayout.Button("浏览", GUILayout.Width(60)))
        {
            string path = EditorUtility.OpenFolderPanel("选择保存路径", "Assets", "");
            if (!string.IsNullOrEmpty(path))
            {
                // 将完整路径转换为相对于项目的路径
                string projectPath = Path.GetFullPath(Application.dataPath);
                mgr.SavePath = path;
            }
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("转换为C类模板并保存为Prefab"))
        {
            CreateAndSavePrefab(mgr);
        }
        
        if (GUILayout.Button("转换为FBX并保存"))
        {
            CreateAndSaveFbx(mgr);
        }
        EditorGUILayout.EndHorizontal();

    }

    private void CreateAndSaveFbx(StationTransporterManager mgr)
    {
         // 创建一个父对象来包含所有空对象
        GameObject parentObj = new GameObject("StationTransporter");
        // 创建ActorGroup和CameraGroup
        GameObject actorGroup = new GameObject("ActorGroup");
        actorGroup.transform.SetParent(parentObj.transform);
        GameObject cameraGroup = new GameObject("CameraRoot");
        cameraGroup.transform.SetParent(parentObj.transform);

        // 为每个Actor创建空对象
        int actorIndex = 0;
        foreach (var actor in mgr.Actors)
        {
            if (actor == null) continue;
            string actorName = ZString.Format("Actor_{0}",actor.name);
            GameObject emptyObj = new GameObject(actorName);
            emptyObj.transform.SetParent(actorGroup.transform);
            emptyObj.transform.position = actor.position;
            emptyObj.transform.rotation = actor.rotation;
            // emptyObj.transform.localScale = actor.localScale;
            actorIndex++;
        }

        // 为每个Camera创建空对象
        foreach (var camera in mgr.Cameras)
        {
            if (camera == null) continue;
            string cameraName = ZString.Format("Camera_{0}",camera.name);
            GameObject emptyObj = new GameObject(cameraName);
            emptyObj.transform.SetParent(cameraGroup.transform);
            emptyObj.transform.position = camera.position;
            emptyObj.transform.rotation = camera.rotation;
            // emptyObj.transform.localScale = camera.localScale;
            var originalCamera = camera.GetComponent<Camera>();
            if (originalCamera != null)
            {
                Camera cam = emptyObj.AddComponent<Camera>();
                EditorUtility.CopySerialized(originalCamera, cam);
            }
        }

        string dateTimeString = System.DateTime.Now.ToString("yyyy_MM_dd_HH_mm_ss");
        mgr.SaveFileName = ZString.Format("StationTransporter_{0}", dateTimeString);
        // 保存为FBX
        string savePath = mgr.SavePath;
        string fileName = string.IsNullOrEmpty(mgr.SaveFileName) ? "StationTransporter" : mgr.SaveFileName;
        string fbxPath = ZString.Format("{0}/{1}.fbx", savePath, fileName);
        fbxPath = AssetDatabase.GenerateUniqueAssetPath(fbxPath);
        // parentObjs 是parentObj和其下所有对象
        List<GameObject> parentObjs = new List<GameObject>();
        parentObjs.Add(parentObj);
        foreach (Transform child in parentObj.GetComponentsInChildren<Transform>(true))
        {
            if (child != parentObj.transform)
                parentObjs.Add(child.gameObject);
        }
        ModelExporter.ExportObjects(fbxPath, parentObjs.ToArray());
        
        // 清理场景中的临时对象
        DestroyImmediate(parentObj);
        
        AssetDatabase.Refresh();
        Debug.Log(ZString.Format("FBX已保存到: {0}", mgr.SavePath));
    }

    private void CreateAndSavePrefab(StationTransporterManager mgr)
    {
        // 创建一个父对象来包含所有空对象
        GameObject parentObj = new GameObject("StationTransporter");
        // 创建ActorGroup和CameraGroup
        GameObject actorGroup = new GameObject("Actor Group");
        actorGroup.transform.SetParent(parentObj.transform);
        GameObject cameraGroup = new GameObject("Camera Group");
        cameraGroup.transform.SetParent(parentObj.transform);

        // 为每个Actor创建空对象
        int actorIndex = 0;
        foreach (var actor in mgr.Actors)
        {
            if (actor == null) continue;
            string actorName = ((char)('A' + actorIndex)).ToString();
            GameObject emptyObj = new GameObject(actorName);
            emptyObj.transform.SetParent(actorGroup.transform);
            emptyObj.transform.position = actor.position;
            emptyObj.transform.rotation = actor.rotation;
            // emptyObj.transform.localScale = actor.localScale;
            actorIndex++;
        }

        // 为每个Camera组创建Person Camera对象
        int actorCount = mgr.Actors != null ? mgr.Actors.Count : 0;
        string[] personNames = {"Zero", "One", "Two", "Three", "Four", "Five", "Six"};
        string personPrefix = actorCount >= 0 && actorCount < personNames.Length ? ZString.Format("{0}Person", personNames[actorCount]) : ZString.Format("{0}Person", actorCount);
        // 拼接后缀
        System.Text.StringBuilder suffixBuilder = new System.Text.StringBuilder();
        char actorChar = 'A';
        for (int i = 0; i < actorCount && i < 6; i++)
        {
            var actor = mgr.Actors[i];
            if (actor == null) continue;
            suffixBuilder.Append(ZString.Format("{0}{1}({2})", i > 0 ? "" : "#", (char)(actorChar + i), actor.rotation.eulerAngles.y));
        }
        string personCameraName = ZString.Format("{0} Camera_{1}", personPrefix, suffixBuilder);
        GameObject personCameraGroup = new GameObject(personCameraName);
        personCameraGroup.transform.SetParent(cameraGroup.transform);
        mgr.SaveFileName = ZString.Format("{0}-Null_Tags", personPrefix);

        int count = 0;
        // 为每个Camera创建空对象
        foreach (var camera in mgr.Cameras)
        {
            if (camera == null) continue;
            float fov = 0f;
            var originalCamera = camera.GetComponent<Camera>();
            if (originalCamera != null)
            {
                fov = originalCamera.fieldOfView;
            }
            var list = CfgManager.tables.TbCEditorCameraAsset.DataList.Where(x => CCameraTools.GetSlotCountByCameraName(x.UnityName) == actorCount).ToList();
            string camObjName;
            if (list != null)
                camObjName = ZString.Format("Cam{0}_{0}A{1}_SP_A_{2}_Null_0_Null_Null", actorCount, list.Count + 1 + count, fov);
            else
                camObjName = ZString.Format("Cam{0}_{0}A0_SP_A_{1}_Null_0_Null_Null", actorCount, fov);
            GameObject emptyObj = new GameObject(camObjName);
            emptyObj.transform.SetParent(personCameraGroup.transform);
            emptyObj.transform.position = camera.position;
            emptyObj.transform.rotation = camera.rotation;
            // emptyObj.transform.localScale = camera.localScale;
            
            CinemachineVirtualCamera virtualCameraComp = emptyObj.AddComponent<CinemachineVirtualCamera>();
            virtualCameraComp.m_Lens.FieldOfView = fov;

            count++;
        }

        // 确保保存路径存在
        string savePath = mgr.SavePath;
        if (!AssetDatabase.IsValidFolder(savePath))
        {
            string[] pathParts = savePath.Split('/');
            string currentPath = pathParts[0];
            for (int i = 1; i < pathParts.Length; i++)
            {
                string parentPath = currentPath;
                currentPath = ZString.Format("{0}/{1}", currentPath, pathParts[i]);
                if (!AssetDatabase.IsValidFolder(currentPath))
                {
                    AssetDatabase.CreateFolder(parentPath, pathParts[i]);
                }
            }
        }

        // 保存为Prefab
        string fileName = string.IsNullOrEmpty(mgr.SaveFileName) ? "StationTransporterObjects" : mgr.SaveFileName;
        string prefabPath = ZString.Format("{0}/{1}.prefab", savePath, fileName);
        prefabPath = AssetDatabase.GenerateUniqueAssetPath(prefabPath);
        PrefabUtility.SaveAsPrefabAsset(parentObj, prefabPath);
        
        // 清理场景中的临时对象
        DestroyImmediate(parentObj);
        
        AssetDatabase.Refresh();
        Debug.Log(ZString.Format("Prefab已保存到: {0}", prefabPath));
    }
}
#endif