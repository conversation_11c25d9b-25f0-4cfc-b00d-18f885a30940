//using System.Collections;
//using System.Linq;
//using CEditor;
//using Pathfinding;
//using Sirenix.OdinInspector;
//using UnityEngine;
//using VGame.Framework;

//namespace Slate.ActionClips
//{
//    [Name("手持物事件配置")]
//    [Attachable(typeof(CLeftHandheldTrack))]
//    [Category("C类编辑器")]
//    public class CLeftHandheldTriggerClip : BaseDialogueActorActionClip<CHandHeldParams>
//    {
//        private GameObject performer;
//        private HandheldManage handheldManager;

//        [SerializeField, HideInInspector]
//        private float _length = 1;

//        //public override float length
//        //{
//        //    get { return _length; }
//        //    set { _length = value; }
//        //}

//        public override string info
//        {
//            get
//            {
//                return "手持物片段";
//            }
//        }

//        protected override void OnCreate()
//        {
//            base.OnCreate();
//            GetCfg().refBoneName = "Bip01_L_Hand";
//        }

//        private void SetEnter()
//        {
//            performer = GetActor();
//            handheldManager = performer.GetComponent<HandheldManage>();

//            if (performer != null && handheldManager != null)
//            {
//                handheldManager.useLeftHandheldUpdateControl = true;

//                if ((GetCfg().isHoldingObject == false || GetCfg().holdObject == null) && (GetCfg().overwriteHandAnim == false || GetCfg().overwriteAnim == null))
//                    handheldManager.leftHandHoldingMaintainTrigger = false;
//                else
//                {
//                    handheldManager.ClearLeftHandHoldState();

//                    HandHoldState handHoldState = new HandHoldState();
//                    handHoldState.overwriteHandAnim = GetCfg().overwriteHandAnim;
//                    handHoldState.isHoldingObject = GetCfg().isHoldingObject;
//                    handHoldState.useHoldObjectAnim = GetCfg().useHoldObjectAnim;
//                    handHoldState.overwriteAnim = GetCfg().overwriteAnim;
//                    handHoldState.holdObject = GetCfg().holdObject;
//                    handHoldState.holdObjectAnimation = GetCfg().holdObjectAnimation;
//                    handHoldState.refBoneName = GetCfg().refBoneName;
//                    handHoldState.positionOffset = GetCfg().positionOffset;
//                    handHoldState.rotateOffset = GetCfg().rotateOffset;
//                    handHoldState.localscale = GetCfg().localscale;

//                    handheldManager.leftHandheldState = handHoldState;
//                    handheldManager.leftHandHoldingMaintainTrigger = true;
//                }
//            }
//        }

//        //Called in forward sampling when the clip is entered
//        protected override void OnEnter()
//        {
//            base.OnEnter();
//            SetEnter();
//        }

//        //Called in backwards sampling when the clip is entered.
//        protected override void OnReverseEnter()
//        {

//        }

//        //Called per frame while the clip is updating. Time is the local time within the clip.
//        //So a time of 0 means the start of the clip.
//        protected override void OnUpdate(float time, float previousTime)
//        {

//        }

//        //Called in forwards sampling when the clip exits
//        protected override void OnExit() 
//        {

//        }

//        //Called in backwards sampling when the clip exits.
//        protected override void OnReverse() { }
//    }
//}