﻿// --------------------------------------------------------------------------------------------------------------------
// <copyright file="MonoSingleton.cs" company="Tencent">
//   
// </copyright>
// <summary>
//   AutoSingletonAttribute是用来防止单件的对象实际被new了多个的时候，毕竟这是个Mono的组件，实例化多次也是可能的
//   MonoSingleton是所有mono单件的父类，用模版来实现，只要具体的类型派生于此并将自身作为模版参数即可
// </summary>
// --------------------------------------------------------------------------------------------------------------------
namespace CEditor
{
    using System;
    using UnityEngine;

    /// <summary>
    /// The auto singleton attribute.
    /// </summary>
    public class AutoSingletonAttribute : Attribute
    {
        /// <summary>
        /// The b auto create.
        /// </summary>
        public bool bAutoCreate;

        /// <summary>
        /// Initializes a new instance of the <see cref="AutoSingletonAttribute"/> class.
        /// </summary>
        /// <param name="bCreate">
        /// The b create.
        /// </param>
        public AutoSingletonAttribute(bool bCreate)
        {
            this.bAutoCreate = bCreate;
        }
    }


    /// <summary>
    /// 基类继承树中有MonoBehavrour类的单件实现，这种单件实现有利于减少对场景树的查询操作
    /// </summary>
    /// <typeparam name="T">
    /// </typeparam>
    [AutoSingleton(true)]
    public class MonoSingleton<T> : MonoBehaviour where T : Component
    {
        // 单件子类实例
        /// <summary>
        /// The _instance.
        /// </summary>
        private static Component SInstance;

        // 在单件中，每个物件的destroyed标志设计上应该分割在不同的存储个空间中，因此，忽略R#的这个提示
        // ReSharper disable once StaticFieldInGenericType
        /// <summary>
        /// The _destroyed.
        /// </summary>
        private static bool SDestroyed;

        /// <summary>
        ///     获得单件实例，查询场景中是否有该种类型，如果有存储静态变量，如果没有，构建一个带有这个component的gameobject
        ///     必须通过DestroyInstance自行管理单件的生命周期
        /// </summary>
        /// <returns>返回单件实例</returns>
        public static T GetInstance()
        {
            if (SInstance == null && !SDestroyed)
            {
                Type theType = typeof(T);

                SInstance = (T)FindObjectOfType(theType);

                if (SInstance == null)
                {
                    #if UNITY_EDITOR
                    // Editor + 没播放状态下为了支持各种编辑器的便捷访问Instance，还是要支持AutoCreated
                    // 播放状态则严格一些，设置Auto false则不自动创建
                    if (Application.isPlaying)
                    {
                        object[] Attributes = theType.GetCustomAttributes(typeof(AutoSingletonAttribute), true);
                        if (Attributes.Length > 0)
                        {
                            var bAutoCreate = ((AutoSingletonAttribute)Attributes[0]).bAutoCreate;
                            if (!bAutoCreate)
                            {
                                // 不用再重入了 GC Alloc
                                SDestroyed = true;
                                return null;
                            }
                        }
                    }
                    #endif

                    var go = new GameObject(typeof(T).Name);
                    SInstance = go.AddComponent<T>();
                }
            }


            return SInstance as T;
        }

        public static T CreateInstance()
        {
            if (HasInstance())
            {
                return GetInstance();
            }
            
            var go = new GameObject(typeof(T).Name);
            SInstance = go.AddComponent<T>();
            return SInstance as T;
        }
        
        /// <summary>
        /// Gets the instance.
        /// </summary>
        public static T instance
        {
            get { return GetInstance(); }
        }

        /// <summary>
        ///     删除单件实例,这种继承关系的单件生命周期应该由模块显示管理
        /// </summary>
        public static void DestroyInstance()
        {
            if (SInstance != null)
            {
                AutoDestroy(SInstance.gameObject);
            }

            SDestroyed = true;
            SInstance = null;
        }

        /// <summary>
        /// The clear destroy.
        /// </summary>
        public static void ClearDestroy()
        {
            DestroyInstance();

            SDestroyed = false;
        }

        /// <summary>
        ///     Awake消息，确保单件实例的唯一性
        /// </summary>
        protected void Awake()
        {
            if (SInstance != null && SInstance.gameObject != this.gameObject)
            {
                AutoDestroy(this.gameObject);
            }
            else if (SInstance == null)
            {
// #if UNITY_EDITOR
//                 // 编辑器下如果没run起场景不要调用DontDestroyOnLoad，不然会有报错
//                 if (Application.isPlaying)
//                 {
//                     UnityEngine.Object.DontDestroyOnLoad(this.gameObject);
//                 }
// #else
//                 UnityEngine.Object.DontDestroyOnLoad(this.gameObject);
// #endif
                
                SInstance = this;

                this.Init();
            }
        }

        /// <summary>
        ///     OnDestroy消息，确保单件的静态实例会随着GameObject销毁
        /// </summary>
        protected void OnDestroy()
        {
            if (SInstance != null && SInstance.gameObject == this.gameObject)
            {
                SInstance = null;

                // 反初始化
                this.Uninit();
            }
        }

        /// <summary>
        /// The destroy self.
        /// </summary>
        public virtual void DestroySelf()
        {
            SInstance = null;
            AutoDestroy(this.gameObject);
        }

        protected static void AutoDestroy(GameObject gameObject)
        {
            if (Application.isPlaying)
            {
                MonoBehaviour.Destroy(gameObject);
            }
            else
            {
                MonoBehaviour.DestroyImmediate(gameObject); // UNITY_EDITOR
            }
        }

        /// <summary>
        /// The has instance.
        /// </summary>
        /// <returns>
        /// The <see cref="bool"/>.
        /// </returns>
        public static bool HasInstance()
        {
            return SInstance != null;
        }

        /// <summary>
        /// The init.
        /// </summary>
        protected virtual void Init()
        {

        }

        protected virtual void Uninit()
        {
            
        }
    }

}
