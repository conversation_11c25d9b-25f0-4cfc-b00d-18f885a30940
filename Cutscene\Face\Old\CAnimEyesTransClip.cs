//using CAnimTrans;
//using UnityEngine;

//namespace Slate.ActionClips
//{
//    [Name("面部眼睛动作配置")]
//    [Attachable(typeof(CAnimEyesTransTrack))]
//    [Category("C类编辑器")]
//    public class CAnimEyesTransClip : ActorActionClip
//    {
//        //[LabelText("播放速度")]
//        //[SerializeField]
//        //public float PlaySpeed = 1;

//        //[LabelText("循环播放")]
//        //[SerializeField]
//        //public bool Loop = false;

//        //private float _usedBlendAnimTime;

//        //[HideInInspector]
//        //[SerializeField] private float _length = 1 / 30f;

//        //[HideInInspector]
//        //public bool IsCrossing = false;
//        public AnimationClip eyesClip;
//        public float eyesWeight = 1;
//        public PlayingMode eyesPlayingMode = PlayingMode.EndStop;
//        public float eyesStartTime = 0;
//        public float eyesEndTime = 3;
//        public BlendType eyesBlendType = BlendType.EaseInOut;
//        public float eyesBlendInDuration = 0.3f;

//        public BlendType eyesStaticBlendType = BlendType.EaseInOut;
//        public float eyesStaticBlendInDuration = 0.3f;

//        public bool useEyesOverwriteAnim = false;
//        public AnimationClip eyesOverwriteAnim;
//        public float eyesOverwriteAnimFrameTime = 0;
//        public float eyesOverwriteAnimBlendInTime = 0.1f;
//        public float eyesOverwriteAnimBlendOutTime = 0.1f;

//        private GameObject performer;

//        public override string info
//        {
//            get
//            {
//                if (eyesClip != null)
//                {
//                    return eyesClip.name;
//                }
//                else
//                {
//                    return "面部眼睛动作配置";
//                }
//            }
//        }

//        //Called in forward sampling when the clip is entered
//        protected override void OnEnter()
//        {
//            performer = GetActor();
//            AnimTransMaster master = performer.GetComponent<AnimTransMaster>();

//            if (master != null && performer != null)
//            {
//                master.currentFaceEyesAnimClip = eyesClip;
//                master.currentFaceEyesWeight = eyesWeight;
//                master.eyesPlayingMode = eyesPlayingMode;
//                master.eyesStartTime = eyesStartTime;
//                master.eyesEndTime = eyesEndTime;
//                master.faceEyesBlendType = eyesBlendType;
//                master.faceEyesBlendInDuration = eyesBlendInDuration;
//                master.faceEyesStaticBlendType = eyesStaticBlendType;
//                master.faceEyesStaticBlendInDuration = eyesStaticBlendInDuration;

//                master.useEyesOverwriteAnim = useEyesOverwriteAnim;
//                master.eyesOverwriteAnim = eyesOverwriteAnim;
//                master.eyesOverwriteAnimFrameTime = eyesOverwriteAnimFrameTime;
//                master.eyesOverwriteAnimBlendInTime = eyesOverwriteAnimBlendInTime;
//                master.eyesOverwriteAnimBlendOutTime = eyesOverwriteAnimBlendOutTime;

//                master.EyesTransAnim();
//            }
//        }

//        //Called per frame while the clip is updating. Time is the local time within the clip.
//        //So a time of 0 means the start of the clip.
//        protected override void OnUpdate(float time, float previousTime) { }

//        //Called in forwards sampling when the clip exits
//        protected override void OnExit() { }

//        //Called in backwards sampling when the clip is entered.
//        protected override void OnReverseEnter() { }

//        //Called in backwards sampling when the clip exits.
//        protected override void OnReverse() { }
//    }
//}