using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using VGame.Framework;

namespace CEditor
{
    public static class StationTemplateToolsKit
    {
        public static StationTemplateType GetStationTemplateTypeFromName(string typeName)
        {
            if (typeName == "OnePerson")
                typeName = "Single";
            if (typeName == "OnePersonSit")
                typeName = "SingleSit";
            StationTemplateType type = StationTemplateType.Null;
            if (!string.IsNullOrEmpty(typeName))
                type = (StationTemplateType)Enum.Parse(typeof(StationTemplateType), typeName);
            return type;
        }

        public static int GetSlotCountByStationTemplateType(StationTemplateType type)
        {
            switch (type)
            {
                case StationTemplateType.Null: return 0;
                case StationTemplateType.Single: return 1;
                case StationTemplateType.SingleSit: return 1;
                case StationTemplateType.TwoPerson: return 2;
                case StationTemplateType.TwoPersonSit: return 2;
                case StationTemplateType.ThreePerson: return 3;
                case StationTemplateType.ThreePersonSit: return 3;
                case StationTemplateType.FourPerson: return 4;
                case StationTemplateType.FourPersonSit: return 4;
            }
            return 0;
        }

        public static StationTemplatePostureType GetActorPostureByStationTemplateType(StationTemplateType type)
        {
            switch (type)
            {
                case StationTemplateType.Null: return StationTemplatePostureType.Null;
                case StationTemplateType.Single: return StationTemplatePostureType.Stand;
                case StationTemplateType.SingleSit: return StationTemplatePostureType.Sit;
                case StationTemplateType.TwoPerson: return StationTemplatePostureType.Stand;
                case StationTemplateType.TwoPersonSit: return StationTemplatePostureType.Sit;
                case StationTemplateType.ThreePerson: return StationTemplatePostureType.Stand;
                case StationTemplateType.ThreePersonSit: return StationTemplatePostureType.Sit;
                case StationTemplateType.FourPerson: return StationTemplatePostureType.Stand;
                case StationTemplateType.FourPersonSit: return StationTemplatePostureType.Sit;
            }
            return StationTemplatePostureType.Null;
        }

        public static int GetSlotCountByStationTemplateName(string name)
        {
            if (string.IsNullOrEmpty(name)) return 0;
            return GetSlotCountByStationTemplateType(GetStationTemplateTypeFromName(name));
        }

        public static int GetSlotIndexByStringName(string slotName)
        {
            if (slotName == null)
                return -1;
            slotName = slotName.ToUpper();
            byte[] array = System.Text.Encoding.ASCII.GetBytes(slotName);
            int asciicode = (int)(array[0]);
            return asciicode - 65;
        }

        public static string GetSlotStringNameByIndex(int slotIndex)
        {
            if (slotIndex >= 0 && slotIndex <= 25)
            {
                slotIndex += 65;
                byte[] array = new byte[1];
                array[0] = (byte)(Convert.ToInt32(slotIndex)); //ASCII码强制转换二进制
                string code = Convert.ToString(System.Text.Encoding.ASCII.GetString(array));
                return code;
            }
            return "Null";
        }

        public static string GetTargetTag(List<int> targetIndexList)
        {
            if (targetIndexList == null)
                return null;
            targetIndexList.Sort();
            string result = "";
            foreach(int index in targetIndexList)
            {
                result += GetSlotStringNameByIndex(index);
            }
            return result;
        }

        public static List<List<int>> GetAllSubset(int fullIndexCount)
        {
            List<int> set = new List<int>();
            for (int i = 0; i < fullIndexCount; i++)
                set.Add(i);
            List<List<int>> subsets = GetSubsets(set);
            List<List<int>> garbag = new List<List<int>>();
            foreach (List<int> subset in subsets)
            {
                if (subset.Count == 0)
                {
                    garbag.Add(subset);
                    continue;
                }
                if (subset.Count > 1)
                {
                    for (int i = 0; i < subset.Count - 1; i++)
                    {
                        if ((subset[i+1] - subset[i]) > 1)
                        {
                            garbag.Add(subset);
                            continue;
                        }
                    }
                }
                
            }
            foreach (List<int> subset in garbag)
            {
                subsets.Remove(subset);
            }
            subsets.Sort((a, b) => a.Count.CompareTo(b.Count));
            return subsets;
        }

        private static List<List<int>> GetSubsets(List<int> set)
        {
            List<List<int>> subsets = new List<List<int>>();
            int n = set.Count;
            int totalSubsets = 1 << n; // 2^n

            for (int i = 0; i < totalSubsets; i++)
            {
                List<int> subset = new List<int>();
                for (int j = 0; j < n; j++)
                {
                    if ((i & (1 << j)) != 0)
                    {
                        subset.Add(set[j]);
                    }
                }
                subsets.Add(subset);
            }

            return subsets;
        }

        // slotOffset解析
        public static List<SlotRotateOffsetInfo> AnalysisSlotsRotateOffsets(string name)
        {
            if (string.IsNullOrEmpty(name))
                return null;
            if (name.Substring(0, 1) == "#")
                name = name.Substring(1);

            string[] strings = name.Split(")");
            List<SlotRotateOffsetInfo> result = new List<SlotRotateOffsetInfo>();

            foreach (string s in strings)
            {
                if (!string.IsNullOrEmpty(s))
                {
                    result.Add(new SlotRotateOffsetInfo()
                    {
                        slotIndex = StationTemplateToolsKit.GetSlotIndexByStringName(s.Split("(")[0]),
                        rotateYoffset = Convert.ToSingle(s.Split("(")[1])
                    });
                }
            }
            return result;
        }

        public static bool NeedToRotateSlot(string name)
        {
            if (name == null)
                return false;
            if (name == "Null")
                return false;
            if (name.Split(")").Length < 2)
                return false;
            return true;
        }

        public static bool CameraIsUnderStationTemplate(int templateID, int cameraID)
        {
            if (cameraID - templateID > 0 && (cameraID - templateID) < 10000) 
                return true;
            return false;
        }

        public static float CalculateAlign1To2RotateYOffset(Vector3 line1A, Vector3 line1B, Vector3 line2A, Vector3 line2B)
        {
            Vector3 xStart = new Vector3(line1A[0], 0, line1A[2]);
            Vector3 xEnd = new Vector3(line1B[0], 0, line1B[2]);
            Vector3 yStart = new Vector3(line2A[0], 0, line2A[2]);
            Vector3 yEnd = new Vector3(line2B[0], 0, line2B[2]);

            float angle = Mathf.Acos(Vector3.Dot((xEnd - xStart).normalized, (yEnd - yStart).normalized)) * Mathf.Rad2Deg;

            Vector3 cro = Vector3.Cross((xEnd - xStart).normalized, (yEnd - yStart).normalized);
            if (cro.y >= 0)
                return angle;
            else
                return angle * (-1);

        }

        public static Vector3 CalculateAlign1To2PositionOffset(Vector3 A, Vector3 B)
        {
            return B - A;
        }

        public static bool HasForefathers(Transform progeny, Transform forefather)
        {
            if (forefather == null) return true;
            if (progeny == null) return false;
            
            if (progeny.parent == forefather)
                return true;
            else
                return HasForefathers(progeny.parent, forefather);
        }

        public static List<DerivativeCameraGroupInfo> GetDefaultDerCamGroupIDs(string derCamTag)
        {
            if (string.IsNullOrEmpty(derCamTag) || derCamTag.ToLower() == "null")
                return new List<DerivativeCameraGroupInfo>();
            string defaultDerCamTag = derCamTag.Split("##")[0];
            if (string.IsNullOrEmpty(defaultDerCamTag) || defaultDerCamTag.ToLower() == "null") 
                return new List<DerivativeCameraGroupInfo>();

            string[] defaultDerCamIDs = defaultDerCamTag.Split("#");
            List<DerivativeCameraGroupInfo> result = new List<DerivativeCameraGroupInfo>();
            foreach (string id in defaultDerCamIDs)
            {
                int outID;
                if (int.TryParse(id, out outID))
                    result.Add(new DerivativeCameraGroupInfo()
                    {
                        templateID = outID,
                        slotNum = -1,
                        subGroupObject = null
                    });
            }
            return result;
        }

        public static List<CameraGroupOfTargetSlotInfo> GetOverwriteDerCamGroupInfo(string derCamTag)
        {
            if (string.IsNullOrEmpty(derCamTag)) 
                return new List<CameraGroupOfTargetSlotInfo>();
            string[] tags = derCamTag.Split("##");
            if (tags.Length < 2)
                return new List<CameraGroupOfTargetSlotInfo>();
            string overwriteDerCamTag = tags[1];
            string[] overwriteDerCamTags = overwriteDerCamTag.Split(")");
            List<CameraGroupOfTargetSlotInfo> result = new List<CameraGroupOfTargetSlotInfo>();
            foreach (string tag in overwriteDerCamTags)
            {
                if (!string.IsNullOrEmpty(tag))
                {
                    string[] tagDetail = tag.Split("(");
                    if (tagDetail.Length < 2)
                        continue;
                    if (string.IsNullOrEmpty(tagDetail[0]))
                        continue;
                    if (!string.IsNullOrEmpty(tagDetail[1]))
                    {
                        int outID;
                        if (tagDetail[1].ToLower() == "null")
                        {
                            result.Add(new CameraGroupOfTargetSlotInfo()
                            {
                                targetSlotGroupTag = tagDetail[0],
                                templateID = -1,
                                targetSlotIndex = null,
                                groupGameObject = null,
                                spawnParentGameObject = null,
                            });
                        }
                        else if (int.TryParse(tagDetail[1], out outID))
                        {
                            result.Add(new CameraGroupOfTargetSlotInfo()
                            {
                                targetSlotGroupTag = tagDetail[0],
                                templateID = outID,
                                targetSlotIndex = null,
                                groupGameObject = null,
                                spawnParentGameObject = null,
                            });
                        }
                    }
                }
            }
            return result;
        }
    }

    public enum StationTemplateType
    {
        Single,
        TwoPerson,
        ThreePerson,
        SingleSit,
        TwoPersonSit,
        ThreePersonSit,
        FourPerson,
        FourPersonSit,
        Null
    }

    public enum StationTemplatePostureType
    {
        Stand,
        Sit,
        Custom,
        Null
    }

    [ExecuteAlways]
    public class SlotRotateOffsetInfo
    {
        public int slotIndex;
        public float rotateYoffset;
    }
}
