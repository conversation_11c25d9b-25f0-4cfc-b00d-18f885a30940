using System.Collections;
using System.Linq;
using CEditor;
using ET;
using Sirenix.OdinInspector;
using UnityEngine;
using VGame;
using VGame.Framework;

namespace Slate
{
    [Name("后处理/后处理模板")]
    public class CPostProcessTemplateClip : DirectorActionClip
    {
        [LabelText("后处理模板ID")] [ValueDropdown("PostprocessConfig")]
        public int templateId;

        public override bool isValid => templateId != 0;

        private IEnumerable PostprocessConfig()
        {
            return CfgManager.tables.TbCEditorPostprocessTemplateTable.DataList.Select(x => new ValueDropdownItem($"{x.ID}-{x.Detail}", x.ID));
        }

        protected override async ETTask OnEnterAsync()
        {
            if (StationTemplateManager.instance.IsPostprocessTemplateRegistered(templateId))
            {
                Log.LogInfo("CPostProcessTemplateClip reuse post process template, id: {0}", templateId);
                StationTemplateManager.instance.ReusePostprocessTemplate();
                return;
            }

            var cfg = CfgManager.tables.TbCEditorPostprocessTemplateTable.GetOrDefault(templateId);
            if (cfg == null)
                return;

            var template = await DialogueUtil.Load<GameObject>(cfg.FileName);
            if (template == null)
                return;

            var instance = Instantiate(template);
            Log.LogInfo("CPostProcessTemplateClip register postprocess template, id: {0}, instance: {1}", templateId, instance.name);
            StationTemplateManager.instance.RegisterPostprocessTemplate(templateId, instance);
        }

        protected override void OnRootDisabled()
        {
            Log.LogInfo("CPostProcessTemplateClip unregister postprocess template, id: {0}", templateId);
            StationTemplateManager.instance.UnregisterPostprocessTemplate(templateId);
        }
    }
}
