using CEditor;
using Cysharp.Text;
using Sirenix.OdinInspector;
using Slate.ActionClips;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Slate
{
    [Name("C类编辑器_镜头轨道")]
    [Attachable(typeof(DirectorGroup))]
    [Category("C类编辑器")]

    public class CCameraTransTrack : DirectorActionTrack
    {
        [SerializeField]
        public int CamTemplateID = 0;

        private bool changeStationTemplateTransformOnce = true;

        protected override void OnCreate()
        {
            base.OnCreate();
            this.name = ZString.Format("C类编辑器_镜头轨道[{0}]", CamTemplateID);
            changeStationTemplateTransformOnce = true;
        }

        protected override void OnSceneGUI()
        {
            base.OnSceneGUI();
            this.name = ZString.Format("C类编辑器_镜头轨道[{0}]", CamTemplateID);
        }

        protected override void OnEnter()
        {
            base.OnEnter();
        }

        public void InitStationTemplateOriginalTransform(string CamPickerName)
        {
            Debug.Log(ZString.Concat("InitStationTemplateOriginalTransform [" , CamPickerName , "]"));
            if (changeStationTemplateTransformOnce)
                changeStationTemplateTransformOnce = false;
            else
                return;

            if (!string.IsNullOrEmpty(CamPickerName))
            {
                Cutscene cutscene = root as Cutscene;
                if (cutscene != null && cutscene.StoryScene != null)
                {
                    var stationTemplatePickers = cutscene.StoryScene.GetComponentsInChildren<StationTemplatePicker>();
                    foreach (var picker in stationTemplatePickers)
                    {
                        if (picker.name.Equals(CamPickerName))
                        {
                            StationTemplateManager.instance.SetStationTemplateOriginalTransform(picker.transform);
                            return;
                        }
                    }
                }
            }
            StationTemplateManager.instance.SetStationTemplateOriginalTransform(Vector3.zero, Quaternion.identity);
        }

#if UNITY_EDITOR
        public override Texture icon
        {
            get
            {
                _icon = Resources.Load("PlayIcon") as Texture;
                return _icon as Texture;
            }
        }
#endif
        public override string info
        {
            get
            {
                return "camera trans track info";
            }
        }

        public CSingleCameraTransClip FindFirstCameraTransClip()
        {
            foreach (var clip in clips)
            {
                if (clip is CSingleCameraTransClip camClip)
                    return camClip;
            }

            return null;
        }
    }
}