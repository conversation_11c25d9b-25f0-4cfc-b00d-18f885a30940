using System.Collections;
using System.Collections.Generic;
using FIMSpace.FEyes;
using UnityEngine;
using Cysharp.Text;
using System;

namespace CEditor
{
    [ExecuteAlways]
    public class FaceBlendShapeSetting
    {
        public string blendShapeName = "";
        public int index = -1;
        public float weight = 1.0f;
    }

    [ExecuteAlways]
    public class EyeBlinkManager : MonoBehaviour
    {
        public AnimTransMaster animMaster;

        public FEyesAnimator eyesAnimator;

        public SkinnedMeshRenderer faceRenderer = null;

        public string baseBoneName = "Bip01_Spine";

        public string headBoneName = "Bip01_Head";

        public Transform baseBone = null;

        public Transform headBone = null;

        public List<FaceBlendShapeSetting> closeEyesBSList = new List<FaceBlendShapeSetting>();

        public bool useAutoHeadRotateCloseEye = true;

        public bool useAutoHeadRotateBlinkEye = true;

        public float minIntervalDuration = 0.3f;

        public float minCloseEyeJudgeValue = 10f;

        // half close eye when look down 20deg
        public float minAutoCloseEyesUnderHeadAngle = 20f;
        
        //stop half close eye when look down increase to 15deg
        public float maxAutoOpenEyesUnderHeadAngle = 15f;
        
        public float minAutoCompleteBlinkHeadRotateAcceleration = 3.5f;

        //public float minAutoHalfBlinkHeadRotateAcceleration = 300f;

        //public float minAutoHalfBlinkHeadRotateVelocity = 30f;

        public AnimationClip customBlinkAnim { get; set; }

        public float customBlinkAnimTime { get; set; } = 0.0f;

        public float halfBlinkProportion { get; set; } = 0.5f;

        public float blinkFrequency { get; set; } = 1.5f;

        public float blinkFrequencyRandomStrenght { get; set; } = 0.3f;

        public float blinkSpeed { get; set; } = 0.8f;

        public float blinkSpeedRandomStrenght { get; set; } = 0.2f;

        public float blinkHoldTime { get; set; } = 0.0f;

        public float blinkHoldTimeRandomStrenght { get; set; } = 0.1f;

        /////////////////////////////////////////////////////////
        public EyeOverwriteType closeEyesType { get; set; } = EyeOverwriteType.CompletelyClose;

        public AnimationClip customEyesCloseAnim { get; set; }

        public float customEyesCloseAnimTime { get; set; } = 0.0f;

        public float eyesCloseBlendInTime { get; set; } = 0.15f;

        public float eyesCloseBlendOutTime { get; set; } = 0.3f;

        /////////////////////////////////////////////////////////
        public bool useKeepBlinkControl { get; set; } = false;

        public bool useKeepCloseEyesControl { get; set; } = false;

        public bool keepBlinkingTrigger { get; set; } = false;

        public bool keepClosingEyesTrigger { get; set; } = false;

        ////////////////////////////////////////////
        /////////////////运行时参数/////////////////
        ////////////////////////////////////////////
        private float blinkIntervalDurationTime = 0.0f;

        private float targetBlinkDurationTime = 1.0f;
        private float targetBlinkBlendInTime = 0.15f;
        private float targetBlinkHoldTime = 0.0f;
        
        private Vector3 headRotate = Vector3.zero;
        private Vector3 prevHeadRotate = Vector3.zero;
        private float headRotateVelocity;
        private float prevHeadRotateVelocity;
        private float headRotateAcceleration;

        private Vector3 smoothHeadRotate = Vector3.zero;
        private float smoothHeadRotateVelocity;
        private float smoothHeadRotateAcceleration;

        private float animCloseEyesValue = 0.0f;

        private EyeOverwriteType preCloseEyesType = EyeOverwriteType.None;
        private EyeOverwriteType preBlinkEyesType = EyeOverwriteType.None;

        ////////////////////////////////////////////////
        private bool isClosingEyes = false;

        // Start is called before the first frame update
        void Start()
        {
            if (animMaster == null)
                animMaster = this.gameObject.GetComponent<AnimTransMaster>();
            if (eyesAnimator == null)
                eyesAnimator = this.gameObject.GetComponent<FEyesAnimator>();
            eyesAnimator.onEyeSwitched.AddListener(OnEyeSwitchTarget);
            InitBlendShapeSetting();
            InitRuntimeParam();
            InitRelevantBone();
        }

        // Update is called once per frame
        void Update()
        {
            UpdateRuntimeParam();

            if (useKeepBlinkControl)
                UpdateKeepBlinkState();
            if (useKeepCloseEyesControl)
                UpdateKeepCloseEyesState();

            if (useAutoHeadRotateCloseEye)
                AutoHeadRotateCloseEye();
            if (useAutoHeadRotateBlinkEye)
                AutoHeadRotateBlinkEye();
        }

        private void LateUpdate()
        {
            RecordAnimCloseEyesValue();
            RecordHeadTransform();
            CalculateHeadMotionParam();

            //Debug.Log(ZString.Concat("RotateVelocity : ", smoothHeadRotateVelocity.y.ToString()));
            //Debug.Log(ZString.Concat("RotateAcceleration : ", smoothHeadRotateAcceleration.y.ToString()));
            //Debug.Log(ZString.Concat("Rotate : ", headRotate.ToString()));
            //Debug.Log(ZString.Concat("RotateVelocity : ", headRotateVelocity.ToString()));
            //Debug.Log(ZString.Concat("RotateAcceleration : ", headRotateAcceleration.ToString()));
        }


        ////////////////////////////////////////////////////////////////////////
        ////////////////////////////////CUTSCENE////////////////////////////////
        ////////////////////////////////////////////////////////////////////////
        private void UpdateKeepBlinkState()
        {
            if (keepBlinkingTrigger)
            {
                RandomBlinkOnce(halfBlinkProportion);
                keepBlinkingTrigger = false;
            }
            else
            {
                useKeepBlinkControl = false;
            }
        }

        private void UpdateKeepCloseEyesState()
        {
            if (keepClosingEyesTrigger)
            {
                if (!isClosingEyes || (isClosingEyes && preCloseEyesType != closeEyesType))
                {
                    CloseEyes(closeEyesType);
                }
                keepClosingEyesTrigger = false;
            }
            else
            {
                OpenEyes();
                useKeepCloseEyesControl = false;
            }
        }


        ////////////////////////////////////////////////////////////////////////
        ///////////////////////////////BLINK EYES///////////////////////////////
        ////////////////////////////////////////////////////////////////////////
        private void OnEyeSwitchTarget()
        {
            EyesOverwritePerformConfig blinkConfig = new EyesOverwritePerformConfig();
            blinkConfig.overwriteType = EyeOverwriteType.CompletelyClose;
            blinkConfig.eyesOverwriteConfig.useEyesOverwriteAnim = true;
            blinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime = targetBlinkBlendInTime - 0.075f;
            blinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendOutTime = targetBlinkBlendInTime + 0.175f;
            blinkConfig.eyesOverwriteConfig.eyesOverwriteAnimHoldTime = targetBlinkHoldTime;
            BlinkOnce(blinkConfig);
            // Debug.Log("Eye Switch Target Blink Once");
        }
        
        public bool BlinkOnce(EyeOverwriteType blinkType)
        {
            if (animMaster == null)
                return false;
            if (!CheckBlinkValid())
                return false;

            RefreshTargetBlinkValue();
            
            EyesOverwritePerformConfig blinkConfig = new EyesOverwritePerformConfig();
            blinkConfig.overwriteType = blinkType;
            blinkConfig.eyesOverwriteConfig.useEyesOverwriteAnim = true;
            blinkConfig.eyesOverwriteConfig.eyesOverwriteAnim = customBlinkAnim;
            blinkConfig.eyesOverwriteConfig.eyesOverwriteAnimFrameTime = customBlinkAnimTime;
            blinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime = targetBlinkBlendInTime ;
            blinkConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendOutTime = targetBlinkBlendInTime + 0.15f;
            blinkConfig.eyesOverwriteConfig.eyesOverwriteAnimHoldTime = targetBlinkHoldTime;

            if (animMaster.BlinkOnce(blinkConfig))
            {
                preBlinkEyesType = blinkConfig.overwriteType;
                ResetRuntimeParam();
                return true;
            }
            return false;
        }
        
        public bool BlinkOnce(EyesOverwritePerformConfig blinkConfig)
        {
            if (animMaster == null)
                return false;
            if (!CheckBlinkValid())
                return false;
            
            if (animMaster.BlinkOnce(blinkConfig))
            {
                preBlinkEyesType = blinkConfig.overwriteType;
                ResetRuntimeParam();
                return true;
            }
            return false;
        }

        private void RandomBlinkOnce(float halfBlinkProportion = 0.5f)
        {
            if (blinkIntervalDurationTime < targetBlinkDurationTime)
                return;

            bool fullClose = UnityEngine.Random.Range(0f, 1f) >= Mathf.Clamp01(halfBlinkProportion) ? true : false;
            EyeOverwriteType blinkType = EyeOverwriteType.CompletelyClose;
            if (fullClose)
                blinkType = EyeOverwriteType.CompletelyClose;
            else
                blinkType = EyeOverwriteType.HalfClose;
            BlinkOnce(blinkType);
        }

        ////////////////////////////////////////////////////////////////////////
        ///////////////////////////////CLOSE EYES///////////////////////////////
        ////////////////////////////////////////////////////////////////////////
        public bool CloseEyes(EyeOverwriteType eyesCloseType)
        {
            if (animMaster == null)
                return false;

            //RefreshTargetBlinkValue();

            EyesOverwritePerformConfig eyesCloseConfig = new EyesOverwritePerformConfig();
            eyesCloseConfig.overwriteType = eyesCloseType;
            eyesCloseConfig.eyesOverwriteConfig.useEyesOverwriteAnim = true;
            eyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnim = customEyesCloseAnim;
            eyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimFrameTime = customEyesCloseAnimTime;
            eyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime = eyesCloseBlendInTime;
            eyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendOutTime = eyesCloseBlendOutTime;
            eyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimHoldTime = 0;

            if (animMaster.CloseEyes(eyesCloseConfig))
            {
                preCloseEyesType = eyesCloseType;
                isClosingEyes = true;
                return true;
            }
            return false;
        }

        public bool CloseEyes(EyesOverwritePerformConfig eyesCloseConfig)
        {
            if (animMaster == null)
                return false;
            if (ReferenceEquals(eyesCloseConfig, null))
                return false;
            //RefreshTargetBlinkValue();
            if (animMaster.CloseEyes(eyesCloseConfig))
            {
                preCloseEyesType = eyesCloseConfig.overwriteType;
                isClosingEyes = true;
                return true;
            }
            return false;
        }

        public bool OpenEyes()
        {
            if (animMaster == null)
                return false;
            
            if (animMaster.OpenEyes())
            {
                isClosingEyes = false;
                return true;
            }
            return false;
        }

        public bool CheckInCloseEyes()
        {
            if (animMaster == null)
                return false;
            return animMaster.CheckInCloseEyes();
        }

        ////////////////////////////////////////////////////////////////////////
        //////////////////////////////////INIT//////////////////////////////////
        ////////////////////////////////////////////////////////////////////////
        private void InitBlendShapeSetting()
        {
            //closeEyesBSList.Add(new FaceBlendShapeSetting() { blendShapeName = "", index = 0 });
            closeEyesBSList.Clear();
            closeEyesBSList.Add(new FaceBlendShapeSetting() { blendShapeName = "blendShape.ARkit_Blink_L", index = -1, weight = 0.25f });
            closeEyesBSList.Add(new FaceBlendShapeSetting() { blendShapeName = "blendShape.ARkit_Blink_R", index = -1, weight = 0.25f });
            closeEyesBSList.Add(new FaceBlendShapeSetting() { blendShapeName = "blendShape.Eye_Close", index = -1, weight = 0.25f });
            closeEyesBSList.Add(new FaceBlendShapeSetting() { blendShapeName = "blendShape.Eye_BanBi", index = -1, weight = 0.0f });
            closeEyesBSList.Add(new FaceBlendShapeSetting() { blendShapeName = "blendShape.Eye_HuaChi", index = -1, weight = 0.25f });
            closeEyesBSList.Add(new FaceBlendShapeSetting() { blendShapeName = "blendShape.Eye_Wink_L", index = -1, weight = 1.0f });
            closeEyesBSList.Add(new FaceBlendShapeSetting() { blendShapeName = "blendShape.Eye_Wink_R", index = -1, weight = 1.0f });

            foreach (SkinnedMeshRenderer skinmeshrender in this.gameObject.GetComponentsInChildren<SkinnedMeshRenderer>())
            {
                if (skinmeshrender.name.Contains("Face"))
                {
                    faceRenderer = skinmeshrender;
                    break;
                }
            }
            if (faceRenderer != null)
            {
                for (int i = 0; i < faceRenderer.sharedMesh.blendShapeCount; i++)
                {
                    string blendShapeName = faceRenderer.sharedMesh.GetBlendShapeName(i);
                    int index = closeEyesBSList.FindIndex(x => x.blendShapeName == blendShapeName);
                    if (index != -1)
                        closeEyesBSList[index].index = i;
                }
            }
        }

        private void InitRelevantBone()
        {
            baseBone = StationTemplateManager.FindChildInTransform(
                this.transform,
                baseBoneName,
                new List<string>() { StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand],
                    StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit] });
            headBone = StationTemplateManager.FindChildInTransform(
                this.transform,
                headBoneName,
                new List<string>() { StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand],
                    StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit] });
        }

        private void InitRuntimeParam()
        {
            blinkIntervalDurationTime = minIntervalDuration;
        }


        ////////////////////////////////////////////////////////////////////////
        /////////////////////////////////UPDATE/////////////////////////////////
        ////////////////////////////////////////////////////////////////////////
        private void UpdateRuntimeParam()
        {
            if (animCloseEyesValue > minCloseEyeJudgeValue)
                ResetRuntimeParam();
            else
                blinkIntervalDurationTime += Time.deltaTime;
        }

        private void ResetRuntimeParam()
        {
            blinkIntervalDurationTime = 0.0f;
        }

        private void RefreshTargetBlinkValue()
        {
            targetBlinkDurationTime = 1 / (GetRandomParam(blinkFrequency, blinkFrequencyRandomStrenght, true) + 0.0001f);
            targetBlinkBlendInTime = 1 / (GetRandomParam(blinkSpeed * 10, blinkSpeedRandomStrenght * 10, true) + 0.0001f);
            targetBlinkHoldTime = GetRandomParam(blinkHoldTime, blinkHoldTimeRandomStrenght, true);
        }


        ////////////////////////////////////////////////////////////////////////
        /////////////////////////////////RECORD/////////////////////////////////
        ////////////////////////////////////////////////////////////////////////
        private void RecordHeadTransform()
        {
            prevHeadRotate = headRotate;
            if (headBone != null)
            {
                // headRotate = headBone.rotation.eulerAngles - baseBone.rotation.eulerAngles;
                
                // use current headreference as direction indicator
                // HeadReference.forward    blue z      left
                // HeadReference.right      red x       down
                // HeadReference.up         green y     forward
                headRotate = headBone.up;
                // Debug.Log(ZString.Format("headRotate.y: {0} headRotateDis: {1}", headRotate.y, headRotatedis));
                // headRotate.x = 0;
            }
        }

        private void CalculateHeadMotionParam()
        {
            prevHeadRotateVelocity = headRotateVelocity;
            
            headRotateVelocity = (headRotate - prevHeadRotate).magnitude / Time.deltaTime;
            headRotateAcceleration = (headRotateVelocity - prevHeadRotateVelocity) / Time.deltaTime;

            smoothHeadRotate = Vector3.Lerp(smoothHeadRotate, headRotate, 0.5f);
            smoothHeadRotateVelocity = Mathf.Lerp(smoothHeadRotateVelocity, headRotateVelocity, 0.5f);
            smoothHeadRotateAcceleration = Mathf.Lerp(smoothHeadRotateAcceleration, headRotateAcceleration, 0.5f);
        }

        private void RecordAnimCloseEyesValue()
        {
            float closeStrength = 0;
            foreach (var s in closeEyesBSList)
            {
                if (s != null && !string.IsNullOrEmpty(s.blendShapeName) && s.index >= 0)
                {
                    float v = faceRenderer.GetBlendShapeWeight(s.index) * s.weight;
                    closeStrength = v > closeStrength ? v : closeStrength;
                }
            }
            animCloseEyesValue = closeStrength;
        }

        ////////////////////////////////////////////////////////////////////////
        ///////////////////////////Auto Blink Close/////////////////////////////
        ////////////////////////////////////////////////////////////////////////
        private void AutoHeadRotateBlinkEye()
        {
            // Debug.Log(smoothHeadRotateAcceleration);
            // 
            if (smoothHeadRotateAcceleration > minAutoCompleteBlinkHeadRotateAcceleration)
            {
                BlinkOnce(EyeOverwriteType.CompletelyClose);
                // Debug.Log(ZString.Format("Large Move Blink Once ACC: {0}", smoothHeadRotateAcceleration));
            }
            
            //else if (Mathf.Abs(headRotateAcceleration.y) > minAutoHalfBlinkHeadRotateAcceleration && Mathf.Abs(smoothHeadRotateVelocity.y) > minAutoHalfBlinkHeadRotateVelocity)
            //    BlinkOnce(EyeOverwriteType.HalfClose);
        }

        private void AutoHeadRotateCloseEye()
        {
            if (useKeepCloseEyesControl)
                return;

            float smoothHeadPitch = Mathf.Asin(smoothHeadRotate.normalized.y) * Mathf.Rad2Deg;

            if (!isClosingEyes && smoothHeadPitch < -minAutoCloseEyesUnderHeadAngle)
            {
                EyesOverwritePerformConfig eyesCloseConfig = new EyesOverwritePerformConfig();
                eyesCloseConfig.overwriteType = EyeOverwriteType.HalfClose;
                eyesCloseConfig.eyesOverwriteConfig.useEyesOverwriteAnim = true;
                eyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnim = null;
                eyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimFrameTime = 0;
                eyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendInTime = 0.4f;
                eyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimBlendOutTime = 0.5f;
                eyesCloseConfig.eyesOverwriteConfig.eyesOverwriteAnimHoldTime = 0;

                CloseEyes(eyesCloseConfig);
            }
            
            if (isClosingEyes && smoothHeadPitch > -maxAutoOpenEyesUnderHeadAngle)
                OpenEyes();
        }

        private bool CheckBlinkValid()
        {
            if (animCloseEyesValue <= minCloseEyeJudgeValue &&
                blinkIntervalDurationTime >= minIntervalDuration)
                return true;
            return false;
        }

        ////////////////////////////////////////////////////////////////////////
        //////////////////////////////////TOOLS/////////////////////////////////
        ////////////////////////////////////////////////////////////////////////
        private float GetRandomParam(float baseValue, float randomStrength, bool positive)
        {
            if (randomStrength < 0)
                randomStrength = -randomStrength;
            float value = baseValue + UnityEngine.Random.Range(-randomStrength, randomStrength);
            if (positive)
                return Mathf.Clamp(value, 0, baseValue + randomStrength);
            else return value;
        }


        ////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////
        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

