//#if UNITY_EDITOR
//using UnityEngine;
//using UnityEditor;
//using Animancer.Editor;
//using System.IO;
//using System;

//namespace CEditor
//{
//    //[CustomEditor(typeof(CameraController))] //关联之前的脚本
//    public class CameraControllerEditor : Editor
//    {
//        private SerializedObject obj; //序列化

//        private SerializedProperty FOVOffset, screenRotateOffset, screenHorizontalOffset, screenVerticalOffset, horizontalAngleOffset, pitchOffset, distanceOffset;

//        private bool prevRuntimeAutoSave;
//        private float prevFOVOffset, prevScreenRotateOffset, prevScreenHorizontalOffset, prevScreenVerticalOffset, prevHorizontalAngleOffset, prevPitchOffset, prevDistanceOffset;

//        private float defaultFOV;

//        string directory = "Assets/Datas/";

//        void OnEnable()
//        {
//            obj = new SerializedObject(target);

//            FOVOffset = obj.FindProperty("FOVOffset");
//            screenRotateOffset = obj.FindProperty("screenRotateOffset");
//            screenHorizontalOffset = obj.FindProperty("screenHorizontalOffset");
//            screenVerticalOffset = obj.FindProperty("screenVerticalOffset");
//            horizontalAngleOffset = obj.FindProperty("horizontalAngleOffset");
//            pitchOffset = obj.FindProperty("pitchOffset");
//            distanceOffset = obj.FindProperty("distanceOffset");

//            prevFOVOffset = FOVOffset.floatValue;
//            prevScreenRotateOffset = screenRotateOffset.floatValue;
//            prevScreenHorizontalOffset = screenHorizontalOffset.floatValue;
//            prevScreenVerticalOffset = screenVerticalOffset.floatValue;
//            prevHorizontalAngleOffset = horizontalAngleOffset.floatValue;
//            prevPitchOffset = pitchOffset.floatValue;
//            prevDistanceOffset = distanceOffset.floatValue;

//            string camName = target.name;
//            if (CCameraTools.IsCamera(camName))
//            {
//                string FOVString = "20";
//                CameraNameInfo cameraNameInfo = CCameraTools.AnalysisCameraInfoFromName(camName);
//                if (cameraNameInfo != null)
//                    if (cameraNameInfo.camType != CameraType.SingleDerivative)
//                        FOVString = cameraNameInfo.baseCamInfo.FOV.ToString();
//                defaultFOV = Convert.ToSingle(FOVString);
//            }
//        }

//        public override void OnInspectorGUI()
//        {
//            base.OnInspectorGUI();

//            obj.Update();

//            EditorGUILayout.PropertyField(FOVOffset);
//            EditorGUILayout.PropertyField(screenRotateOffset);
//            EditorGUILayout.PropertyField(screenHorizontalOffset);
//            EditorGUILayout.PropertyField(screenVerticalOffset);
//            EditorGUILayout.PropertyField(horizontalAngleOffset);
//            EditorGUILayout.PropertyField(pitchOffset);
//            EditorGUILayout.PropertyField(distanceOffset);

//            float finalFOV = defaultFOV + FOVOffset.floatValue;
//            finalFOV = Mathf.Clamp(finalFOV, 10, 120);
//            FOVOffset.SetValue(target, finalFOV - defaultFOV);
//            screenRotateOffset.SetValue(target, Mathf.Clamp(screenRotateOffset.floatValue, -180, 180));
//            screenHorizontalOffset.SetValue(target, Mathf.Clamp(screenHorizontalOffset.floatValue, -1, 1));
//            screenVerticalOffset.SetValue(target, Mathf.Clamp(screenVerticalOffset.floatValue, -1, 1));

//            // 自动保存
//            //if (prevRuntimeAutoSave != runtimeAutoSave.boolValue ||
//            //    prevFOVOffset != FOVOffset.floatValue ||
//            //    prevScreenRotateOffset != screenRotateOffset.floatValue ||
//            //    prevScreenHorizontalOffset != screenHorizontalOffset.floatValue ||
//            //    prevScreenVerticalOffset != screenVerticalOffset.floatValue ||
//            //    prevHorizontalAngleOffset != horizontalAngleOffset.floatValue || 
//            //    prevPitchOffset != pitchOffset.floatValue ||
//            //    prevDistanceOffset != distanceOffset.floatValue)
//            //{
//            //    prevFOVOffset = FOVOffset.floatValue;
//            //    prevScreenRotateOffset = screenRotateOffset.floatValue;
//            //    prevScreenHorizontalOffset = screenHorizontalOffset.floatValue;
//            //    prevScreenVerticalOffset = screenVerticalOffset.floatValue;
//            //    prevHorizontalAngleOffset = horizontalAngleOffset.floatValue;
//            //    prevPitchOffset = pitchOffset.floatValue;
//            //    prevDistanceOffset = distanceOffset.floatValue;

//            //    if (!Application.isPlaying)
//            //        SaveCameraManagerDataByJSON();
//            //}
//            //if (!Application.isPlaying)
//            //    LoadCameraManagerDataByJSON();

//            obj.ApplyModifiedProperties();
//        }


//        //void LoadCameraManagerDataByJSON()
//        //{
//        //    string dataPath = directory + "CameraControllerParam" + target.GetInstanceID().ToString() + ".json";
//        //    if (File.Exists(dataPath))
//        //    {
//        //        string json = File.ReadAllText(dataPath);
//        //        CameraControllerData data = JsonUtility.FromJson<CameraControllerData>(json);
//        //        FOVOffset.SetValue(target, data.FOVOffset);
//        //        screenRotateOffset.SetValue(target, data.screenRotateOffset);
//        //        screenHorizontalOffset.SetValue(target, data.screenHorizontalOffset);
//        //        screenVerticalOffset.SetValue(target, data.screenVerticalOffset);
//        //        horizontalAngleOffset.SetValue(target, data.horizontalAngleOffset);
//        //        pitchOffset.SetValue(target, data.pitchOffset);
//        //        distanceOffset.SetValue(target, data.distanceOffset);
//        //    }
//        //}

//        //void SaveCameraManagerDataByJSON()
//        //{
//        //    CameraControllerData data = new CameraControllerData();
//        //    data.FOVOffset = FOVOffset.floatValue;
//        //    data.screenRotateOffset = screenRotateOffset.floatValue;
//        //    data.screenHorizontalOffset = screenHorizontalOffset.floatValue;
//        //    data.screenVerticalOffset = screenVerticalOffset.floatValue;
//        //    data.horizontalAngleOffset = horizontalAngleOffset.floatValue;
//        //    data.pitchOffset = pitchOffset.floatValue;
//        //    data.distanceOffset = distanceOffset.floatValue;

//        //    if (Directory.Exists(directory) == false)
//        //    {
//        //        Directory.CreateDirectory(directory);
//        //    }
//        //    string json = JsonUtility.ToJson(data);
//        //    StreamWriter sw = new StreamWriter(directory + "CameraControllerParam" + target.GetInstanceID().ToString() + ".json");
//        //    sw.Write(json);
//        //    sw.Close();
//        //}
//    }
//}

//#endif