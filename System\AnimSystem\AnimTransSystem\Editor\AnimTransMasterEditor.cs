#if UNITY_EDITOR
using UnityEditor;
using CEditor;

namespace CEditor
{
    [CustomEditor(typeof(AnimTransMaster))] //关联之前的脚本
    public class AnimTransMasterEditor : Editor
    {
        private SerializedObject obj; //序列化

        private SerializedProperty currentMode;
        private SerializedProperty useAutoTransTime, useAutoMirror, transRiskCalculateOptimizeArmApproachingPose, fullAutoTransCheckStep, TransTimeRange, useFaceBone;
        private SerializedProperty showGrid, viewLeft, showDebugStep, debugStep, debugObject, CameraHolder, FrontGrid, LeftGrid;
        private SerializedProperty XRange, YRange, ZRange, XClipLine, YClipLine, ZClipLine, rightHandPosition, leftHandPosition, rightElbowPosition, leftElbowPosition;


        void OnEnable()
        {
            obj = new SerializedObject(target);

            currentMode = obj.FindProperty("currentMode");
            useAutoTransTime = obj.FindProperty("useAutoTransTime");
            useAutoMirror = obj.FindProperty("useAutoMirror");
            transRiskCalculateOptimizeArmApproachingPose = obj.FindProperty("transRiskCalculateOptimizeArmApproachingPose");
            fullAutoTransCheckStep = obj.FindProperty("fullAutoTransCheckStep");
            TransTimeRange = obj.FindProperty("TransTimeRange");

            useFaceBone = obj.FindProperty("useFaceBone");

            showGrid = obj.FindProperty("showGrid");
            viewLeft = obj.FindProperty("viewLeft");
            showDebugStep = obj.FindProperty("showDebugStep");
            debugStep = obj.FindProperty("debugStep");
            debugObject = obj.FindProperty("debugObject");
            CameraHolder = obj.FindProperty("CameraHolder");
            FrontGrid = obj.FindProperty("FrontGrid");
            LeftGrid = obj.FindProperty("LeftGrid");

            XRange = obj.FindProperty("XRange");
            YRange = obj.FindProperty("YRange");
            ZRange = obj.FindProperty("ZRange");
            XClipLine = obj.FindProperty("XClipLine");
            YClipLine = obj.FindProperty("YClipLine");
            ZClipLine = obj.FindProperty("ZClipLine");
            rightHandPosition = obj.FindProperty("rightHandPosition");
            leftHandPosition = obj.FindProperty("leftHandPosition");
            rightElbowPosition = obj.FindProperty("rightElbowPosition");
            leftElbowPosition = obj.FindProperty("leftElbowPosition");
        }


        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            obj.Update();


            EditorGUILayout.PropertyField(currentMode);

            EditorGUILayout.PropertyField(useAutoTransTime);
            EditorGUILayout.PropertyField(useAutoMirror);
            if (useAutoTransTime.boolValue || useAutoMirror.boolValue)
            {
                EditorGUILayout.PropertyField(transRiskCalculateOptimizeArmApproachingPose);
                EditorGUILayout.PropertyField(fullAutoTransCheckStep);
                EditorGUILayout.PropertyField(TransTimeRange);
            }

            if (currentMode.enumValueIndex == ((int)MasterMode.AnimTrans))
            {
                EditorGUILayout.PropertyField(useFaceBone);
            }
            else if (currentMode.enumValueIndex == ((int)MasterMode.Debug))
            {
                EditorGUILayout.PropertyField(debugObject);
                EditorGUILayout.PropertyField(CameraHolder);
                EditorGUILayout.PropertyField(FrontGrid);
                EditorGUILayout.PropertyField(LeftGrid);

                EditorGUILayout.PropertyField(showGrid);
                EditorGUILayout.PropertyField(viewLeft);
                EditorGUILayout.PropertyField(showDebugStep);
                if (showDebugStep.boolValue)
                {
                    EditorGUILayout.PropertyField(debugStep);
                }
            }
            else if (currentMode.enumValueIndex == ((int)MasterMode.AutoTagCalculate))
            {
                EditorGUILayout.PropertyField(XRange);
                EditorGUILayout.PropertyField(YRange);
                EditorGUILayout.PropertyField(ZRange);
                EditorGUILayout.PropertyField(XClipLine);
                EditorGUILayout.PropertyField(YClipLine);
                EditorGUILayout.PropertyField(ZClipLine);
                EditorGUILayout.PropertyField(rightHandPosition);
                EditorGUILayout.PropertyField(leftHandPosition);
                EditorGUILayout.PropertyField(rightElbowPosition);
                EditorGUILayout.PropertyField(leftElbowPosition);
            }
            obj.ApplyModifiedProperties();
        }
    }
}
#endif