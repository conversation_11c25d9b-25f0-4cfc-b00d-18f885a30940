using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace CEditor
{
    [ExecuteAlways]
    public class CEditorTest : MonoBehaviour
    {
        public Camera camera;
        public int StationTemplateID;
        public SwitchStationTemplateMode switchMode;
        public GameObject actorPerfab;
        public List<GameObject> actorObject;
        public int slotIndex;
        //public int camID;
        //public string camTargetTag = "A";
        public AnimTransPlayableConfig animTransPlayableConfig;

        public bool SetStationTemplateID_Test = false;
        public bool SetStationTemplateID_SwitchMode_Test = false;
        //public bool SetCharacterSlots_Test = false;
        public bool SetCharacterSlot_Test = false;
        //public bool SetCameraID_Test = false;
        //public bool SetCameraGroupID_Test = false;
        public bool SetCameraRef_Test = false;
        public bool Clear_Test = false;
        public bool CompletelyClear_Test = false;
        //public bool GetTargetSlotList = false;

        public bool GoldFinger = false;


        // Start is called before the first frame update
        void Start()
        {

        }

        // Update is called once per frame
        void Update()
        {
            if (SetStationTemplateID_Test)
            {
                SetStationTemplateID_Test = false;
                StationTemplateManager.instance.SetStationTemplateID(StationTemplateID);
            }

            if (SetStationTemplateID_SwitchMode_Test)
            {
                SetStationTemplateID_SwitchMode_Test = false;
                StationTemplateManager.instance.SetStationTemplateID(StationTemplateID, switchMode);
            }

            //if (SetCharacterSlots_Test)
            //{
            //    SetCharacterSlots_Test = false;
            //    //StationTemplateManager.instance.SetCharacterSlots(new List<ActorSlotSignUpInfo>()
            //    //{
            //    //    new ActorSlotSignUpInfo
            //    //{
            //    //    go = Instantiate(actorPerfab),
            //    //    slotIndex = 0,
            //    //    cfg = animTransPlayableConfig
            //    //}
            //    //});
            //}

            if (SetCharacterSlot_Test)
            {
                GameObject go = null;
                if (actorPerfab != null)
                {
                    go = Instantiate(actorPerfab);
                }
                SetCharacterSlot_Test = false;
                StationTemplateManager.instance.SetCharacterSlot(new ActorSlotSignUpInfo
                {
                    go = go,
                    slotIndex = slotIndex,
                    cfg = animTransPlayableConfig
                },
                false);
            }

            //if (SetCameraID_Test)
            //{
            //    SetCameraID_Test = false;

            //    List<string> list = StationTemplateManager.instance.GetTargetSlotsList();
            //    foreach (string slot in list)
            //    {
            //        Debug.Log("-------------------" + slot);
            //    }

            //    CameraIDTransParam param = new CameraIDTransParam();
            //    param.camID = camID;
            //    param.cameraPerformConfig = new CameraPerformConfig();
            //    param.cameraPerformConfig.cameraControllerData = new CameraControllerData();
            //    param.cameraPerformConfig.cameraSwitchPerform = new CameraSwitchPerformConfig();
            //    StationTemplateManager.instance.SetCameraID(param, camTargetTag);
            //}

            //if (SetCameraGroupID_Test)
            //{
            //    SetCameraGroupID_Test = false;
            //}

            if (SetCameraRef_Test)
            {
                SetCameraRef_Test = false;
                if (StationTemplateManager.instance == null)
                {
                    Debug.Log("--------------------------No Instance");
                    return;
                }
                StationTemplateManager.instance.SetCameraRef(camera);
            }

            if (Clear_Test)
            {
                Clear_Test = false;
                StationTemplateManager.instance.Clear();
            }

            if (CompletelyClear_Test)
            {
                CompletelyClear_Test = false;
                StationTemplateManager.instance.CompletelyClear();
            }

            if (GoldFinger)
            {
                GoldFinger = false;
                if (StationTemplateManager.instance != null)
                {
                    if (StationTemplateManager.instance.actorSlots != null && StationTemplateManager.instance.actorSlots.Count > 0)
                    {
                        for (int i = 0; i < StationTemplateManager.instance.actorSlots.Count; i++)
                        {
#if UNITY_EDITOR
                            if (StationTemplateManager.instance.actorSlots[i].actor != null)
                                DestroyImmediate(StationTemplateManager.instance.actorSlots[i].actor);
#else
                            if (StationTemplateManager.instance.actorSlots[i].actor != null)
                                Destroy(StationTemplateManager.instance.actorSlots[i].actor);
#endif
                        }
                    }
                    StationTemplateManager.instance.Clear();

                    StationTemplateManager.instance.SetStationTemplateID(StationTemplateID, switchMode);

                    if (actorObject.Count > 0 && actorObject[0] != null)
                    {
                        for (int i = 0; i < StationTemplateManager.instance.actorSlots.Count; i++)
                        {
                            StationTemplateManager.instance.SetCharacterSlot(new ActorSlotSignUpInfo()
                            {
                                go = actorObject[i],
                                slotIndex = i,
                                cfg = animTransPlayableConfig
                            }, false);
                        }
                    }
                    else
                    {
                        for (int i = 0; i < StationTemplateManager.instance.actorSlots.Count; i++)
                        {
                            StationTemplateManager.instance.SetCharacterSlot(new ActorSlotSignUpInfo()
                            {
                                go = Instantiate(actorPerfab),
                                slotIndex = i,
                                cfg = animTransPlayableConfig
                            }, false);
                        }
                    }
                    

                    StationTemplateManager.instance.SetCameraRef(camera);
                }
            }

            //if (GetTargetSlotList)
            //{
            //    GetTargetSlotList = false;
            //    foreach (string s in StationTemplateManager.GetTargetSlotsListV2(StationTemplateID))
            //    {
            //        Debug.Log("[" + s + "]");
            //    }
            //}
        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

