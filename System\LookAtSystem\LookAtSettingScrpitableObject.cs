﻿using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;
using Slate;

namespace CEditor
{
    // public class SliderSuffixAttribute : PropertyAttribute
    // {
    //     public float MinValue { get; private set; }
    //     public float MaxValue { get; private set; }
    //     public string Suffix { get; private set; }
    //     public SuffixMode Mode { get; private set; }
    //
    //     public enum SuffixMode
    //     {
    //         FromMinToMax,
    //         FromMaxToMin
    //     }
    //
    //     public SliderSuffixAttribute(float minValue, float maxValue, SuffixMode mode = SuffixMode.FromMinToMax,
    //         string suffix = "")
    //     {
    //         MinValue = minValue;
    //         MaxValue = maxValue;
    //         Mode = mode;
    //         Suffix = suffix;
    //     }
    // }

    [System.Serializable]
    public struct EyeBlendshapeSetting
    {
        public string LookOutLeft;
        public string LookInLeft;
        public string LookUpLeft;
        public string LookDownLeft;
        public string LookOutRight;
        public string LookInRight;
        public string LookUpRight;
        public string LookDownRight;

        public Vector2 LeftEyeLeftRangeMinMax;
        public Vector2 LeftEyeRightRangeMinMax;
        public Vector2 RightEyeLeftRangeMinMax;
        public Vector2 RightEyeRightRangeMinMax;
    }

    [CreateAssetMenu(fileName = "LookAtSetting", menuName = "Look At Setting/Look At Setup")]
    public class LookAtSettingScrpitableObject : ScriptableObject
    {
        // Basic Settings
        [Header("Basic Settings")] [Tooltip("Whether to initialize Look Animator")]
        public bool InitLookAnimator = true;

        [Tooltip("Whether to initialize Eye Animator")]
        public bool InitEyeAnimator = true;


        // LookBones Weights
        [Header("Look Bones Weights")]
        [Tooltip("Weight values for each LookBone, affects the smoothness of head following")]
        [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
        public List<float> LookBoneWeights = new List<float> { 0.4200521f, 0.2021357f, 0.3816873f };

        [Tooltip("Call init on first frame or next frame")]
        public bool isStartAfterTPose = false;
        
        // Compensation Settings
        [Header("Compensation Settings")]
        [Tooltip("Compensation weight, affects the compensation degree of other body parts")]
        [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
        public float CompensationWeight = 0.7f;

        [Tooltip("Position compensation value, affects the position compensation of other body parts")]
        [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
        public float CompensatePositions = 0.1f;
        
        [Tooltip("This variable is making rotation animation become very smooth (but also slower).\nIt is enabling smooth rotation transition in bone rotations")]
        [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
        public float UltraSmoother = 0.1f;
        
        // Rotation Settings
        [Header("Rotation Settings")]
        [Tooltip("Rotation speed, affects how fast the head follows the target")]
        [SliderSuffix(0f, 2f, SliderSuffixAttribute.SuffixMode.FromMinToMax, "")]
        public float RotationSpeed = 0.2f;

        [Tooltip("The influence degree of Look Animator")]
        [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
        public float LookAnimatorAmount = 0.8f;

        [Tooltip("Angle threshold to stop looking upward")]
        [SliderSuffix(0f, 180f, SliderSuffixAttribute.SuffixMode.FromMinToMax, "°")]
        public float StopLookingAbove = 65f;

        [Tooltip("Angle threshold to start looking upward")]
        [SliderSuffix(0f, 45f, SliderSuffixAttribute.SuffixMode.FromMinToMax, "°")]
        public float LookWhenAbove = 9f;

        [Tooltip("Angle to maintain opposite rotation until")]
        [SliderSuffix(0f, 45f, SliderSuffixAttribute.SuffixMode.FromMinToMax, "°")]
        public float HoldRotateToOppositeUntil = 13f;

        // Rotation Limits
        [Header("Rotation Limits")] [Tooltip("X-axis rotation limit range")]
        public Vector2 XRotationLimitsMinMax = new Vector2(-65f, 65f);

        [Tooltip("Y-axis rotation limit range")]
        public Vector2 YRotationLimitsMinMax = new Vector2(-15f, 40f);

        [Tooltip("X-axis elastic range")] [SliderSuffix(0f, 60f, SliderSuffixAttribute.SuffixMode.FromMinToMax, "°")]
        public float XElasticRange = 20f;

        [Tooltip("Y-axis elastic range")] [SliderSuffix(0f, 45f, SliderSuffixAttribute.SuffixMode.FromMinToMax, "°")]
        public float YElasticRange = 15f;

        [Tooltip("Initial X-axis elastic range")]
        [SliderSuffix(0f, 90f, SliderSuffixAttribute.SuffixMode.FromMinToMax, "°")]
        public float StartLookElasticRangeX = 40f;

        [Tooltip("Initial Y-axis elastic range")]
        [SliderSuffix(0f, 90f, SliderSuffixAttribute.SuffixMode.FromMinToMax, "°")]
        public float StartLookElasticRangeY = 25f;

        // Eye Animator Settings
        [Header("Eye Animator Settings")]
        [Tooltip("Degree of random eye movement")]
        [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
        public float EyesRandomMovement = 0.5f;

        [Tooltip("speed of eye movement")]
        [SliderSuffix(0f, 1f, SliderSuffixAttribute.SuffixMode.FromMinToMax)]
        public float EyeSpeed = 1f;

        [Tooltip("Eye angle threshold to stop looking upward")]
        [SliderSuffix(0f, 180f, SliderSuffixAttribute.SuffixMode.FromMinToMax, "°")]
        public float StopLookAbove = 18f;

        // Eye Clamp
        [Header("Eye Clamp")] [Tooltip("Horizontal eye rotation limit range")]
        public Vector2 EyesClampHorizontalMinMax = new Vector2(-15f, 15f);

        [Tooltip("Vertical eye rotation limit range")]
        public Vector2 EyesClampVerticalMinMax = new Vector2(-5f, 5f);

        // Eye Blendshape Settings
        [Header("Eye Blendshape Settings")] [Tooltip("Settings for eye blendshapes")]
        public EyeBlendshapeSetting EyeBlendshapeSettings = new EyeBlendshapeSetting
        {
            LookOutLeft = "LookOut_L",
            LookInLeft = "LoockIn_L",
            LookUpLeft = "Lookup_L",
            LookDownLeft = "LookDown_L",
            LookOutRight = "LookOut_R",
            LookInRight = "LoockIn_R",
            LookUpRight = "Lookup_R",
            LookDownRight = "LookDown_R",
            LeftEyeLeftRangeMinMax = new Vector2(0, 100),
            LeftEyeRightRangeMinMax = new Vector2(0, 100),
            RightEyeLeftRangeMinMax = new Vector2(0, 100),
            RightEyeRightRangeMinMax = new Vector2(0, 100)
        };

        // Sync Settings
        [Header("Sync Settings")] [Tooltip("Whether to sync clamping ranges")]
        public bool SyncClamping = false;

        [Tooltip("Whether to sync ranges")] public bool SyncRanges = false;

        [Tooltip("Whether to sync usage amount")]
        public bool SyncUseAmount = false;

        [Tooltip("Whether to sync target")] public bool SyncTarget = true;
    }
}