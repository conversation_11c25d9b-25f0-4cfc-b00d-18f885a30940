﻿using UnityEngine;

namespace Slate
{

    public class SliderSuffixAttribute : PropertyAttribute
    {
        public float MinValue { get; private set; }
        public float MaxValue { get; private set; }
        public string Suffix { get; private set; }
        public SuffixMode Mode { get; private set; }

        public enum SuffixMode
        {
            FromMinToMax,
            FromMaxToMin
        }

        public SliderSuffixAttribute(float minValue, float maxValue, SuffixMode mode = SuffixMode.FromMinToMax,
            string suffix = "")
        {
            MinValue = minValue;
            MaxValue = maxValue;
            Mode = mode;
            Suffix = suffix;
        }
    }
}