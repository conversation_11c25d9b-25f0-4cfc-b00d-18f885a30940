using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using FIMSpace.FLook;
using FIMSpace.FEyes;
using Cysharp.Text;

namespace CEditor
{
    public struct LookAtParams
    {        
        public Transform s_lookAtTarget;
        public float s_bodyLookAtStrenth;
        public float s_bodyRotateSpeed;
        public float s_bodyLookRange;
        public float s_eyesLookAtStrenth;
        public float s_eyesRotateSpeed;
        public float s_eyesLookRange;
        public float s_eyesRandomMoveAmount;
        public float s_eyesRandomMoveSpeed;
        public Vector2 s_eyesRandomScaleVH;
        public FEyesAnimator.FERandomMovementType s_eyesRandomMoveType;
    }

    [ExecuteAlways]
    public class LookAtManager : MonoBehaviour
    {
        public LookAtSettingScrpitableObject lookAtSetting;
        public FLookAnimator fLookAnimator;
        public FEyesAnimator fEyeAnimator;



        private void Awake()
        {
        }

        void Start()
        {
            // Init();
        }

        private void Update()
        {
        }

        public void UpdateParams(LookAtParams lookparams)
        {
            if(fLookAnimator != null){
                fLookAnimator.ObjectToFollow = lookparams.s_lookAtTarget;
                fLookAnimator.SwitchLookingAmount(lookparams.s_bodyLookAtStrenth);
                fLookAnimator.RotationSpeed = lookparams.s_bodyRotateSpeed;
                fLookAnimator.StopLookingAbove = lookparams.s_bodyLookRange;
            }else{
                Debug.LogWarning("[Lookatmanager] fLookAnimator is null, ignore params update");
            }

            if(fEyeAnimator != null){
                fEyeAnimator.BlendToEyesFollowAnimation(lookparams.s_eyesLookAtStrenth);

                fEyeAnimator.StopLookAbove = lookparams.s_eyesLookRange;
                fEyeAnimator.EyesSpeed = lookparams.s_eyesRotateSpeed;

                fEyeAnimator.EyesRandomMovement = lookparams.s_eyesRandomMoveAmount;
                fEyeAnimator.RandomMovementAxisScale = lookparams.s_eyesRandomScaleVH;
                fEyeAnimator.RandomizingSpeed = lookparams.s_eyesRandomMoveSpeed;
                fEyeAnimator.RandomMovementPreset = lookparams.s_eyesRandomMoveType;
            }else{
                Debug.LogWarning("[Lookatmanager] fEyeAnimator is null, ignore params update");
            }

        }
        
        public void Init()
        {
            if (lookAtSetting == null)
            {
                Debug.LogWarning("[Lookatmanager] lookAtSetting is null, please assign it in inspector");
                return;
            }

            if (lookAtSetting.InitLookAnimator)
            {
                if (gameObject.GetComponent<FLookAnimator>() != null)
                {
#if UNITY_EDITOR
                    DestroyImmediate(gameObject.GetComponent<FLookAnimator>());
#else
                    Destroy(gameObject.GetComponent<FLookAnimator>());
#endif
                }
                if (gameObject.GetComponent<FLookAnimator>() == null)
                {
                    fLookAnimator = this.gameObject.AddComponent<FLookAnimator>();
                    fLookAnimator.FindBaseTransform();
                    // Debug.Log("fLookAnimator.BaseTransform.name "+fLookAnimator.BaseTransform.name);
                    FindHeadBoneAnimator(fLookAnimator);

                    // 使用ScriptableObject中的参数
                    fLookAnimator.BackBonesCount = lookAtSetting.LookBoneWeights.Count;
                    fLookAnimator.StartAfterTPose = lookAtSetting.isStartAfterTPose;
                    fLookAnimator.DupCallOnValidate();

                    fLookAnimator.AutoBackbonesWeights = false;
                    if (fLookAnimator.LookBones.Count == fLookAnimator.BackBonesCount + 1)
                    {
                        // 使用ScriptableObject中的权重设置
                        for (int i = 1; i < fLookAnimator.LookBones.Count; i++)
                        {
                            if (i - 1 < lookAtSetting.LookBoneWeights.Count)
                            {
                                fLookAnimator.LookBones[i].lookWeight = lookAtSetting.LookBoneWeights[i - 1];
                            }
                            else
                            {
                                Debug.LogWarning(ZString.Format("LookBoneWeights list doesn't have enough elements for bone {0}", i));
                            }
                        }
                    }
                    else
                    {
                        Debug.LogWarning("fLookAnimator: look bone setup failed, lookbone mismatch backbone count + 1");
                    }

                    // 设置补偿参数
                    FindCompensationBones(fLookAnimator);
                    fLookAnimator.CompensationWeight = lookAtSetting.CompensationWeight;
                    fLookAnimator.CompensatePositions = lookAtSetting.CompensatePositions;

                    // 设置旋转和LookAt参数
                    fLookAnimator.RotationSpeed = lookAtSetting.RotationSpeed;
                    fLookAnimator.UltraSmoother = lookAtSetting.UltraSmoother;
                    fLookAnimator.LookAnimatorAmount = lookAtSetting.LookAnimatorAmount;
                    fLookAnimator.StopLookingAbove = lookAtSetting.StopLookingAbove;
                    fLookAnimator.LookWhenAbove = lookAtSetting.LookWhenAbove;
                    fLookAnimator.HoldRotateToOppositeUntil = lookAtSetting.HoldRotateToOppositeUntil;
                    fLookAnimator.XRotationLimits = lookAtSetting.XRotationLimitsMinMax;
                    fLookAnimator.XElasticRange = lookAtSetting.XElasticRange;
                    fLookAnimator.StartLookElasticRangeX = lookAtSetting.StartLookElasticRangeX;
                    fLookAnimator.YRotationLimits = lookAtSetting.YRotationLimitsMinMax;
                    fLookAnimator.YElasticRange = lookAtSetting.YElasticRange;
                    fLookAnimator.StartLookElasticRangeY = lookAtSetting.StartLookElasticRangeY;
                }
            }

            if (lookAtSetting.InitEyeAnimator)
            {
                if (gameObject.GetComponent<FEyesAnimator>() != null)
                {
#if UNITY_EDITOR
                    DestroyImmediate(gameObject.GetComponent<FEyesAnimator>());
#else
                    Destroy(gameObject.GetComponent<FEyesAnimator>());
#endif
                }
                if (gameObject.GetComponent<FEyesAnimator>() == null)
                {
                    fEyeAnimator = this.gameObject.AddComponent<FEyesAnimator>();
                    FindHeadEyeBoneBlendShape(fEyeAnimator, lookAtSetting.EyeBlendshapeSettings);

                    // 设置眼睛动画参数
                    fEyeAnimator.EyesSpeed = lookAtSetting.EyeSpeed;
                    fEyeAnimator.EyesRandomMovement = lookAtSetting.EyesRandomMovement;
                    fEyeAnimator.StopLookAbove = lookAtSetting.StopLookAbove;
                    fEyeAnimator.EyesClampHorizontal = lookAtSetting.EyesClampHorizontalMinMax;
                    fEyeAnimator.EyesClampVertical = lookAtSetting.EyesClampVerticalMinMax;

                    fEyeAnimator.LookAnimator = fLookAnimator;
                    fEyeAnimator.SyncClamping = lookAtSetting.SyncClamping;
                    fEyeAnimator.SyncRanges = lookAtSetting.SyncRanges;
                    fEyeAnimator.SyncUseAmount = lookAtSetting.SyncUseAmount;
                    fEyeAnimator.SyncTarget = lookAtSetting.SyncTarget;
                }
            }

            if (fLookAnimator != null)
                fLookAnimator.ObjectToFollow = null;
            if (fEyeAnimator != null)
            {
                fEyeAnimator.EyesTarget = null;
                fEyeAnimator.FollowTargetAmount = 0;
            }
        }


        private void FindHeadEyeBoneBlendShape(FEyesAnimator eyeLook, EyeBlendshapeSetting eyebsSetting)
        {
            Transform root = eyeLook.transform;
            if (eyeLook.BaseTransform) root = eyeLook.BaseTransform;
            Animator animator = root.GetComponentInChildren<Animator>();
            Transform animatorHeadBone = null;
            if(animator == null){
                Debug.LogWarning("[Lookatmanager] cannot find animator in gameObject, skip eye blendshape setup");
                return;
            }

            if (!animator.isHuman)
            {
                Debug.LogWarning("[Lookatmanager] animator is not humanoid, skip eye blendshape setup");
                return;
            }
            animatorHeadBone = animator.GetBoneTransform(HumanBodyBones.Head);
            if(animatorHeadBone == null){
                Debug.LogWarning("[Lookatmanager] cannot find head bone in animator, skip eye blendshape setup");
                return;
            }

            eyeLook.HeadReference = animatorHeadBone;
            FindEyeBlendShape(eyeLook, eyebsSetting);
        }

        private void FindEyeBlendShape(FEyesAnimator eyeLook, EyeBlendshapeSetting eyebsSetting)
        {
            SkinnedMeshRenderer faceRenderer = null;
            foreach (SkinnedMeshRenderer skinmeshrender in eyeLook.GetComponentsInChildren<SkinnedMeshRenderer>())
            {
                if (skinmeshrender.name.Contains("Face"))
                {
                    faceRenderer = skinmeshrender;
                    break;
                }
            }

            if (faceRenderer == null )
            {
                Debug.LogWarning("[Lookatmanager] Could not find face renderer in children");
                return;
            }

            if (faceRenderer.sharedMesh.blendShapeCount == 0)
            {
                Debug.LogWarning("[Lookatmanager] no blendshapes in given face_mesh");
                return;
            }

            eyeLook.Eyes.Clear();            
            eyeLook.Eyes.Add(faceRenderer.transform);
            eyeLook.Eyes.Add(faceRenderer.transform);
            eyeLook.UpdateLists();

            if (eyeLook.EyeSetups.Count != 2)
            {
                Debug.LogError("[Lookatmanager] EyeSetups count is not 2, setup failed");
                return;
            }

            foreach (FEyesAnimator.EyeSetup setup in eyeLook.EyeSetups)
            {
                setup.ControlType = FEyesAnimator.EyeSetup.EEyeControlType.Blendshape;
                setup.BlendshapeMesh = faceRenderer;
            }

            for (int i = 0; i < faceRenderer.sharedMesh.blendShapeCount; i++)
            {
                string blendShapeName = faceRenderer.sharedMesh.GetBlendShapeName(i);
                if (blendShapeName.Contains(eyebsSetting.LookOutLeft))
                {
                    eyeLook.EyeSetups[0].EyeLeftShape = i;
                    continue;
                }

                if (blendShapeName.Contains(eyebsSetting.LookInLeft))
                {
                    eyeLook.EyeSetups[0].EyeRightShape = i;
                    continue;
                }

                if (blendShapeName.Contains(eyebsSetting.LookUpLeft))
                {
                    eyeLook.EyeSetups[0].EyeUpShape = i;
                    continue;
                }

                if (blendShapeName.Contains(eyebsSetting.LookDownLeft))
                {
                    eyeLook.EyeSetups[0].EyeDownShape = i;
                    continue;
                }

                if (blendShapeName.Contains(eyebsSetting.LookOutRight))
                {
                    eyeLook.EyeSetups[1].EyeRightShape = i;
                    continue;
                }

                if (blendShapeName.Contains(eyebsSetting.LookInRight))
                {
                    eyeLook.EyeSetups[1].EyeLeftShape = i;
                    continue;
                }

                if (blendShapeName.Contains(eyebsSetting.LookUpRight))
                {
                    eyeLook.EyeSetups[1].EyeUpShape = i;
                    continue;
                }

                if (blendShapeName.Contains(eyebsSetting.LookDownRight))
                {
                    eyeLook.EyeSetups[1].EyeDownShape = i;
                    continue;
                }
            }

            eyeLook.EyeSetups[0].MinMaxValueLeft = eyebsSetting.LeftEyeLeftRangeMinMax;
            eyeLook.EyeSetups[0].MinMaxValueRight = eyebsSetting.LeftEyeRightRangeMinMax;
            eyeLook.EyeSetups[1].MinMaxValueLeft = eyebsSetting.RightEyeLeftRangeMinMax;
            eyeLook.EyeSetups[1].MinMaxValueRight = eyebsSetting.RightEyeRightRangeMinMax;
        }

        private void FindCompensationBones(FLookAnimator headLook)
        {
            // First let's check if it's humanoid character, then we can get head bone transform from it
            Transform root = headLook.transform;
            if (headLook.BaseTransform) root = headLook.BaseTransform;

            Animator animator = root.GetComponentInChildren<Animator>();
            if(animator == null){
                Debug.LogWarning("[Lookatmanager] cannot find animator in gameObject, skip compensation bones setup");
                return;
            }
            List<Transform> compensationBones = new List<Transform>();

            if (!animator.isHuman)
            {
                Debug.LogWarning("[Lookatmanager] animator is not humanoid, skip compensation bones setup, please check if the animator is humanoid");
                return;
            }
            
            Transform b = animator.GetBoneTransform(HumanBodyBones.LeftShoulder);
            if (b) compensationBones.Add(b);

            b = animator.GetBoneTransform(HumanBodyBones.RightShoulder);
            if (b) compensationBones.Add(b);

            b = animator.GetBoneTransform(HumanBodyBones.LeftUpperArm);
            if (b) compensationBones.Add(b);

            b = animator.GetBoneTransform(HumanBodyBones.RightUpperArm);
            if (b) compensationBones.Add(b);

            if (compensationBones.Count != 0)
            {
                headLook.CompensationBones = new List<FLookAnimator.CompensationBone>();

                for (int i = 0; i < compensationBones.Count; i++)
                {
                    headLook.CompensationBones.Add(new FLookAnimator.CompensationBone(compensationBones[i]));
                }

                for (int c = headLook.CompensationBones.Count - 1; c >= 0; c--)
                {
                    if (headLook.CompensationBones[c].Transform == null) headLook.CompensationBones.RemoveAt(c);
                }

                // compensationBonesCount = headLook.CompensationBones.Count;
            }
        }


        private void FindHeadBoneAnimator(FLookAnimator headLook)
        {
            // First let's check if it's humanoid character, then we can get head bone transform from it
            Transform root = headLook.transform;
            if (headLook.BaseTransform) root = headLook.BaseTransform;

            Animator animator = root.GetComponentInChildren<Animator>();
            Transform animatorHeadBone = null;
            if(animator == null){
                Debug.LogWarning("[Lookatmanager] cannot find animator in gameObject, skip compensation bones setup");
                return;
            }
    
            if (!animator.isHuman)
            {
                Debug.LogWarning("[Lookatmanager] animator is not humanoid, skip compensation bones setup, please check if the animator is humanoid");
                return;
            }
            animatorHeadBone = animator.GetBoneTransform(HumanBodyBones.Head);
            if (animatorHeadBone == null)
            {
                Debug.LogWarning("[Lookatmanager] Couldn't find head bone as lead bone in humanoid animator, skip head bone setup");
                return;
            }
            headLook.LeadBone = animatorHeadBone;
        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}