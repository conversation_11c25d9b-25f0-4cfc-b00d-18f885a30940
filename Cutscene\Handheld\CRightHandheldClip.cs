using System.Collections;
using System.Linq;
using CEditor;
using Sirenix.OdinInspector;
using UnityEngine;
using VGame.Framework;

namespace Slate.ActionClips
{
    [Name("手持物区域配置")]
    [Attachable(typeof(CRightHandheldTrack))]
    [Category("C类编辑器")]
    public class CRightHandheldClip : BaseDialogueActorActionClip<CHandHeldParams>
    {
        private GameObject performer;
        private HandheldManage handheldManager;

        [SerializeField, HideInInspector]
        private float _length = 1;

        public override float length
        {
            get { return _length; }
            set { _length = value; }
        }

        public override string info
        {
            get
            {
                return "手持物片段";
            }
        }

        protected override void OnCreate()
        {
            base.OnCreate();
            GetCfg().refBoneName = "Bip01_R_Hand";
        }

        private void SetEnter()
        {
            performer = GetActor();
            handheldManager = performer.GetComponent<HandheldManage>();

            if (performer != null && handheldManager != null)
            {
                handheldManager.ClearRightHandHoldState();
                if (handheldManager.rightHandHoldingMaintainTrigger)
                    handheldManager.rightHandHoldingMaintainTrigger = false;

                HandHoldState handHoldState = new HandHoldState();
                handHoldState.overwriteHandAnim = GetCfg().overwriteHandAnim;
                handHoldState.isHoldingObject = GetCfg().isHoldingObject;
                handHoldState.useHoldObjectAnim = GetCfg().useHoldObjectAnim;
                handHoldState.overwriteAnim = GetCfg().overwriteAnim;
                handHoldState.holdObject = GetCfg().holdObject;
                handHoldState.holdObjectAnimation = GetCfg().holdObjectAnimation;
                handHoldState.refBoneName = GetCfg().refBoneName;
                handHoldState.positionOffset = GetCfg().positionOffset;
                handHoldState.rotateOffset = GetCfg().rotateOffset;
                handHoldState.localscale = GetCfg().localscale;

                handheldManager.rightHandheldState = handHoldState;
                handheldManager.useRightHandheldUpdateControl = true;
                handheldManager.rightHandHoldingTrigger = true;
                handheldManager.ForceUpdateRightHnadheldState();
            }
        }

        //Called in forward sampling when the clip is entered
        protected override void OnEnter()
        {
            base.OnEnter();
            SetEnter();
        }

        //Called in backwards sampling when the clip is entered.
        protected override void OnReverseEnter()
        {
            base.OnReverseEnter();
            SetEnter();
        }
        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime)
        {
            if (performer != null && handheldManager != null)
            {
                handheldManager.useRightHandheldUpdateControl = true;
                handheldManager.rightHandHoldingTrigger = true;
                handheldManager.ForceUpdateRightHnadheldState();
            }
        }

        //Called in forwards sampling when the clip exits
        protected override void OnExit() 
        {
            if (performer != null && handheldManager != null)
            {
                handheldManager.useRightHandheldUpdateControl = true;
                handheldManager.rightHandHoldingTrigger = true;
                handheldManager.ForceUpdateRightHnadheldState();
            }
        }



        //Called in backwards sampling when the clip exits.
        protected override void OnReverse() { }
    }
}