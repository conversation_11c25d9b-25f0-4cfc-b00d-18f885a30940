using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace CEditor
{
    public enum NamingConventionTypes
    {
        ActorGroup,
        CameraGroup,
        HangingGroup,
        CloneCameraGroup,
        TargetGroup,
        FixFollowPoint,
        DerCamOriSlotGroup,
        DerCamPawnGroup,
        StationTemplateHistory,
        CameraBrain,
        InitBoneTreeStand,
        InitBoneTreeSit
    }

    public static class StationTemplateDataCollector
    {
        public static Dictionary<NamingConventionTypes, string> namingConvention = new Dictionary<NamingConventionTypes, string>()
        {
            {NamingConventionTypes.ActorGroup, "Actor Group"},
            {NamingConventionTypes.CameraGroup, "Camera Group"},
            {NamingConventionTypes.HangingGroup, "Hanging Group"},
            {NamingConventionTypes.CloneCameraGroup, "Clone Camera Group"},
            {NamingConventionTypes.TargetGroup, "Target Group"},
            {NamingConventionTypes.FixFollowPoint, "Follows"},
            {NamingConventionTypes.DerCamOriSlotGroup, "Ori Actor Group" },
            {NamingConventionTypes.DerCamPawnGroup, "DerCam Pawn Group" },
            {NamingConventionTypes.StationTemplateHistory, "StationTemplateHistory" },
            {NamingConventionTypes.CameraBrain, "StationTemplateCameraBrain" },
            {NamingConventionTypes.InitBoneTreeStand, "InitBoneTree_Stand" },
            {NamingConventionTypes.InitBoneTreeSit, "InitBoneTree_Sit" }
        };
    }
}