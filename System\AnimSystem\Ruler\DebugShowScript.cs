using System.Collections.Generic;
using UnityEngine;

namespace CEditor
{
    [ExecuteAlways]
    public class DebugShowScript : MonoBehaviour
    {
        public GameObject cubesGroup;
        private List<GameObject> cubes = new List<GameObject>();
        //public int spaceXLenght;
        //public int spaceYLenght;
        //public int spaceZLenght;
        private GameObject[,,] voxels = new GameObject[11, 13, 9];
        public Material material;
        // Start is called before the first frame update
        void Start()
        {
            FindAllChildCube(cubesGroup);
            RemapVoxelPosition();
            InitMat();
            //for (int i = -2; i <= 4; i++)
            //{
            //    SetVoxelColor(new Vector3(i, i, i), Color.Lerp(Color.green, Color.red, (float)(i+2) / 6f));
            //    //Material matInst = Instantiate(material);
            //    //voxels[i,i,i].GetComponent<MeshRenderer>().material = matInst;
            //    //matInst.SetColor("_TintColor", Color.Lerp(Color.green, Color.red, (float)i / 8f));
            //}
        }

        // Update is called once per frame
        void Update()
        {

        }

        private void FindAllChildCube(GameObject go)
        {
            if (go != null)
            {
                for (int i = 0; i < go.transform.childCount; i++)
                {
                    if (go.transform.GetChild(i).gameObject.GetComponent<MeshFilter>() != null)
                    {
                        cubes.Add(go.transform.GetChild(i).gameObject);
                    }
                    else
                    {
                        FindAllChildCube(go.transform.GetChild(i).gameObject);
                    }
                }
            }
        }


        private void RemapVoxelPosition()
        {
            Vector3[,,] positions = new Vector3[11, 13, 9];
            List<float> xList = new List<float>();
            List<float> yList = new List<float>();
            List<float> zList = new List<float>();
            foreach (var c in cubes)
            {
                bool has = false;
                foreach (var x in xList)
                {
                    if (Mathf.Abs(x - c.transform.position.x) < 0.01f)
                        has = true;
                }
                if (!has)
                    xList.Add(c.transform.position.x);

                has = false;
                foreach (var y in yList)
                {
                    if (Mathf.Abs(y - c.transform.position.y) < 0.01f)
                        has = true;
                }
                if (!has)
                    yList.Add(c.transform.position.y);

                has = false;
                foreach (var z in zList)
                {
                    if (Mathf.Abs(z - c.transform.position.z) < 0.01f)
                        has = true;
                }
                if (!has)
                    zList.Add(c.transform.position.z);
            }

            xList.Sort();
            yList.Sort();
            zList.Sort();

            foreach (var c in cubes)
            {
                int xIndex = 0;
                int yIndex = 0;
                int zIndex = 0;
                foreach (var x in xList)
                {
                    if (Mathf.Abs(x - c.transform.position.x) > 0.01f)
                        xIndex++;
                    else
                        break;
                }
                foreach (var y in yList)
                {
                    if (Mathf.Abs(y - c.transform.position.y) > 0.01f)
                        yIndex++;
                    else
                        break;
                }
                foreach (var z in zList)
                {
                    if (Mathf.Abs(z - c.transform.position.z) > 0.01f)
                        zIndex++;
                    else
                        break;
                }
                voxels[zIndex, yIndex, xIndex] = c;
            }
        }

        private void InitMat()
        {
            foreach (var c in voxels)
            {
                Material matInst = Instantiate(material);
                matInst.SetColor("_TintColor", Color.black);
                c.GetComponent<MeshRenderer>().material = matInst;
            }
        }

        public void SetVoxelColor(Vector3 index, Color color)
        {
            //Material matInst = Instantiate(material);
            //matInst.SetColor("_TintColor", color);
            voxels[10 - ((int)index.z + 2), (int)index.y + 6, 8 - ((int)index.x + 4)].GetComponent<MeshRenderer>().material.SetColor("_TintColor", color);
        }

        public void BlackAllVoxel()
        {
            foreach (var c in voxels)
                c.GetComponent<MeshRenderer>().material.SetColor("_TintColor", Color.black);
        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

