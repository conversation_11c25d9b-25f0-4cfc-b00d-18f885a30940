#if UNITY_EDITOR
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Slate.ActionClips;
using Sirenix.OdinInspector.Editor;

using VGame.Framework;
using Cinemachine;
using CEditor;
using Cysharp.Text;

[CustomEditor(typeof(CSingleCameraTransClip))] //关联之前的脚本
public class CSingleCameraTransClipEditor : OdinEditor
{
    private SerializedObject obj; //序列化

    //private SerializedProperty TargetTag, CamID;

    //private SerializedProperty lensCutBlendTime, lensCutBlendType, lensCutCustomBlendCurve, filterWeakLensChanges, followPerform, lookAtPerform, FOVOffset, screenRotateOffset, screenHorizontalOffset, screenVerticalOffset, circleAngleOffset, pitchOffset, distanceOffset, cameraPositionOffset, blendHint;

    //private SerializedProperty useCamMove, camMoveType, camMoveBlendStyle, camMoveTime, camMoveCustromBlendCurve, camMoveReverse, camMoveZoomDistance, camMovePanHorizontal, camMovePanVertical, camMovePanRoll, camMoveTrackHorizontal, camMoveTrackVertical, camMoveCircleAngle, camHitchcock;

    //private SerializedProperty useCamShake/*, camShakeType, camShakeAmpli, camShakeFreq*/;

    private Texture2D previewTexture;
    private int newCamID;
    private int prevCamID;
    private string picName = "";
    private string picsBasePath = System.IO.Path.Combine(System.Environment.CurrentDirectory, "CEditorPreview");
    void OnEnable()
    {
        obj = new SerializedObject(target);

        CSingleCameraTransClip script = (CSingleCameraTransClip)target;
        
        // 新进入Editor时，如果缩略图的tex资源为null，但是CamId有值，需要实例化出来一个tex赋值给缩略图
        if (script.CamID != 0 && script.SelectedCameraIcon == null)
        {
            SCameraInfoParams info = new SCameraInfoParams();
            info.Id = script.CamID;
            info.Path = script.SelectedCameraIconPath;
            info.Detail = script.SelectedCameraDetail;
            script.SelectedCameraIcon = CImageBrowserWindowUtility.GenerateTextureByInfo(info);
        }
    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        obj.Update();

        // CSingleCameraTransClip script = (CSingleCameraTransClip)target;
        //
        // //EditorGUILayout.PropertyField(TargetTag, new GUIContent("镜头组标签"));
        // //EditorGUILayout.PropertyField(CamID, new GUIContent("镜头"));
        //
        // newCamID = script.CamID;
        // if (newCamID != prevCamID)
        // {
        //     prevCamID = newCamID;
        //     picName = CfgManager.tables.TbCEditorCameraAsset.GetOrDefault(newCamID).UnityName.Split("_")[0] + "_" +
        //               CfgManager.tables.TbCEditorCameraAsset.GetOrDefault(newCamID).LensID.ToString() + ".png";
        //     string camPicPath = System.IO.Path.Combine(picsBasePath, picName);
        //     if (System.IO.Directory.Exists(picsBasePath) && System.IO.File.Exists(camPicPath))
        //         previewTexture = CEditorUITools.LoadTextureFromFile(camPicPath);
        // }
        //
        // // 显示预览
        // if (previewTexture != null)
        // {
        //     //EditorGUILayout.Space();
        //     EditorGUILayout.LabelField("基础镜头预览");
        //     // 显示图片，宽度为 200，高度自动适应
        //     float aspectRatio = (float)previewTexture.width / previewTexture.height;
        //     float previewWidth = Screen.width / 3;
        //     float previewHeight = previewWidth / aspectRatio;
        //     Rect rect = GUILayoutUtility.GetRect(128, 168);
        //     rect.width = previewWidth;
        //     rect.height = previewHeight;
        //     EditorGUI.DrawPreviewTexture(
        //         rect,
        //         previewTexture
        //     );
        //     EditorGUILayout.Space(40);
        // }
        
        //EditorGUILayout.PropertyField(filterWeakLensChanges, new GUIContent("过滤微小镜头变换"));
        //EditorGUILayout.PropertyField(lensCutBlendTime, new GUIContent("过渡时长"));
        //EditorGUILayout.PropertyField(lensCutBlendType, new GUIContent("过渡曲线类型"));
        //if (lensCutBlendType.enumValueIndex == ((int)CinemachineBlendDefinition.Style.Custom))
        //    EditorGUILayout.PropertyField(lensCutCustomBlendCurve, new GUIContent("自定义曲线"));
        //EditorGUILayout.PropertyField(followPerform, new GUIContent("跟随表现类型"));
        //EditorGUILayout.PropertyField(lookAtPerform, new GUIContent("注视表现类型"));
        //EditorGUILayout.PropertyField(FOVOffset, new GUIContent("FOV偏移"));
        //EditorGUILayout.PropertyField(screenRotateOffset, new GUIContent("屏幕空间旋转"));
        //EditorGUILayout.PropertyField(screenHorizontalOffset, new GUIContent("屏幕空间水平偏移"));
        //EditorGUILayout.PropertyField(screenVerticalOffset, new GUIContent("屏幕空间垂直偏移"));
        //EditorGUILayout.PropertyField(circleAngleOffset, new GUIContent("水平角度旋转"));
        //EditorGUILayout.PropertyField(pitchOffset, new GUIContent("俯仰偏移"));
        //EditorGUILayout.PropertyField(distanceOffset, new GUIContent("距离偏移"));
        //EditorGUILayout.PropertyField(cameraPositionOffset, new GUIContent("相机位置平移"));
        //EditorGUILayout.PropertyField(blendHint, new GUIContent("过渡路径类型"));

        //EditorGUILayout.PropertyField(useCamMove, new GUIContent("启用运镜预设"));
        //if (useCamMove.boolValue)
        //{
        //    EditorGUILayout.PropertyField(camMoveType, new GUIContent("运镜类型"));
        //    EditorGUILayout.PropertyField(camMoveBlendStyle, new GUIContent("运镜过渡曲线"));
        //    if(camMoveBlendStyle.enumValueIndex == (int)CinemachineBlendDefinition.Style.Custom)
        //        EditorGUILayout.PropertyField(camMoveCustromBlendCurve, new GUIContent("自定义运镜曲线"));
        //    EditorGUILayout.PropertyField(camMoveTime, new GUIContent("运镜时长"));
        //    EditorGUILayout.PropertyField(camMoveReverse, new GUIContent("运镜反序"));
        //    switch (camMoveType.enumValueIndex)
        //    {
        //        case (int)CamMovementType.ZoomIn:
        //            EditorGUILayout.PropertyField(camMoveZoomDistance, new GUIContent("运镜推拉距离"));
        //            break;
        //        case (int)CamMovementType.ZoomOut:
        //            EditorGUILayout.PropertyField(camMoveZoomDistance, new GUIContent("运镜推拉距离"));
        //            break;
        //        case (int)CamMovementType.Pan:
        //            EditorGUILayout.PropertyField(camMovePanHorizontal, new GUIContent("运镜水平摇镜偏移量"));
        //            EditorGUILayout.PropertyField(camMovePanVertical, new GUIContent("运镜垂直摇镜偏移量"));
        //            EditorGUILayout.PropertyField(camMovePanRoll, new GUIContent("运镜滚镜头偏移量"));
        //            break;
        //        case (int)CamMovementType.Track:
        //            EditorGUILayout.PropertyField(camMoveTrackHorizontal, new GUIContent("运镜水平移镜头"));
        //            EditorGUILayout.PropertyField(camMoveTrackVertical, new GUIContent("运镜垂直移镜头"));
        //            break;
        //        case (int)CamMovementType.Circle:
        //            EditorGUILayout.PropertyField(camMoveCircleAngle, new GUIContent("运镜环绕镜头"));
        //            break;
        //        case (int)CamMovementType.Hitchcock:
        //            EditorGUILayout.PropertyField(camHitchcock, new GUIContent("运镜希区柯克镜头"));
        //            break;
        //        default: break;
        //    }
        //}
        //EditorGUILayout.PropertyField(useCamShake, new GUIContent("启用镜头抖动预设"));
        //if (useCamShake.boolValue)
        //{
        //    //EditorGUILayout.PropertyField(camShakeType, new GUIContent("镜头抖动预设"));
        //    EditorGUILayout.PropertyField(camShakeAmpli, new GUIContent("镜头抖动强度"));
        //    EditorGUILayout.PropertyField(camShakeFreq, new GUIContent("镜头抖动频率"));
        //}

        obj.ApplyModifiedProperties();
    }
}
#endif
