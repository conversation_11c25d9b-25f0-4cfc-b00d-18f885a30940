using System.Collections;
using CEditor;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using ET;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;
using VGame;
using VGame.Framework;
using cfg.story;

namespace Slate.ActionClips
{
    [System.Serializable]
    public class CAnimBodyTransParams
    {
        [HorizontalGroup("anim", width: 70),ShowInInspector,HideLabel,PropertyOrder(-1)]
        private int tempId
        {
            get => animClipKey;
            set => animClipKey = value;
        }
        
        [SerializeField, HideLabel, ValueDropdown("AnimationConfig"), OnValueChanged("OnAnimationChange"),HorizontalGroup("anim")]
        public int animClipKey;

        [HorizontalGroup("anim",width:40), Button]
        private void Copy()
        {
            var x = CfgManager.tables.TbCEditorBodyAssets.GetOrDefault(animClipKey);
            if (x != null) GUIUtility.systemCopyBuffer =  $"{x.ID}|{x.Header}|{x.Detail} [{x.FileName}]";
        }
        
        private IEnumerable AnimationConfig()
        {
            return CfgManager.tables.TbCEditorBodyAssets.DataList.Where(x => x.IsReady).Select(x =>
                {
                    return new ValueDropdownItem($"{x.ID}|{x.Header}|{x.Detail} [{x.FileName}]", x.ID); 
                });
        }

        private AnimationClip _animationClip;
        public async ETTask<AnimationClip> GetClip()
        {
            if (_animationClip != null) return _animationClip;
            var cfg = CfgManager.tables.TbCEditorBodyAssets.GetOrDefault(animClipKey);
            if (cfg != null && cfg.IsReady && !string.IsNullOrEmpty(cfg.FileName))
            {
                _animationClip = await DialogueUtil.Load<AnimationClip>(cfg.FileName);
                return _animationClip;
            }

            return null;
        }

        public void OnAnimationChange()
        {
            _animationClip = null;
        }


        [SerializeField, LabelText("肢体动作播放逻辑")]
        public PlayingMode playingMode = PlayingMode.EndStop;
        
        [SerializeField, LabelText("肢体起始时间")]
        public float animStartTime = 0;
        
        [SerializeField, LabelText("肢体停止时间")]
        public float animEndTime = 99999;
        
        [SerializeField, LabelText("肢体过渡曲线")]
        public BlendType blendInType = BlendType.EaseInOut;
        
        [SerializeField, LabelText("肢体过渡时长")]
        public float blendInDuration = 1;
        
        [SerializeField, LabelText("肢体静态姿态混入类型")]
        public BlendType staticBlendInType = BlendType.QuickEaseInSlowEaseOut;
        
        [SerializeField, LabelText("肢体静态姿态混入时长")]
        public float staticBlendInDuration = 1;


        [SerializeField, LabelText("A类混入C类起始点")]
        public bool beginTransToCPerformance = false;

        [HideIf("@!beginTransToCPerformance", 1)]
        [SerializeField, LabelText("AC类过渡时长")]
        public float ACTransBlendInOutTime = 1f;

        [HideIf("@!beginTransToCPerformance", 1)]
        [SerializeField, LabelText("A类是Rootmotion资产")]
        public bool APerformanceAnimIsRootmotion = true;

        [HideIf("@!beginTransToCPerformance", 1)]
        [SerializeField, LabelText("C类是Rootmotion资产")]
        public bool CPerformanceAnimIsRootmotion = true;


        [SerializeField, LabelText("呼吸资产"), ValueDropdown("BreathAnimationConfig"), OnValueChanged("OnBreathAnimationChange")]
        public int breathAnimClipKey;
        private IEnumerable BreathAnimationConfig()
        {
            return CfgManager.tables.TbCEditorBreathAssets.DataList.Select(x =>
            {
                return new ValueDropdownItem($"{x.ID}-{x.Detail} [{x.FileName}]", x.ID);
            });
        }
        private AnimationClip _breathAnimationClip;
        public async ETTask<AnimationClip> GetBreathClip()
        {
            if (_breathAnimationClip != null) return _breathAnimationClip;
            var cfg = CfgManager.tables.TbCEditorBodyAssets.GetOrDefault(breathAnimClipKey);
            if (cfg != null && cfg.IsReady && !string.IsNullOrEmpty(cfg.FileName))
            {
                _breathAnimationClip = await DialogueUtil.Load<AnimationClip>(cfg.FileName);
                return _breathAnimationClip;
            }
            return null;
        }
        public void OnBreathAnimationChange()
        {
            _breathAnimationClip = null;
        }
        //[SerializeField, LabelText("呼吸资产")]
        //public AnimationClip breathClip;


        //[SerializeField, LabelText("呼吸遮罩"), ValueDropdown("BreathMaskConfig"), OnValueChanged("OnBreathMaskChange")]
        //public int breathMaskKey;
        //private IEnumerable BreathMaskConfig()
        //{
        //    return CfgManager.tables.TbCEditorBreathMaskAssets.DataList.Select(x =>
        //    {
        //        return new ValueDropdownItem($"{x.ID}-{x.Header}-{x.Detail} [{x.FileName}]", x.ID);
        //    });
        //}
        //private AvatarMask _breathMask;
        //public AvatarMask GetBreathMask()
        //{
        //    if (_breathMask != null) return _breathMask;
        //    var cfg = CfgManager.tables.TbCEditorBreathMaskAssets.GetOrDefault(breathMaskKey);
        //    if (cfg != null)
        //    {
        //        _breathMask = ResManager.LoadInDialogue<AvatarMask>(cfg.FileName);
        //        return _breathMask;
        //    }

        //    return null;
        //}
        //public void OnBreathMaskChange()
        //{
        //    _breathMask = null;
        //}
        [SerializeField, LabelText("呼吸遮盖")]
        public AvatarMask breathAvatarMask;

        [SerializeField, LabelText("呼吸强度")]
        public float breathStrength = 1;

        
        [SerializeField, LabelText("播放速率")]
        public float animPlayRate = 1;
        
        [SerializeField, LabelText("镜像")]
        public bool mirrorAnim = false;

        [SerializeField, LabelText("使用角色动画控制手持物")]
        public bool useBodyRelevantHandheldObject = false;

        //[SerializeField, LabelText("覆盖右手动画")]
        //public bool overWriteLocalRightHand = false;

        //[SerializeField, LabelText("覆盖左手动画")]
        //public bool overWriteLocalLeftHand = false;

        //[SerializeField, LabelText("右手动画资产")]
        //public AnimationClip overWriteLocalRightHandClip;

        //[SerializeField, LabelText("左手动画资产")]
        //public AnimationClip overWriteLocalLeftHandClip;


        [SerializeField, LabelText("右手位置防穿Tag")]
        public Vector3 rightHandPosition;
        
        [SerializeField, LabelText("左手位置防穿Tag")]
        public Vector3 leftHandPosition;
        
        [SerializeField, LabelText("右肘位置防穿Tag")]
        public Vector3 rightElbowPosition;
        
        [SerializeField, LabelText("左肘位置防穿Tag")]
        public Vector3 leftElbowPosition;
    }
    
    [Name("肢体动作配置")]
    [Attachable(typeof(CAnimBodyTransTrack))]
    [Category("C类编辑器")]
    public class CAnimBodyTransClip : BaseDialogueActorActionClip<CAnimBodyTransParams>
    {
        private GameObject performer;

        public int assetIndex { get; set; }

        public override string info
        {
            get
            {
                var asset = CfgManager.tables.TbCEditorBodyAssets.GetOrDefault(GetCfg().animClipKey);
                if (asset != null)
                {
                    return asset.Detail;
                }
                else
                {
                    return "肢体动作配置";
                }
            }
        }

        [SerializeField]
        [HideInInspector]
        private float _length = 0.1f;
        public override float length {
            get { return _length; }
            set { _length = value; }
        }

        public async ETTask<float> MatchAnimLength()
        {
            AnimationClip animClip = await GetCfg().GetClip();
            if (animClip != null)
            {
                length = animClip.length;
                return length;
            }

            return 0;
        }
        protected override void OnCreate()
        {
            base.OnCreate();

            var cfgList = CfgManager.tables.TbCEditorBreathAssets.DataList;
            if (cfgList != null && cfgList.Count > 0)
                GetCfg().breathAnimClipKey = cfgList[0].ID;

            var bodyList = CfgManager.tables.TbCEditorBodyAssets.DataList.Where(x => x.IsReady).ToList();
            if (bodyList != null && bodyList.Count > 0)
                GetCfg().animClipKey = bodyList[0].ID;
        }

        //Called in forward sampling when the clip is entered
        //protected override void OnEnter()
        protected override async ETTask OnEnterAsync()
        {
            performer = GetActor();
            performer?.Active(true);
            if (GetCfg().animClipKey < 0)
                return;
            AnimTransMaster master = performer.GetComponent<AnimTransMaster>();

            if (master != null && performer != null)
            {
                master.currentAnimClip = await GetCfg().GetClip();
                master.playingMode = GetCfg().playingMode;
                master.animStartTime = GetCfg().animStartTime;
                master.animEndTime = GetCfg().animEndTime;
                master.blendInType = GetCfg().blendInType;
                if (master.useAutoTransTime == false)
                    master.blendInDuration = GetCfg().blendInDuration;
                master.staticBlendInType = GetCfg().staticBlendInType;
                master.staticBlendInDuration = GetCfg().staticBlendInDuration;
                master.breathClip = await GetCfg().GetBreathClip();
                master.breathAvatarMask = null;
                //master.breathAvatarMask = GetCfg().GetBreathMask();
                master.breathStrength = GetCfg().breathStrength;
                master.animPlayRate = GetCfg().animPlayRate;
                master.mirrorAnim = GetCfg().mirrorAnim;
                //master.overWriteLocalRightHand = GetCfg().overWriteLocalRightHand;
                //master.overWriteLocalLeftHand = GetCfg().overWriteLocalLeftHand;
                //master.overWriteLocalRightHandClip = GetCfg().overWriteLocalRightHandClip;
                //master.overWriteLocalLeftHandClip = GetCfg().overWriteLocalLeftHandClip;

                if (GetCfg().beginTransToCPerformance)
                {
                    master.ACTransBlendInOutTime = GetCfg().ACTransBlendInOutTime;
                    master.TransToCPerformance(GetCfg().APerformanceAnimIsRootmotion, GetCfg().CPerformanceAnimIsRootmotion);
                }

                master.SetNewArmPosition(GetCfg().rightHandPosition, GetCfg().leftHandPosition, GetCfg().rightElbowPosition, GetCfg().leftElbowPosition);
                master.BodyTransAnim(!GetCfg().beginTransToCPerformance);

                CheckAutoHandheldObjectLoad(GetCfg().animClipKey, GetCfg().useBodyRelevantHandheldObject);
            }
        }

        private async void CheckAutoHandheldObjectLoad(int animKey, bool canLoad)
        {
            HandheldManage handheldManager = performer.GetComponent<HandheldManage>();
            if (handheldManager == null)
                return;

            CEditorBodyHandheldMapping holdMapping = CfgManager.tables.TbCEditorBodyHandheldMapping.GetOrDefault(animKey);
            CEditorHandheldTable holdObject = null;
            GameObject holdGameObjectPrefab = null;
            if (holdMapping != null)
                holdObject = CfgManager.tables.TbCEditorHandheldTable.GetOrDefault(holdMapping.HandheldID);
            if (holdObject != null)
                holdGameObjectPrefab = await DialogueUtil.LoadPrefab(holdObject.GameObjectName);

            // bodyID无手持物，同时手持物轨道无手持物，卸载bodyID手持
            if (holdMapping == null || holdObject == null || (canLoad && holdGameObjectPrefab == null))
            {
                handheldManager.rightHandHoldingMaintainTrigger = false;
                handheldManager.leftHandHoldingMaintainTrigger = false;
                handheldManager.ForceUpdateRightHnadheldState();
                handheldManager.ForceUpdateLeftHnadheldState();
                return;
            }

            // bodyID有手持物，可以
            if (canLoad)
            {
                HandHoldState handHoldState = new HandHoldState();
                handHoldState.overwriteHandAnim = false;
                handHoldState.overwriteAnim = null;
                handHoldState.isHoldingObject = true;
                handHoldState.holdObject = holdGameObjectPrefab;
                if (string.IsNullOrEmpty(holdObject.GameObjectAnimName) || holdObject.GameObjectAnimName.ToLower() == "null")
                {
                    handHoldState.useHoldObjectAnim = false;
                    handHoldState.holdObjectAnimation = null;
                }
                else
                {
                    var holdObjectAnim = await DialogueUtil.Load<AnimationClip>(holdObject.GameObjectAnimName);
                    if (holdObjectAnim == null)
                    {
                        handHoldState.useHoldObjectAnim = false;
                        handHoldState.holdObjectAnimation = null;
                    }
                    else
                    {
                        handHoldState.useHoldObjectAnim = true;
                        handHoldState.holdObjectAnimation = holdObjectAnim;
                    }
                }
                handHoldState.refBoneName = holdObject.HangingBoneName;
                handHoldState.positionOffset = new Vector3(holdObject.PositionOffsetX, holdObject.PositionOffsetY, holdObject.PositionOffsetZ);
                handHoldState.rotateOffset = new Vector3(holdObject.RotateOffsetX, holdObject.RotateOffsetY, holdObject.RotateOffsetZ);
                handHoldState.localscale = new Vector3(holdObject.ScaleX, holdObject.ScaleY, holdObject.ScaleZ);

                if (holdObject.IsRightHand && handheldManager.rightHandHoldingTrigger == false)
                {
                    handheldManager.ClearRightHandHoldState();
                    handheldManager.rightHandheldState = handHoldState;
                    handheldManager.useRightHandheldUpdateControl = true;
                    handheldManager.rightHandHoldingMaintainTrigger = true;
                    handheldManager.ForceUpdateRightHnadheldState();
                }
                if (!holdObject.IsRightHand && handheldManager.leftHandHoldingTrigger == false)
                {
                    handheldManager.ClearLeftHandHoldState();
                    handheldManager.leftHandheldState = handHoldState;
                    handheldManager.useLeftHandheldUpdateControl = true;
                    handheldManager.leftHandHoldingMaintainTrigger = true;
                    handheldManager.ForceUpdateLeftHnadheldState();
                }
                return;
            }
            else
            {
//                 if (holdGameObjectPrefab != null)
//                 {
// #if UNITY_EDITOR
//                     DestroyImmediate(holdGameObjectPrefab);
// #else
//                     Destroy(holdGameObjectPrefab);
// #endif
//                 }
            }
        }

        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime) { }

        //Called in forwards sampling when the clip exits
        protected override void OnExit() { }

        //Called in backwards sampling when the clip is entered.
        protected override void OnReverseEnter() { }

        //Called in backwards sampling when the clip exits.
        protected override void OnReverse() { }
    }
}