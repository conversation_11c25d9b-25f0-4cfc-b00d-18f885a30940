using UnityEngine;

namespace Slate
{
    [Name("C类编辑器_左手手持物轨道")]
    [Attachable(typeof(ActorGroup))]
    [Category("C类编辑器")]

    public class CLeftHandheldTrack : ActionTrack
    {
        protected override void OnCreate()
        {
            base.OnCreate();
            this.name = "C类编辑器_左手手持物轨道";
        }

        protected override void OnSceneGUI()
        {
            base.OnSceneGUI();
            this.name = "C类编辑器_左手手持物轨道";
        }

        protected override void OnEnter()
        {
            base.OnEnter();
        }
#if UNITY_EDITOR
        public override Texture icon
        {
            get
            {
                _icon = Resources.Load("PlayIcon") as Texture;
                return _icon as Texture;
            }
        }
#endif
        public override string info
        {
            get
            {
                return "left handheld track info";
            }
        }
    }
}