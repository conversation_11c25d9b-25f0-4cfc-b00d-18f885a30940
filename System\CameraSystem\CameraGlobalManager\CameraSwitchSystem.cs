using Cinemachine;
using Sirenix.OdinInspector;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using VGame.Framework;
using Cysharp.Text;

namespace CEditor
{
    [Serializable]
    [ExecuteAlways]
    public class BlendListItemRefPerform
    {
        public CinemachineVirtualCamera cam;
        public CinemachineBlendDefinition.Style blendStyle = CinemachineBlendDefinition.Style.EaseInOut;
        public AnimationCurve customBlendCurve;
        public float blendTime;
        public float houldOnTime; 

        public HangingPerformType followPerform;
        public HangingPerformType lookAtPerform;
        public float FOVOffset;
        public float screenRotateOffset;
        public float screenHorizontalOffset;
        public float screenVerticalOffset;
        public float circleAngleOffset;
        public float pitchOffset;
        public float distanceOffset;
        public Vector3 cameraPositionOffset;
        public CinemachineVirtualCamera.BlendHint blendHint = CinemachineVirtualCameraBase.BlendHint.None;

        public NoiseSettings camShakeType;
        public float camShakeAmpli;
        public float camShakeFreq;

        public bool customLookAtTarget;
        public float customLookATargetDistance;
    }

    [Serializable]
    [ExecuteAlways]
    public class BlendListItemIDPerform
    {
        private string _targetTag { get; set; }
        private int _templateID { get; set; }
        [SerializeField, LabelText("镜头ID"), ValueDropdown("CamIDConfig")]
        public int camID;
        public CinemachineBlendDefinition.Style blendStyle = CinemachineBlendDefinition.Style.EaseInOut;
        public AnimationCurve customBlendCurve;
        public float blendTime;
        public float houldOnTime;

        public HangingPerformType followPerform;
        public HangingPerformType lookAtPerform;
        public float FOVOffset;
        public float screenRotateOffset;
        public float screenHorizontalOffset;
        public float screenVerticalOffset;
        public float circleAngleOffset;
        public float pitchOffset;
        public float distanceOffset;
        public Vector3 cameraPositionOffset;
        public CinemachineVirtualCamera.BlendHint blendHint = CinemachineVirtualCameraBase.BlendHint.None;

        public NoiseSettings camShakeType;
        public float camShakeAmpli;
        public float camShakeFreq;

        public bool customLookAtTarget;
        public float customLookATargetDistance;


        private IEnumerable CamIDConfig()
        {
            List<cfg.story.CEditorCameraAsset> subList;
            if (StationTemplateManager.instance != null && StationTemplateManager.instance.targetSlotCameraGroups != null && StationTemplateManager.instance.targetSlotCameraGroups.Count > 0)
            {
                CameraGroupOfTargetSlotInfo info = StationTemplateManager.instance.targetSlotCameraGroups.Find(x => x.targetSlotGroupTag == GetTargetTag());
                if (info != null)
                {
                    int targetStationTemplateID = info.templateID;
                    subList = CfgManager.tables.TbCEditorCameraAsset.DataList.Where(x => StationTemplateToolsKit.CameraIsUnderStationTemplate(targetStationTemplateID, x.ID)).ToList();
                }
                else
                {
                    int tempID = _templateID;
                    if (tempID == 0)
                        tempID = 10000;
                    CameraGroupOfTargetSlotInfo targetTagInfo = StationTemplateManager.GetTargetSlotsListV3(tempID).Find(x => x.targetSlotGroupTag == GetTargetTag());
                    int derTempID = targetTagInfo == null ? 0 : targetTagInfo.templateID;
                    subList = CfgManager.tables.TbCEditorCameraAsset.DataList.Where(x => StationTemplateToolsKit.CameraIsUnderStationTemplate(derTempID, x.ID)).ToList();
                }
            }
            else
            {
                int tempID = _templateID;
                if (tempID == 0)
                    tempID = 10000;
                CameraGroupOfTargetSlotInfo targetTagInfo = StationTemplateManager.GetTargetSlotsListV3(tempID).Find(x => x.targetSlotGroupTag == GetTargetTag());
                int derTempID = targetTagInfo == null ? 0 : targetTagInfo.templateID;
                subList = CfgManager.tables.TbCEditorCameraAsset.DataList.Where(x => StationTemplateToolsKit.CameraIsUnderStationTemplate(derTempID, x.ID)).ToList();
            }
            return subList.Select(x =>
            {
                return new ValueDropdownItem($"{x.ID}-{x.Detail}", x.ID);
            });
        }
        private string GetTargetTag()
        {
            if (_targetTag != null) return _targetTag;
            return null;
        }

        public void SetTargetTag(string targetTag)
        {
            this._targetTag = targetTag;
        }

        public void SetTemplateID(int templateID)
        {
            _templateID = templateID;
        }
    }

    public enum CamMovementType
    {
        ZoomIn,
        ZoomOut,
        Pan,
        Track,
        Circle,
        Hitchcock
    }

    [ExecuteAlways]
    public class CameraGroupRefTransParam
    {
        public List<CameraRefTransParam> camGroup;
        public CameraSwitchPerformConfig camGroupTransInPerform;
    }
    [ExecuteAlways]
    public class CameraGroupIDTransParam
    {
        public List<CameraIDTransParam> camGroup;
        public CameraSwitchPerformConfig camGroupTransInPerform;
    }

    [ExecuteAlways]
    public class CameraRefTransParam
    {
        public CinemachineVirtualCamera cam;
        public CameraPerformConfig cameraPerformConfig;
    }
    [ExecuteAlways]
    public class CameraIDTransParam
    {
        public int camID;
        public CameraPerformConfig cameraPerformConfig;
    }

    [ExecuteAlways]
    public class CameraPerformConfig
    {
        public CameraControllerData cameraControllerData;
        public HangingPerformType followPerform;
        public HangingPerformType lookAtPerform;

        public Vector3 followHangingOffset;
        public Vector3 lookAtHangingOffset;

        public CameraSwitchPerformConfig cameraSwitchPerform;

        public float blendListHoldOnTime;

        public bool checkSimilar;
    }

    [ExecuteAlways]
    public class CameraSwitchPerformConfig
    {
        public CinemachineBlendDefinition.Style blendStyle;
        public AnimationCurve custromBlendCurve;
        public float blendTime;
    }

    [ExecuteAlways]
    public class CameraSwitchSystem : MonoBehaviour
    {
        public StationTemplateManager stationTemplateManager;

        public float similarCamPositionDistanceMax = 0.05f;
        public float similarCamRotationDistanceMax = 0.3f;

        private bool delayChangeSingleCameraWithAbsCamBefore = false;
        private CameraRefTransParam singleCameraWithAbsCamBeforeCamParam;
        private string singleCameraWithAbsCamBeforeTargetTag;
        private Vector3 singleCameraWithAbsCamBeforeAnimCamPosition;
        private Vector3 singleCameraWithAbsCamBeforeAnimCamRotate;
        private float singleCameraWithAbsCamBeforeAnimCamFOV;

        // Start is called before the first frame update
        void Start()
        {
            stationTemplateManager = StationTemplateManager.instance;
        }

        // Update is called once per frame
        void Update()
        {
            CleanCloneCameras();
        }

        private void LateUpdate()
        {
            if (delayChangeSingleCameraWithAbsCamBefore)
            {
                delayChangeSingleCameraWithAbsCamBefore = false;
                ChangeSingleCameraWithAbsCamBefore(singleCameraWithAbsCamBeforeCamParam, singleCameraWithAbsCamBeforeTargetTag, singleCameraWithAbsCamBeforeAnimCamPosition, singleCameraWithAbsCamBeforeAnimCamRotate, singleCameraWithAbsCamBeforeAnimCamFOV);
            }
        }

        public void ChangeSingleCamera(CameraRefTransParam camParam, string targetTag, bool checkSimilar)
        {
            if (CheckStationTemplateState(stationTemplateManager) == false)
                return;
            CinemachineBrain brain = stationTemplateManager.GetCinemachineBrain();
            if (brain != null)
            {
                if (camParam.cam != null)
                {
                    GameObject oriCam = camParam.cam.gameObject;
                    if (oriCam)
                    {
                        CameraGroupOfTargetSlotInfo subgroupInfo = stationTemplateManager.targetSlotCameraGroups.Find(x => x.targetSlotGroupTag == targetTag);
                        if (subgroupInfo != null)
                        {
                            GameObject cloneCamGroup = stationTemplateManager.getCloneCameraGroup();
                            if (cloneCamGroup != null)
                            {
                                //stationTemplateManager.AlignDerCamAndSlot(oriCam.name, targetTag);
                                CloneCameraState cloneCameraState = CloneCameraGenerator(oriCam, camParam.cameraPerformConfig, subgroupInfo, null, cloneCamGroup.transform);
                                if (cloneCameraState == null)
                                    return;

                                cloneCameraState.targetTag = targetTag;
                                if (checkSimilar && stationTemplateManager.cloneCameraBuffer != null && stationTemplateManager.cloneCameraBuffer.Count > 0)
                                {
                                    Debug.Log("Enter Sim");
                                    CinemachineVirtualCamera currentVC = null;
                                    GameObject currentCam = null;
                                    CloneCameraState currentCloneCamState = null;
                                    if (brain.ActiveVirtualCamera != null)
                                    {
                                        currentVC = CCameraTools.GetLivedVirturalCamera(brain);
                                        if (currentVC != null)
                                        {
                                            currentCam = currentVC.gameObject;
                                            if (currentCam != null)
                                            {
                                                currentCloneCamState = stationTemplateManager.cloneCameraBuffer.Find(x => ReferenceEquals(x.cloneCamera, currentCam));
                                                if (currentCloneCamState != null)
                                                {
                                                    if (CheckCameraIsSimilar(cloneCameraState, currentCloneCamState))
                                                    {
                                                        Debug.Log("sim");
                                                        cloneCameraState.justBorn = false;
                                                        cloneCameraState.readyToDestory = true;
                                                        cloneCameraState.parentCamera = null;
                                                        stationTemplateManager.cloneCameraBuffer.Add(cloneCameraState);
                                                        return;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                stationTemplateManager.cloneCameraBuffer.Add(cloneCameraState);

                                brain.m_DefaultBlend.m_Style = camParam.cameraPerformConfig.cameraSwitchPerform.blendStyle;
                                if (camParam.cameraPerformConfig.cameraSwitchPerform.blendStyle == CinemachineBlendDefinition.Style.Custom)
                                    brain.m_DefaultBlend.m_CustomCurve = camParam.cameraPerformConfig.cameraSwitchPerform.custromBlendCurve;
                                brain.m_DefaultBlend.m_Time = camParam.cameraPerformConfig.cameraSwitchPerform.blendTime;
                                ResetCloneCameraPriority();
                                cloneCameraState.cloneCamera.GetComponent<CinemachineVirtualCamera>().Priority = 100;
                                brain.ManualUpdate();
                            }
                        }
                    }
                }
            }
        }

        public void ChangeSingleCameraWithAbsCamBefore(CameraRefTransParam camParam, string targetTag, Vector3 animCamPosition, Vector3 animCamRotate, float animCamFOV)
        {
            if (CheckStationTemplateState(stationTemplateManager) == false)
                return;
            CinemachineBrain brain = stationTemplateManager.GetCinemachineBrain();
            if (brain != null)
            {
                if (camParam.cam != null)
                {
                    GameObject oriCam = camParam.cam.gameObject;
                    if (oriCam)
                    {
                        CameraGroupOfTargetSlotInfo subgroupInfo = stationTemplateManager.targetSlotCameraGroups.Find(x => x.targetSlotGroupTag == targetTag);
                        if (subgroupInfo != null)
                        {
                            GameObject cloneCamGroup = stationTemplateManager.getCloneCameraGroup();
                            if (cloneCamGroup != null)
                            {
                                // 定义BlendList
                                GameObject blendListCamera = new GameObject(CCameraTools.GetUniqueGameObjectName("BlendListCamera", cloneCamGroup.transform));
                                blendListCamera.transform.parent = cloneCamGroup.transform;
                                blendListCamera.transform.localPosition = Vector3.zero;
                                blendListCamera.transform.localRotation = Quaternion.identity;
                                blendListCamera.transform.localScale = Vector3.one;
                                CinemachineBlendListCamera cineBlendListCam = blendListCamera.AddComponent<CinemachineBlendListCamera>();
                                cineBlendListCam.m_Instructions = new CinemachineBlendListCamera.Instruction[2];

                                // 定义C类程序化相机
                                CloneCameraState cloneCameraState = CloneCameraGenerator(oriCam, camParam.cameraPerformConfig, subgroupInfo, blendListCamera, cloneCamGroup.transform);
                                if (cloneCameraState == null)
                                {
#if UNITY_EDITOR
                                    DestroyImmediate(blendListCamera);
#else
                                    Destroy(blendListCamera);
#endif
                                    singleCameraWithAbsCamBeforeCamParam = camParam;
                                    singleCameraWithAbsCamBeforeTargetTag = targetTag;
                                    singleCameraWithAbsCamBeforeAnimCamPosition = animCamPosition;
                                    singleCameraWithAbsCamBeforeAnimCamRotate = animCamRotate;
                                    singleCameraWithAbsCamBeforeAnimCamFOV = animCamFOV;
                                    delayChangeSingleCameraWithAbsCamBefore = true;
                                    return;
                                }

                                cloneCameraState.targetTag = targetTag;
                                // 定义模拟动画相机尾帧的固定位置相机
                                CloneCameraState absTransformState = AbsTransformCameraGenerate(animCamPosition, animCamRotate, animCamFOV, blendListCamera, cloneCamGroup.transform);

                                // 组装BlendList
                                CinemachineBlendDefinition absCamBlendDefinition = new CinemachineBlendDefinition()
                                {
                                    m_Style = CinemachineBlendDefinition.Style.Cut,
                                    m_CustomCurve = null,
                                    m_Time = 0
                                };
                                absTransformState.cloneCamera.transform.parent = blendListCamera.transform;
                                cineBlendListCam.m_Instructions[0].m_VirtualCamera = absTransformState.cloneCamera.GetComponent<CinemachineVirtualCamera>();
                                cineBlendListCam.m_Instructions[0].m_Blend = absCamBlendDefinition;
                                cineBlendListCam.m_Instructions[0].m_Hold = 0;

                                CinemachineBlendDefinition cloneCamBlendDefinition = new CinemachineBlendDefinition()
                                {
                                    m_Style = camParam.cameraPerformConfig.cameraSwitchPerform.blendStyle,
                                    m_CustomCurve = camParam.cameraPerformConfig.cameraSwitchPerform.custromBlendCurve,
                                    m_Time = camParam.cameraPerformConfig.cameraSwitchPerform.blendTime
                                };
                                cloneCameraState.cloneCamera.transform.parent = blendListCamera.transform;
                                cineBlendListCam.m_Instructions[1].m_VirtualCamera = cloneCameraState.cloneCamera.GetComponent<CinemachineVirtualCamera>();
                                cineBlendListCam.m_Instructions[1].m_Blend = cloneCamBlendDefinition;
                                cineBlendListCam.m_Instructions[1].m_Hold = 0;

                                CloneCameraState blendListState = new CloneCameraState();
                                blendListState.cloneCamera = blendListCamera;
                                blendListState.relevantObject = null;
                                blendListState.hangingGarbage = null;
                                blendListState.parentCamera = null;
                                blendListState.targetTag = null;
                                blendListState.cameraPerformConfig = null;
                                blendListState.hasChild = true;
                                blendListState.justBorn = true;
                                blendListState.readyToDestory = false;
                                blendListState.isAbsTransformCam = false;

                                stationTemplateManager.cloneCameraBuffer.Add(absTransformState);
                                stationTemplateManager.cloneCameraBuffer.Add(cloneCameraState);
                                stationTemplateManager.cloneCameraBuffer.Add(blendListState);

                                // 配置Cinemachine Brain
                                brain.m_DefaultBlend.m_Style = CinemachineBlendDefinition.Style.Cut;
                                brain.m_DefaultBlend.m_CustomCurve = null;
                                brain.m_DefaultBlend.m_Time = 0f;

                                ResetCloneCameraPriority();
                                blendListCamera.GetComponent<CinemachineBlendListCamera>().Priority = 100;
                                brain.ManualUpdate();
                            }
                        }
                    }
                }
            }
        }


        public void ChangeAbsCamera(CameraSwitchPerformConfig cameraSwitchPerform, Vector3 animCamPosition, Vector3 animCamRotate, float animCamFOV)
        {
            if (CheckStationTemplateState(stationTemplateManager) == false)
                return;
            CinemachineBrain brain = stationTemplateManager.GetCinemachineBrain();
            if (brain != null)
            {
                GameObject cloneCamGroup = stationTemplateManager.getCloneCameraGroup();
                if (cloneCamGroup != null)
                {
                    // 定义模拟动画相机尾帧的固定位置相机
                    CloneCameraState absTransformState = AbsTransformCameraGenerate(animCamPosition, animCamRotate, animCamFOV, null, cloneCamGroup.transform);
                    if (absTransformState == null)
                        return;

                    stationTemplateManager.cloneCameraBuffer.Add(absTransformState);

                    // 配置Cinemachine Brain
                    brain.m_DefaultBlend.m_Style = cameraSwitchPerform.blendStyle;
                    if (cameraSwitchPerform.blendStyle == CinemachineBlendDefinition.Style.Custom)
                        brain.m_DefaultBlend.m_CustomCurve = cameraSwitchPerform.custromBlendCurve;
                    brain.m_DefaultBlend.m_Time = cameraSwitchPerform.blendTime;

                    ResetCloneCameraPriority();
                    absTransformState.cloneCamera.GetComponent<CinemachineVirtualCamera>().Priority = 100;
                    brain.ManualUpdate();
                }
            }
        }

        public void ChangeAnimatedCamera(CameraSwitchPerformConfig cameraSwitchPerform, AnimationClip camAnim)
        {
            if (CheckStationTemplateState(stationTemplateManager) == false)
                return;
            CinemachineBrain brain = stationTemplateManager.GetCinemachineBrain();
            if (brain != null)
            {
                GameObject cloneCamGroup = stationTemplateManager.getCloneCameraGroup();
                if (cloneCamGroup == null)
                    return;

                // 生成动画相机
                CloneCameraState animatedCameraState = AnimatedCameraGenerate(camAnim, cloneCamGroup.transform);
                if (animatedCameraState == null)
                    return;

                stationTemplateManager.cloneCameraBuffer.Add(animatedCameraState);

                // 配置Cinemachine Brain
                brain.m_DefaultBlend.m_Style = cameraSwitchPerform.blendStyle;
                if (cameraSwitchPerform.blendStyle == CinemachineBlendDefinition.Style.Custom)
                    brain.m_DefaultBlend.m_CustomCurve = cameraSwitchPerform.custromBlendCurve;
                brain.m_DefaultBlend.m_Time = cameraSwitchPerform.blendTime;

                ResetCloneCameraPriority();
                animatedCameraState.cloneCamera.GetComponent<CinemachineVirtualCamera>().Priority = 100;
                brain.ManualUpdate();
            }
        }

        public void ChangeBlendListCamera(CameraGroupRefTransParam camGroupParam, string targetTag, bool checkSimilar)
        {
            if (CheckStationTemplateState(stationTemplateManager) == false)
                return;
            CinemachineBrain brain = stationTemplateManager.GetCinemachineBrain();
            if (brain != null)
            {
                if (camGroupParam.camGroup != null && camGroupParam.camGroup.Count > 0)
                {
                    GameObject cloneCamGroup = stationTemplateManager.getCloneCameraGroup();
                    if (cloneCamGroup != null && stationTemplateManager.targetSlotCameraGroups != null)
                    {
                        //for (int i = 0; i < camGroupParam.camGroup.Count; i++)
                        //{
                        //    GameObject oriCam = camGroupParam.camGroup[i].cam.gameObject;
                        //    if (oriCam)
                        //    {
                        //        stationTemplateManager.AlignDerCamAndSlot(oriCam.name, targetTag);
                        //        break;
                        //    }
                        //}
                        GameObject blendListCamera = new GameObject(CCameraTools.GetUniqueGameObjectName("BlendListCamera", cloneCamGroup.transform));
                        blendListCamera.transform.parent = cloneCamGroup.transform;
                        blendListCamera.transform.localPosition = Vector3.zero;
                        blendListCamera.transform.localRotation = Quaternion.identity;
                        blendListCamera.transform.localScale = Vector3.one;
                        CinemachineBlendListCamera cineBlendListCam = blendListCamera.AddComponent<CinemachineBlendListCamera>();
                        cineBlendListCam.m_Instructions = new CinemachineBlendListCamera.Instruction[camGroupParam.camGroup.Count];

                        List<CloneCameraState> childCamState = new List<CloneCameraState>();
                        CloneCameraState currentCloneCamState = null;
                        bool isSim = false;

                        for (int i = 0; i < camGroupParam.camGroup.Count; i++)
                        {
                            GameObject oriCam = camGroupParam.camGroup[i].cam.gameObject;
                            if (oriCam)
                            {
                                CameraGroupOfTargetSlotInfo subgroupInfo = stationTemplateManager.targetSlotCameraGroups.Find(x => x.targetSlotGroupTag == targetTag);
                                if (subgroupInfo != null)
                                {
                                    CinemachineBlendDefinition blendDefinition = new CinemachineBlendDefinition()
                                    {
                                        m_CustomCurve = camGroupParam.camGroup[i].cameraPerformConfig.cameraSwitchPerform.custromBlendCurve,
                                        m_Style = camGroupParam.camGroup[i].cameraPerformConfig.cameraSwitchPerform.blendStyle,
                                        m_Time = camGroupParam.camGroup[i].cameraPerformConfig.cameraSwitchPerform.blendTime
                                    };
                                    CloneCameraState cloneCameraState = CloneCameraGenerator(oriCam, camGroupParam.camGroup[i].cameraPerformConfig, subgroupInfo, blendListCamera, cloneCamGroup.transform);
                                    if (cloneCameraState == null)
                                    {
#if UNITY_EDITOR
                                        DestroyImmediate(blendListCamera);
#else
                                        Destroy(blendListCamera);
#endif
                                        return;
                                    }
                                    cloneCameraState.targetTag = targetTag;
                                    if (i == 0 && checkSimilar && stationTemplateManager.cloneCameraBuffer != null && stationTemplateManager.cloneCameraBuffer.Count > 0)
                                    {
                                        Debug.Log("Enter Sim");
                                        CinemachineVirtualCamera currentVC = null;
                                        GameObject currentCam = null;
                                        
                                        if (brain.ActiveVirtualCamera != null)
                                        {
                                            currentVC = CCameraTools.GetLivedVirturalCamera(brain);
                                            if (currentVC != null)
                                            {
                                                currentCam = currentVC.gameObject;
                                                if (currentCam != null)
                                                {
                                                    currentCloneCamState = stationTemplateManager.cloneCameraBuffer.Find(x => ReferenceEquals(x.cloneCamera, currentCam));
                                                    if (currentCloneCamState != null)
                                                    {
                                                        if (CheckCameraIsSimilar(cloneCameraState, currentCloneCamState))
                                                        {
                                                            Debug.Log("sim");
                                                            if (stationTemplateManager.historysHolder != null && stationTemplateManager.historysHolder.Count > 0)
                                                            {
                                                                Transform currentHanging = stationTemplateManager.getHangingGroup().transform;
                                                                Transform currentTargetGroup = stationTemplateManager.getTargetGroup().transform;

                                                                foreach (GameObject history in stationTemplateManager.historysHolder)
                                                                {
                                                                    if (history != null && StationTemplateToolsKit.HasForefathers(currentCloneCamState.cloneCamera.transform, history.transform))
                                                                    {
                                                                        Transform historyHanging = history.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.HangingGroup]);
                                                                        Transform historyTargetGroup = history.transform.Find(StationTemplateDataCollector.namingConvention[NamingConventionTypes.TargetGroup]);

                                                                        List<Transform> tempHangings = new List<Transform>();
                                                                        List<Transform> tempTargetGroup = new List<Transform>();

                                                                        if (currentHanging != null && historyHanging != null)
                                                                            for (int h = 0; h < historyHanging.childCount; h++)
                                                                                if (historyHanging.GetChild(h) != null)
                                                                                    tempHangings.Add(historyHanging.GetChild(h));
                                                                        if (currentTargetGroup != null && historyTargetGroup != null)
                                                                            for (int h = 0; h < historyTargetGroup.childCount; h++)
                                                                                if (historyTargetGroup.GetChild(h) != null)
                                                                                    tempTargetGroup.Add(historyTargetGroup.GetChild(h));
                                                                        foreach (Transform t in tempHangings)
                                                                            t.parent = currentHanging;
                                                                        foreach (Transform t in tempTargetGroup)
                                                                            t.parent = currentTargetGroup;
                                                                    }
                                                                }
                                                            }

                                                            cloneCameraState.justBorn = false;
                                                            cloneCameraState.readyToDestory = true;
                                                            cloneCameraState.parentCamera = null;

                                                            childCamState.Add(cloneCameraState);

                                                            isSim = true;
                                                            cineBlendListCam.m_Instructions[i].m_VirtualCamera = currentCloneCamState.cloneCamera.GetComponent<CinemachineVirtualCamera>();
                                                            cineBlendListCam.m_Instructions[i].m_Blend = blendDefinition;
                                                            cineBlendListCam.m_Instructions[i].m_Hold = camGroupParam.camGroup[i].cameraPerformConfig.blendListHoldOnTime;
                                                            continue;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    cloneCameraState.cloneCamera.transform.parent = blendListCamera.transform;
                                    cineBlendListCam.m_Instructions[i].m_VirtualCamera = cloneCameraState.cloneCamera.GetComponent<CinemachineVirtualCamera>();
                                    cineBlendListCam.m_Instructions[i].m_Blend = blendDefinition;
                                    cineBlendListCam.m_Instructions[i].m_Hold = camGroupParam.camGroup[i].cameraPerformConfig.blendListHoldOnTime;

                                    childCamState.Add(cloneCameraState);
                                }
                            }
                        }

                        if (isSim)
                        {
                            currentCloneCamState.cloneCamera.transform.parent = blendListCamera.transform;
                            currentCloneCamState.parentCamera = blendListCamera;
                            currentCloneCamState.justBorn = true;
                            currentCloneCamState.hasChild = false;
                            currentCloneCamState.readyToDestory = false;
                        }
                        foreach (var state in childCamState)
                            stationTemplateManager.cloneCameraBuffer.Add(state);

                        CloneCameraState blendListState = new CloneCameraState();
                        blendListState.cloneCamera = blendListCamera;
                        blendListState.relevantObject = null;
                        blendListState.hangingGarbage = null;
                        blendListState.parentCamera = null;
                        blendListState.targetTag = null;
                        blendListState.cameraPerformConfig = null;
                        blendListState.hasChild = true;
                        blendListState.justBorn = true;
                        blendListState.readyToDestory = false;
                        blendListState.isAbsTransformCam = false;
                        stationTemplateManager.cloneCameraBuffer.Add(blendListState);

                        brain.m_DefaultBlend.m_Style = camGroupParam.camGroupTransInPerform.blendStyle;
                        if (camGroupParam.camGroupTransInPerform.blendStyle == CinemachineBlendDefinition.Style.Custom)
                            brain.m_DefaultBlend.m_CustomCurve = camGroupParam.camGroupTransInPerform.custromBlendCurve;
                        brain.m_DefaultBlend.m_Time = camGroupParam.camGroupTransInPerform.blendTime;

                        ResetCloneCameraPriority();
                        blendListCamera.GetComponent<CinemachineBlendListCamera>().Priority = 100;
                        brain.ManualUpdate();
                    }
                }
            }
        }

        private CloneCameraState CloneCameraGenerator(GameObject oriCam, CameraPerformConfig cameraPerformConfig, CameraGroupOfTargetSlotInfo cameraGroupInfo, GameObject parentCamera, Transform cloneGameraGroupTransform)
        {
            if (oriCam == null || cameraPerformConfig == null || cloneGameraGroupTransform == null || oriCam.GetComponent<CinemachineVirtualCamera>() == null) 
                return null;

            GameObject cloneCamera;
            if (cloneGameraGroupTransform != null)
                cloneCamera = GameObject.Instantiate(oriCam, cloneGameraGroupTransform);
            else
                cloneCamera = GameObject.Instantiate(oriCam);
            if (parentCamera != null)
                cloneCamera.name = CCameraTools.GetUniqueGameObjectName(CCameraTools.TransToCloneCameraName(oriCam.name), parentCamera.transform);
            else
                cloneCamera.name = CCameraTools.GetUniqueGameObjectName(CCameraTools.TransToCloneCameraName(oriCam.name), cloneGameraGroupTransform);

            CameraController cameraController = cloneCamera.GetComponent<CameraController>();
            CinemachineVirtualCamera VC = cloneCamera.GetComponent<CinemachineVirtualCamera>();

            cloneCamera.SetActive(true);
            
            if (cameraController != null && VC != null)
            {
                VC.m_StandbyUpdate = CinemachineVirtualCameraBase.StandbyUpdateMode.Always;

                cameraController.FOVOffset = cameraPerformConfig.cameraControllerData.FOVOffset;
                cameraController.screenRotateOffset = cameraPerformConfig.cameraControllerData.screenRotateOffset;
                cameraController.screenHorizontalOffset = cameraPerformConfig.cameraControllerData.screenHorizontalOffset;
                cameraController.screenVerticalOffset = cameraPerformConfig.cameraControllerData.screenVerticalOffset;
                cameraController.circleAngleOffset = cameraPerformConfig.cameraControllerData.circleAngleOffset;
                cameraController.pitchOffset = cameraPerformConfig.cameraControllerData.pitchOffset;
                cameraController.distanceOffset = cameraPerformConfig.cameraControllerData.distanceOffset;
                cameraController.blendHint = cameraPerformConfig.cameraControllerData.blendHint;
                cameraController.camShakeType = cameraPerformConfig.cameraControllerData.camShakeType;
                cameraController.camShakeAmpli = cameraPerformConfig.cameraControllerData.camShakeAmpli;
                cameraController.camShakeFreq = cameraPerformConfig.cameraControllerData.camShakeFreq;
                cameraController.customLookAtTarget = cameraPerformConfig.cameraControllerData.customLookAtTarget;
                cameraController.customLookATargetDistance = cameraPerformConfig.cameraControllerData.customLookATargetDistance;
                cameraController.cameraPositionOffset = cameraPerformConfig.cameraControllerData.cameraPositionOffset;

                CameraNameInfo cameraNameInfo = CCameraTools.AnalysisCameraInfoFromName(oriCam.name);
                if (CCameraTools.IsNoFollowOrLookAtCam(cameraNameInfo))
                {
                    VC.LookAt = null;
                    VC.Follow = null;

                    // 清理Follow LookAt组件
                    var transposer = VC.GetCinemachineComponent<CinemachineTransposer>();
                    var composer = VC.GetCinemachineComponent<CinemachineComposer>();
#if UNITY_EDITOR
                    if (transposer != null)
                        DestroyImmediate(transposer);
                    if (composer != null)
                        DestroyImmediate(composer);
#else
                    if (transposer != null)
                        Destroy(transposer);
                    if (composer != null)
                        Destroy(composer);
#endif
                    CloneCameraState absCameraState = new CloneCameraState();
                    absCameraState.cloneCamera = cloneCamera;
                    absCameraState.hangingGarbage = new List<GameObject>();
                    absCameraState.relevantObject = new List<GameObject>();
                    absCameraState.cameraPerformConfig = null;
                    absCameraState.parentCamera = parentCamera;
                    absCameraState.hasChild = false;
                    absCameraState.justBorn = true;
                    absCameraState.readyToDestory = false;
                    absCameraState.isAbsTransformCam = true;

                    cameraController.Init(cameraNameInfo);
                    cameraController.Refresh();

                    return absCameraState;
                }

                List<GameObject> lookAtRelevant = new List<GameObject>();
                List<GameObject> followRelevant = new List<GameObject>();
                if (cameraNameInfo != null)
                {
                    HangingWithRelevantObjects lookAtHangingWithRelevant = stationTemplateManager.GetHangingTarget(cameraNameInfo.baseCamInfo.lookAtHanging, cameraGroupInfo, oriCam.transform.parent, cameraPerformConfig.lookAtPerform);
                    HangingWithRelevantObjects followHangingWithRelevant = stationTemplateManager.GetHangingTarget(cameraNameInfo.baseCamInfo.followHanging, cameraGroupInfo, oriCam.transform.parent, cameraPerformConfig.followPerform);
                    if (lookAtHangingWithRelevant == null || followHangingWithRelevant == null)
                    {
#if UNITY_EDITOR
                        DestroyImmediate(cloneCamera);
#else
                        Destroy(cloneCamera);
#endif
                        return null;
                    }
                    VC.LookAt = lookAtHangingWithRelevant.hangings;
                    VC.Follow = followHangingWithRelevant.hangings;
                    lookAtRelevant = lookAtHangingWithRelevant.relevantObjects;
                    followRelevant = followHangingWithRelevant.relevantObjects;
                }

                List<GameObject> garbageList = new List<GameObject>();

                //计算和相机平行的挂点rotate节点，保证相机平移参数和习惯性认知的一致性。以lookat挂点和相机相对位置进行lookat和follow挂点rotate节点旋转值计算
                GameObject tempLookAtHanging = new GameObject();
                tempLookAtHanging.transform.parent = VC.LookAt.transform;
                tempLookAtHanging.name = CCameraTools.GetUniqueGameObjectName("tempLookAtHanging", tempLookAtHanging.transform.parent);
                tempLookAtHanging.transform.localRotation = Quaternion.identity;
                tempLookAtHanging.transform.localPosition = Vector3.zero;
                GameObject tempFollowHanging = new GameObject();
                tempFollowHanging.transform.parent = VC.Follow.transform;
                tempFollowHanging.name = CCameraTools.GetUniqueGameObjectName("tempFollowHanging", tempFollowHanging.transform.parent);
                tempFollowHanging.transform.localRotation = Quaternion.identity;
                tempFollowHanging.transform.localPosition = Vector3.zero;
                tempFollowHanging.transform.rotation = Quaternion.Euler(new Vector3(tempFollowHanging.transform.rotation.eulerAngles.x, tempFollowHanging.transform.rotation.eulerAngles.y, 0));
                GameObject tempCamPositoin = new GameObject();
                tempCamPositoin.transform.parent = tempFollowHanging.transform;
                tempCamPositoin.name = CCameraTools.GetUniqueGameObjectName("tempCamPositoin", tempCamPositoin.transform.parent);
                tempCamPositoin.transform.localRotation = Quaternion.identity;
                tempCamPositoin.transform.localPosition = VC.GetCinemachineComponent<CinemachineTransposer>().m_FollowOffset;
                //tempCamPositoin.transform.parent = tempFollowHanging.transform.parent;
                tempLookAtHanging.transform.rotation = Quaternion.LookRotation(tempCamPositoin.transform.position - tempLookAtHanging.transform.position);

                Vector3 followWorldOffset = Vector3.zero;

                switch (cameraNameInfo.baseCamInfo.lookAtHanging.type)
                {
                    case HangingType.ActorBaseHanging:
                        if (cameraPerformConfig.lookAtPerform == HangingPerformType.StaticReferCurrentPose)
                        {
                            lookAtRelevant.Remove(VC.LookAt.gameObject);

                            GameObject go = new GameObject(CCameraTools.GetHangingStructuredName(VC.LookAt.name, false));
                            go.name = CCameraTools.GetUniqueGameObjectName(go.name, stationTemplateManager.getHangingGroup().transform);
                            go.transform.parent = stationTemplateManager.getHangingGroup().transform;
                            go.transform.position = VC.LookAt.position;
                            go.transform.rotation = VC.LookAt.rotation;
                            go.transform.localScale = Vector3.one;

                            Transform offsetTarget = GeneratePositionOffsetsTransform(go.transform, cameraPerformConfig.lookAtHangingOffset, tempLookAtHanging.transform.rotation, true, false);
                            VC.LookAt = offsetTarget;
                            garbageList.Add(go);
                            lookAtRelevant.Add(go);
                            followWorldOffset = offsetTarget.position - offsetTarget.parent.position;
                        }
                        else
                        {
                            lookAtRelevant.Remove(VC.LookAt.gameObject);

                            Transform offsetTarget = GeneratePositionOffsetsTransform(VC.LookAt, cameraPerformConfig.lookAtHangingOffset, tempLookAtHanging.transform.rotation, true, false);
                            VC.LookAt = offsetTarget;
                            garbageList.Add(offsetTarget.parent.gameObject);
                            lookAtRelevant.Add(offsetTarget.parent.parent.gameObject);
                            followWorldOffset = offsetTarget.position - offsetTarget.parent.position;
                        }
                        break;
                    case HangingType.TargetGroup:
                        CinemachineTargetGroup.Target[] targets = VC.LookAt.GetComponent<CinemachineTargetGroup>().m_Targets;
                        if (cameraPerformConfig.lookAtPerform == HangingPerformType.StaticReferCurrentPose)
                        {
                            for (int i = 0; i < targets.Length; i++)
                            {
                                lookAtRelevant.Remove(targets[i].target.gameObject);

                                GameObject go = new GameObject(CCameraTools.GetHangingStructuredName(targets[i].target.name, false));
                                go.name = CCameraTools.GetUniqueGameObjectName(go.name, stationTemplateManager.getHangingGroup().transform);
                                go.transform.parent = stationTemplateManager.getHangingGroup().transform;
                                go.transform.position = targets[i].target.position;
                                go.transform.rotation = targets[i].target.rotation;
                                go.transform.localScale = Vector3.one;

                                targets[i].target = go.transform;
                                garbageList.Add(go);
                                lookAtRelevant.Add(go);
                            }
                        }
                        Transform targetGroupOffsetTarget = GeneratePositionOffsetsTransform(VC.LookAt, cameraPerformConfig.lookAtHangingOffset, tempLookAtHanging.transform.rotation, true, false);
                        VC.LookAt = targetGroupOffsetTarget;
                        followWorldOffset = targetGroupOffsetTarget.position - targetGroupOffsetTarget.parent.position;

                        garbageList.Add(VC.LookAt.parent.parent.gameObject);
                        break;
                    case HangingType.FixedPoint:
                        Transform fixedOffsetTarget = GeneratePositionOffsetsTransform(VC.LookAt, cameraPerformConfig.lookAtHangingOffset, tempLookAtHanging.transform.rotation, true, false);
                        VC.LookAt = fixedOffsetTarget;
                        garbageList.Add(fixedOffsetTarget.parent.gameObject);
                        followWorldOffset = fixedOffsetTarget.position - fixedOffsetTarget.parent.position;
                        break;
                    default: break;
                }

                switch (cameraNameInfo.baseCamInfo.followHanging.type)
                {
                    case HangingType.ActorBaseHanging:
                        if (cameraPerformConfig.followPerform == HangingPerformType.StaticReferCurrentPose)
                        {
                            followRelevant.Remove(VC.Follow.gameObject);

                            GameObject go = new GameObject(CCameraTools.GetHangingStructuredName(VC.Follow.name, false));
                            go.name = CCameraTools.GetUniqueGameObjectName(go.name, stationTemplateManager.getHangingGroup().transform);
                            go.transform.parent = stationTemplateManager.getHangingGroup().transform;
                            go.transform.position = VC.Follow.position;
                            go.transform.rotation = VC.Follow.rotation;
                            go.transform.localScale = Vector3.one;

                            Transform offsetTarget = GeneratePositionOffsetsTransform(go.transform, followWorldOffset, Quaternion.identity, false, true);
                            VC.Follow = offsetTarget;
                            garbageList.Add(go);
                            followRelevant.Add(go);
                        }
                        else
                        {
                            followRelevant.Remove(VC.Follow.gameObject);

                            Transform offsetTarget = GeneratePositionOffsetsTransform(VC.Follow, followWorldOffset, Quaternion.identity, false, true);
                            VC.Follow = offsetTarget;
                            garbageList.Add(offsetTarget.parent.gameObject);
                            followRelevant.Add(offsetTarget.parent.parent.gameObject);
                        }
                        break;
                    case HangingType.TargetGroup:
                        CinemachineTargetGroup.Target[] targets = VC.Follow.GetComponent<CinemachineTargetGroup>().m_Targets;
                        if (cameraPerformConfig.followPerform == HangingPerformType.StaticReferCurrentPose)
                        {
                            for (int i = 0; i < targets.Length; i++)
                            {
                                followRelevant.Remove(targets[i].target.gameObject);

                                GameObject go = new GameObject(CCameraTools.GetHangingStructuredName(targets[i].target.name, false));
                                go.name = CCameraTools.GetUniqueGameObjectName(go.name, stationTemplateManager.getHangingGroup().transform);
                                go.transform.parent = stationTemplateManager.getHangingGroup().transform;
                                go.transform.position = targets[i].target.position;
                                go.transform.rotation = targets[i].target.rotation;
                                go.transform.localScale = Vector3.one;

                                targets[i].target = go.transform;
                                garbageList.Add(go);
                                followRelevant.Add(go);
                            }
                        }
                        Transform targetGroupOffsetTarget = GeneratePositionOffsetsTransform(VC.Follow, followWorldOffset, Quaternion.identity, false, true);
                        VC.Follow = targetGroupOffsetTarget;

                        garbageList.Add(VC.Follow.parent.parent.gameObject);
                        break;
                    case HangingType.FixedPoint:
                        Transform fixedOffsetTarget = GeneratePositionOffsetsTransform(VC.Follow, followWorldOffset, Quaternion.identity, false, true);
                        VC.Follow = fixedOffsetTarget;
                        garbageList.Add(fixedOffsetTarget.parent.gameObject);
                        break;
                    default: break;
                }

                cameraController.Init(cameraNameInfo);
                cameraController.Refresh();

#if UNITY_EDITOR
                DestroyImmediate(tempFollowHanging);
                DestroyImmediate(tempLookAtHanging);
                DestroyImmediate(tempCamPositoin);
#else
                Destroy(tempFollowHanging);
                Destroy(tempLookAtHanging);
                Destroy(tempCamPositoin);
#endif

                CloneCameraState cloneCameraState = new CloneCameraState();
                cloneCameraState.cloneCamera = cloneCamera;
                cloneCameraState.hangingGarbage = garbageList;
                List<GameObject> mergeList = new List<GameObject>(lookAtRelevant);
                mergeList.AddRange(followRelevant.Except(lookAtRelevant));
                cloneCameraState.relevantObject = mergeList;
                cloneCameraState.cameraPerformConfig = cameraPerformConfig;
                cloneCameraState.parentCamera = parentCamera;
                cloneCameraState.hasChild = false;
                cloneCameraState.justBorn = true;
                cloneCameraState.readyToDestory = false;
                cloneCameraState.isAbsTransformCam = false;

                return cloneCameraState;
            }
            return null;
        }

        private CloneCameraState AbsTransformCameraGenerate(Vector3 animCamPosition, Vector3 animCamRotate, float animCamFOV, GameObject parentCamera, Transform cloneGameraGroupTransform)
        {
            GameObject absTransformCam = new GameObject("AbsTransformCam");
            CinemachineVirtualCamera vc = absTransformCam.AddComponent<CinemachineVirtualCamera>();

            var transposer = vc.GetCinemachineComponent<CinemachineTransposer>();
            var composer = vc.GetCinemachineComponent<CinemachineComposer>();

#if UNITY_EDITOR
            if (transposer != null) DestroyImmediate(transposer);
            if (composer != null) DestroyImmediate(composer);
#else
            if (transposer != null) Destroy(transposer);
            if (composer != null) Destroy(composer);
#endif
            absTransformCam.transform.parent = cloneGameraGroupTransform;
            absTransformCam.transform.position = animCamPosition;
            absTransformCam.transform.rotation = Quaternion.Euler(animCamRotate);
            absTransformCam.transform.localScale = Vector3.one;

            vc.m_Lens.FieldOfView = animCamFOV;

            CloneCameraState absCameraState = new CloneCameraState();
            absCameraState.cloneCamera = absTransformCam;
            absCameraState.hangingGarbage = new List<GameObject>();
            absCameraState.relevantObject = new List<GameObject>();
            absCameraState.cameraPerformConfig = null;
            absCameraState.parentCamera = parentCamera;
            absCameraState.hasChild = false;
            absCameraState.justBorn = true;
            absCameraState.readyToDestory = false;
            absCameraState.isAbsTransformCam = true;

            return absCameraState;
        }

        private CloneCameraState AnimatedCameraGenerate(AnimationClip camAnim, Transform cloneGameraGroupTransform)
        {
            GameObject animatedTransformCam = new GameObject("AnimatedCam");
            CinemachineVirtualCamera vc = animatedTransformCam.AddComponent<CinemachineVirtualCamera>();
            CameraController controller = animatedTransformCam.AddComponent<CameraController>();

            animatedTransformCam.transform.parent = cloneGameraGroupTransform;
            animatedTransformCam.transform.localPosition = Vector3.zero;
            animatedTransformCam.transform.localRotation = Quaternion.identity;
            animatedTransformCam.transform.localScale = Vector3.one;

            var transposer = vc.GetCinemachineComponent<CinemachineTransposer>();
            var composer = vc.GetCinemachineComponent<CinemachineComposer>();

#if UNITY_EDITOR
            if (transposer != null) DestroyImmediate(transposer);
            if (composer != null) DestroyImmediate(composer);
#else
            if (transposer != null) Destroy(transposer);
            if (composer != null) Destroy(composer);
#endif
            controller.animBaseCam = true;
            controller.camAnimClip = camAnim;

            controller.Init();
            controller.Refresh();

            CloneCameraState animatedCameraState = new CloneCameraState();
            animatedCameraState.cloneCamera = animatedTransformCam;
            animatedCameraState.hangingGarbage = new List<GameObject>();
            animatedCameraState.relevantObject = new List<GameObject>();
            animatedCameraState.cameraPerformConfig = null;
            animatedCameraState.parentCamera = null;
            animatedCameraState.hasChild = false;
            animatedCameraState.justBorn = true;
            animatedCameraState.readyToDestory = false;
            animatedCameraState.isAbsTransformCam = false;

            return animatedCameraState;
        }

        private Transform GeneratePositionOffsetsTransform(Transform ori, Vector3 positionOffset, Quaternion aimCamRotate, bool isLocalOffset, bool isLocalRotate)
        {
            GameObject cloneHangingRotatePipe = new GameObject(ZString.Concat(ori.name, "<RotatePipe>"));
            cloneHangingRotatePipe.transform.parent = ori;
            cloneHangingRotatePipe.transform.localPosition = new Vector3(0f, 0f, 0f);
            if (isLocalRotate)
                cloneHangingRotatePipe.transform.localRotation = aimCamRotate;
            else
                cloneHangingRotatePipe.transform.rotation = aimCamRotate;
            cloneHangingRotatePipe.transform.localScale = Vector3.one;

            GameObject cloneHangingPositionOffset = new GameObject(ZString.Concat(ori.name, "<PositionOffset>"));
            cloneHangingPositionOffset.transform.parent = cloneHangingRotatePipe.transform;
            if (isLocalOffset)
            {
                cloneHangingPositionOffset.transform.localPosition = positionOffset;
            }
            else
            {
                cloneHangingPositionOffset.transform.localPosition = Vector3.zero;
                cloneHangingPositionOffset.transform.position += positionOffset;
            }
                
            cloneHangingPositionOffset.transform.localRotation = Quaternion.identity;
            cloneHangingPositionOffset.transform.localScale = Vector3.one;

            return cloneHangingPositionOffset.transform;
        }

        private bool CheckCameraIsSimilar(CloneCameraState cam1State, CloneCameraState cam2State)
        {
            if (cam1State != null && cam2State != null && 
                cam1State.cloneCamera != null && cam2State.cloneCamera != null && 
                cam1State.cameraPerformConfig != null && cam2State.cameraPerformConfig != null)
            {
                //Debug.Log(cam1State.targetTag == cam2State.targetTag);
                //Debug.Log(cam1State.cameraPerformConfig.cameraControllerData.FOVOffset == cam2State.cameraPerformConfig.cameraControllerData.FOVOffset);
                //Debug.Log(cam1State.cameraPerformConfig.cameraControllerData.screenRotateOffset == cam2State.cameraPerformConfig.cameraControllerData.screenRotateOffset);
                //Debug.Log(cam1State.cameraPerformConfig.cameraControllerData.screenHorizontalOffset == cam2State.cameraPerformConfig.cameraControllerData.screenHorizontalOffset);
                //Debug.Log(cam1State.cameraPerformConfig.cameraControllerData.screenVerticalOffset == cam2State.cameraPerformConfig.cameraControllerData.screenVerticalOffset);
                //Debug.Log(cam1State.cameraPerformConfig.cameraControllerData.circleAngleOffset == cam2State.cameraPerformConfig.cameraControllerData.circleAngleOffset);
                //Debug.Log(cam1State.cameraPerformConfig.cameraControllerData.pitchOffset == cam2State.cameraPerformConfig.cameraControllerData.pitchOffset);
                //Debug.Log(cam1State.cameraPerformConfig.cameraControllerData.distanceOffset == cam2State.cameraPerformConfig.cameraControllerData.distanceOffset);
                //Debug.Log(cam1State.cameraPerformConfig.followHangingOffset == cam2State.cameraPerformConfig.followHangingOffset);
                //Debug.Log(cam1State.cameraPerformConfig.lookAtHangingOffset == cam2State.cameraPerformConfig.lookAtHangingOffset);
                //Debug.Log(cam1State.cameraPerformConfig.lookAtPerform == cam2State.cameraPerformConfig.lookAtPerform);
                //Debug.Log(cam1State.cameraPerformConfig.followPerform == cam2State.cameraPerformConfig.followPerform);
                if (CCameraTools.GetOriCamNameFromClone(cam1State.cloneCamera.name) == CCameraTools.GetOriCamNameFromClone(cam2State.cloneCamera.name) && 
                    cam1State.targetTag == cam2State.targetTag &&
                    cam1State.cameraPerformConfig.cameraControllerData.FOVOffset == cam2State.cameraPerformConfig.cameraControllerData.FOVOffset &&
                    cam1State.cameraPerformConfig.cameraControllerData.screenRotateOffset == cam2State.cameraPerformConfig.cameraControllerData.screenRotateOffset &&
                    cam1State.cameraPerformConfig.cameraControllerData.screenHorizontalOffset == cam2State.cameraPerformConfig.cameraControllerData.screenHorizontalOffset &&
                    cam1State.cameraPerformConfig.cameraControllerData.screenVerticalOffset == cam2State.cameraPerformConfig.cameraControllerData.screenVerticalOffset &&
                    cam1State.cameraPerformConfig.cameraControllerData.circleAngleOffset == cam2State.cameraPerformConfig.cameraControllerData.circleAngleOffset &&
                    cam1State.cameraPerformConfig.cameraControllerData.pitchOffset == cam2State.cameraPerformConfig.cameraControllerData.pitchOffset &&
                    cam1State.cameraPerformConfig.cameraControllerData.distanceOffset == cam2State.cameraPerformConfig.cameraControllerData.distanceOffset &&
                    cam1State.cameraPerformConfig.cameraControllerData.cameraPositionOffset == cam2State.cameraPerformConfig.cameraControllerData.cameraPositionOffset &&
                    cam1State.cameraPerformConfig.followHangingOffset == cam2State.cameraPerformConfig.followHangingOffset &&
                    cam1State.cameraPerformConfig.lookAtHangingOffset == cam2State.cameraPerformConfig.lookAtHangingOffset &&
                    cam1State.cameraPerformConfig.lookAtPerform == cam2State.cameraPerformConfig.lookAtPerform &&
                    cam1State.cameraPerformConfig.followPerform == cam2State.cameraPerformConfig.followPerform)
                {
                    Debug.Log("base param sim");
                    CinemachineVirtualCamera cam1VC = cam1State.cloneCamera.GetComponent<CinemachineVirtualCamera>();
                    CinemachineVirtualCamera cam2VC = cam2State.cloneCamera.GetComponent<CinemachineVirtualCamera>();
                    if (cam1VC != null && cam2VC != null)
                    {
                        Transform c1Follow = cam1VC.Follow;
                        Transform c2Follow = cam2VC.Follow;
                        Transform c1LookAt = cam1VC.LookAt;
                        Transform c2LookAt = cam2VC.LookAt;
                        if (c1Follow != null && c2Follow != null && c1LookAt != null && c2LookAt != null)
                        {
                            float followDist = Vector3.Distance(c1Follow.position, c2Follow.position);
                            float followRotateDist = Vector3.Distance(c1Follow.rotation.eulerAngles, c2Follow.rotation.eulerAngles);
                            float lookAtDist = Vector3.Distance(c1LookAt.position, c2LookAt.position);

                            Debug.Log(ZString.Concat("followDist: ", followDist));
                            Debug.Log(ZString.Concat("followRotateDist: ", followRotateDist));
                            Debug.Log(ZString.Concat("lookAtDist: ", lookAtDist));

                            if (followDist <= similarCamPositionDistanceMax && lookAtDist <= similarCamPositionDistanceMax && followRotateDist <= similarCamRotationDistanceMax)
                                return true;
                        }
                    }
                }
            }
            return false;
        }

        private void CleanCloneCameras()
        {
            if (CheckStationTemplateState(stationTemplateManager) == false)
                return;
            if (stationTemplateManager.cloneCameraBuffer.Count > 0)
            {
                CinemachineBrain brain = stationTemplateManager.GetCinemachineBrain();
                if (brain != null)
                {
                    List<CloneCameraState> garbage = new List<CloneCameraState>();
                    foreach (CloneCameraState cloneCameraState in stationTemplateManager.cloneCameraBuffer)
                    {
                        if (cloneCameraState.cloneCamera == null)
                        {
                            garbage.Add(cloneCameraState);
                            continue;
                        }
                        if (!brain.IsLive(cloneCameraState.cloneCamera.GetComponent<CinemachineVirtualCameraBase>()) && !cloneCameraState.justBorn && cloneCameraState.parentCamera == null)
                        {
                            if (!cloneCameraState.readyToDestory)
                            {
                                cloneCameraState.readyToDestory = true;
                                continue;
                            }
                            garbage.Add(cloneCameraState);
                            if (cloneCameraState.hasChild)
                            {
                                foreach (CloneCameraState checkChildCamera in stationTemplateManager.cloneCameraBuffer)
                                {
                                    if (!checkChildCamera.hasChild && checkChildCamera.parentCamera == cloneCameraState.cloneCamera)
                                        garbage.Add(checkChildCamera);
                                }
                            }
                        }
                        if (cloneCameraState.justBorn)
                        {
                            cloneCameraState.justBorn = false;
                        }
                    }
                    foreach (CloneCameraState cloneCameraState in garbage)
                    {
                        stationTemplateManager.cloneCameraBuffer.Remove(cloneCameraState);
                    }
                    foreach (CloneCameraState cloneCameraState in garbage)
                    {
                        if (cloneCameraState.cloneCamera != null)
                        {
#if UNITY_EDITOR
                            DestroyImmediate(cloneCameraState.cloneCamera);
#else
                                Destroy(cloneCameraState.cloneCamera);
#endif
                        }
                        if (cloneCameraState.hangingGarbage != null)
                        {
#if UNITY_EDITOR
                            foreach(GameObject g in  cloneCameraState.hangingGarbage)
                                DestroyImmediate(g);
#else
                            foreach(GameObject g in  cloneCameraState.hangingGarbage)
                                Destroy(g);
#endif
                        }
                    }
                    garbage.Clear();
                }
            }
        }

        private void ResetCloneCameraPriority()
        {
            if (CheckStationTemplateState(stationTemplateManager) == false)
                return;
            if (stationTemplateManager.cloneCameraBuffer != null)
            {
                foreach (CloneCameraState cloneCameraState in stationTemplateManager.cloneCameraBuffer)
                {
                    if (cloneCameraState != null && cloneCameraState.cloneCamera != null && cloneCameraState.parentCamera == null)
                    {
                        cloneCameraState.cloneCamera.GetComponent<CinemachineVirtualCameraBase>().Priority = 0;
                    }
                }
            }
        }

        private bool CheckStationTemplateState(StationTemplateManager stm)
        {
            if (stationTemplateManager != null)
                if (stationTemplateManager.cloneCameraBuffer != null)
                    if (stationTemplateManager.GetCinemachineBrain() != null)
                        return true;
            return false;
        }

        //private bool CheckSlotActorHangingValid(int slotIndex, bool checkDynamic)
        //{
        //    if (stationTemplateManager != null)
        //        if (stationTemplateManager.actorSlots != null)
        //            if (slotIndex >= 0 && slotIndex < stationTemplateManager.actorSlots.Count)
        //                return checkDynamic ? (stationTemplateManager.actorSlots[slotIndex].dynamicHangings != null) : (stationTemplateManager.actorSlots[slotIndex].staticHangings != null);
        //    return false;
        //}

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

