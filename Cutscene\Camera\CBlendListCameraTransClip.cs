using Cinemachine;
using Sirenix.OdinInspector;
using Slate;
using Slate.ActionClips;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using CEditor;
using System.Linq;

[Name("运镜序列镜头配置")]
[Attachable(typeof(CCameraTransTrack))]
[Category("C类编辑器")]
public class CBlendListCameraTransClip : CCameraClipParent
{
    [SerializeField, LabelText("镜头标签"), ValueDropdown("TargetTagConfig")]
    public string TargetTag = "";

    private IEnumerable TargetTagConfig()
    {
        CCameraTransTrack track = parent as CCameraTransTrack;
        if (track != null && track.CamTemplateID != 0)
            return StationTemplateManager.GetTargetSlotsListV3(track.CamTemplateID).Select(x => x.targetSlotGroupTag);
        else if (StationTemplateManager.instance != null && StationTemplateManager.instance.targetSlotCameraGroups != null && StationTemplateManager.instance.targetSlotCameraGroups.Count > 0)
            return StationTemplateManager.instance.GetTargetSlotsList();
        return StationTemplateManager.GetTargetSlotsListV3().Select(x => x.targetSlotGroupTag);
    }
    public string GetTargetTag()
    {
        if (TargetTag != null) return TargetTag;
        return null;
    }

    //[SerializeField, LabelText("镜头挂点"), ValueDropdown("CamPickerConfig")]
    //public string CamPickerName = "";

    //private IEnumerable CamPickerConfig()
    //{
    //    Cutscene cutscene = root as Cutscene;
    //    if (cutscene == null)
    //        return null;
    //    if (cutscene.StoryScene == null)
    //        return null;
    //    var stationTemplatePickers = cutscene.StoryScene.GetComponentsInChildren<StationTemplatePicker>();
    //    List<string> pickerNames;
    //    pickerNames = stationTemplatePickers.Select(x => x.name).ToList();
    //    return pickerNames;
    //}

    //[HideInEditorMode]
    [SerializeField, LabelText("过滤微小镜头变换")]
    public bool filterWeakLensChanges = false;

    //[HideInInspector]
    [SerializeField, LabelText("切镜过渡时长")]
    public float lensCutBlendTime = 0;

    //[HideInInspector]
    [SerializeField, LabelText("切镜过渡曲线类别")]
    public CinemachineBlendDefinition.Style lensCutBlendType = CinemachineBlendDefinition.Style.Cut;

    //[HideInInspector]
    [SerializeField, LabelText("切镜自定义过渡曲线")]
    public AnimationCurve lensCutCustomBlendCurve;

    //[HideInInspector]
    [SerializeField, LabelText("运镜组合")]
    public List<BlendListItemIDPerform> cameraPerformConfigsList = new List<BlendListItemIDPerform>();

    private GameObject performer;
    private CameraSwitchSystem cameraSwitchSystem;


    public override string info
    { 
        get
        {
            return "运镜序列";
        }
    }

    //Called in forward sampling when the clip is entered
    protected override void OnEnter()
    {
        // performer = GetActor();
        // cameraSwitchSystem = performer.GetComponent<CameraSwitchSystem>();
        if (StationTemplateManager.instance != null)
        {
            CCameraTransTrack track = parent as CCameraTransTrack;
            if (track != null)
                track.InitStationTemplateOriginalTransform(CamPickerName);

            cameraSwitchSystem = StationTemplateManager.instance.cameraSwitchSystem;
            if (cameraSwitchSystem != null)
            {
                CameraSwitchPerformConfig cameraSwitchPerform = new CameraSwitchPerformConfig();
                cameraSwitchPerform.blendTime = lensCutBlendTime;
                cameraSwitchPerform.blendStyle = lensCutBlendType;
                cameraSwitchPerform.custromBlendCurve = lensCutCustomBlendCurve;

                CameraGroupIDTransParam cameraGroupIDTransParam = new CameraGroupIDTransParam();
                cameraGroupIDTransParam.camGroup = CCameraTools.TransBlendListItemIDListToCameraIDTransParamList(cameraPerformConfigsList);
                cameraGroupIDTransParam.camGroupTransInPerform = cameraSwitchPerform;


                //cameraSwitchSystem.ChangeBlendListCamera(cameraGroupRefTransParam, GetTargetTag());
                StationTemplateManager.instance.SetCameraGroupID(cameraGroupIDTransParam, GetTargetTag(), filterWeakLensChanges);
            }
        }
    }

    //Called per frame while the clip is updating. Time is the local time within the clip.
    //So a time of 0 means the start of the clip.
    protected override void OnUpdate(float time, float previousTime)
    {

    }

    //Called in forwards sampling when the clip exits
    protected override void OnExit() { }

    //Called in backwards sampling when the clip is entered.
    protected override void OnReverseEnter() { }

    //Called in backwards sampling when the clip exits.
    protected override void OnReverse() { }

//#if UNITY_EDITOR
//    public override void OnInspectorGUI()
//    {
//        base.OnInspectorGUI();
//        CCameraTransTrack track = parent as CCameraTransTrack;
//        int templateID = 0;
//        if (track != null)
//            templateID = track.CamTemplateID;

//        foreach (BlendListItemIDPerform i in cameraPerformConfigsList)
//        {
//            i.SetTargetTag(GetTargetTag());
//            i.SetTemplateID(templateID);
//        }
//    }
//#endif
}
