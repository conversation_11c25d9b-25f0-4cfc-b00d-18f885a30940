using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace CEditor
{
    /// <summary>
    /// body
    /// </summary>
    public enum BodyPosture
    {
        Stand,
        Set,
        StandUpOrSetDown,
        Others
    }

    [Serializable]
    [ExecuteAlways]
    public class BodyAssetDetail
    {
        public int ID;
        public string header;
        public string fileName;
        public string detail;
        public string personality;
        public string emotion;
        public bool isReady;
        public bool isLoop;

        public BodyPosture postrue;
        public bool hasPostureChange;
        public bool hasBodyRotate;
        public bool hasBodyMove;

        public float playRate;
        public bool canMirror;
        public AvoidBodyModelGlitchParam avoidBodyModelGlitchParam;
        public BodyWithCameraParam bodyWithCameraParam;
    }

    [Serializable]
    [ExecuteAlways]
    public class AvoidBodyModelGlitchParam
    {
        public bool EndPoseTouchTwoHand;
        public bool EndPoseTouthRHandWithLArm;
        public bool EndPoseTouthRHandWithBody;
        public bool EndPoseTouthLHandWithRArm;
        public bool EndPoseTouthLHandWithBody;

        public Vector3 EndPoseRHandPosition;
        public Vector3 EndPoseLHandPosition;
        public Vector3 EndPoseRElbowPosition;
        public Vector3 EndPoseLElbowPosition;
    }

    [Serializable]
    [ExecuteAlways]
    public class BodyWithCameraParam
    {
        public bool UpperBodySignificantMovement;
        public bool UpperBodyFastMovement;
        public bool LowerBodyMovement;
    }

    /// <summary>
    /// personality
    /// </summary>
    [Serializable]
    [ExecuteAlways]
    public class PersonalityAssetDetail
    {
        public string personality;
        public string detail;
    }

    /// <summary>
    /// body emote
    /// </summary>
    public enum EmoteType
    {
        Purpose,
        Mood,
        GamePlay
    }

    [Serializable]
    [ExecuteAlways]
    public class EmoteAssetDetail
    {
        public int ID;
        public string emote;
        public string detail;
        public EmoteType emoteType;
    }

    [Serializable]
    [ExecuteAlways]
    public class FaceAssetsDetail
    {
        public int ID;
        public string fileName;
        public string detail;
        public bool isReady;
    }
}