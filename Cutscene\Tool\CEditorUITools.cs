#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.IO;
using System.Drawing;
using Cysharp.Text;

namespace CEditor
{
    public static class CEditorUITools
    {
        public static Texture2D LoadTextureFromFile(string filePath)
        {
            // 1. 检查文件是否存在
            if (!File.Exists(filePath))
            {
                Debug.LogError(ZString.Concat("File not found: ", filePath));
                return null;
            }
            // 2. 读取字节数据
            byte[] fileData = File.ReadAllBytes(filePath);

            // 3. 创建Texture2D并加载数据
            Texture2D texture = new Texture2D(2, 2); // 初始尺寸会被覆盖
            bool success = texture.LoadImage(fileData); // 自动解析PNG/JPG

            if (!success)
            {
                Debug.LogError("Failed to load texture");
                return null;
            }

            return texture;
        }
    }
}
#endif

