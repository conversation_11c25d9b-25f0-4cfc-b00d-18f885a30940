using System.Collections.Generic;

namespace CEditor
{
    public struct PhonemeBlendShapeInfo
    {
        public string Phoneme;
        public string BlendShape;
        public float MaxWeight;
    }

    public struct BlendShapeSetting
    {
        public float MinVolume;
        public float MaxVolume;
        public float Smoothness;
        public List<PhonemeBlendShapeInfo> PhonemeBlendShapeTable;

        public BlendShapeSetting(float minVolume = -2.5f, float maxVolume = -1.0f, float smoothness = 0.1f,
            List<PhonemeBlendShapeInfo> phonemeBlendShapeTable = null)
        {
            MinVolume = minVolume;
            MaxVolume = maxVolume;
            Smoothness = smoothness;
            PhonemeBlendShapeTable = phonemeBlendShapeTable;
        }
    }

    public static class PresetBlendShapeSettings
    {
        public static readonly BlendShapeSetting Default = new BlendShapeSetting(
            minVolume: -2.5f,
            maxVolume: -1.0f,
            smoothness: 0.07f,
            phonemeBlendShapeTable: new List<PhonemeBlendShapeInfo>
            {
                new PhonemeBlendShapeInfo { Phoneme = "A", BlendShape = ".Ao2", MaxWeight = 0.45f },
                new PhonemeBlendShapeInfo { Phoneme = "E", BlendShape = ".Aa3", MaxWeight = 0.45f },
                new PhonemeBlendShapeInfo { Phoneme = "I", BlendShape = ".I1", MaxWeight = 0.55f },
                new PhonemeBlendShapeInfo { Phoneme = "O", BlendShape = ".UO", MaxWeight = 0.8f },
                new PhonemeBlendShapeInfo { Phoneme = "U", BlendShape = ".U1", MaxWeight = 0.9f },
                new PhonemeBlendShapeInfo { Phoneme = "-", BlendShape = "None", MaxWeight = 1.0f },
            }
        );
        
        public static readonly BlendShapeSetting Serious = new BlendShapeSetting(
            minVolume: -2.5f,
            maxVolume: -1.0f,
            smoothness: 0.1f,
            phonemeBlendShapeTable: new List<PhonemeBlendShapeInfo>
            {
                new PhonemeBlendShapeInfo { Phoneme = "A", BlendShape = ".A1", MaxWeight = 1.0f },
                new PhonemeBlendShapeInfo { Phoneme = "E", BlendShape = ".I3", MaxWeight = 1.0f },
                new PhonemeBlendShapeInfo { Phoneme = "I", BlendShape = ".I3", MaxWeight = 1.0f },
                new PhonemeBlendShapeInfo { Phoneme = "O", BlendShape = ".O", MaxWeight = 1.0f },
                new PhonemeBlendShapeInfo { Phoneme = "U", BlendShape = ".O", MaxWeight = 1.0f },
                new PhonemeBlendShapeInfo { Phoneme = "-", BlendShape = "None", MaxWeight = 1.0f },
            }
        );
    }
}