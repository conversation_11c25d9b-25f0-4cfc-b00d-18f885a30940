using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace CEditor
{
    [CreateAssetMenu]
    public class AnimTransPlayableConfig : ScriptableObject
    {
        // animator override controller
        public RuntimeAnimatorController playingOverrideControllerTemplate;
        public RuntimeAnimatorController staticOverrideControllerTemplate;

        // 双手重写
        public AvatarMask rightHandMask;
        public AvatarMask leftHandMask;

        public AvatarMask debugMask;

        public AnimationClip eyebrowMaskAnimClip;
        public AnimationClip eyesMaskAnimClip;
        public AnimationClip mouthMaskAnimClip;
        public AnimationClip blankFaceAnimClip;

        public AvatarMask eyebrowBoneMask;
        public AvatarMask eyesBoneMask;
        public AvatarMask mouthBoneMask;

        public AnimationClip faceBSLayerMask;
        public AnimationClip faceBoneLayerMask;
        public AnimationClip faceBSBoneBlankClip;

        public AnimationClip defaultCloseEyeClip;
        public AnimationClip defaultCloseEyeHalfClip;

        public AvatarMask defaultBreathMask;
        public AvatarMask faceBodyPartitionMask;

        public AnimationClip defaultStandIdleClip;
        public AnimationClip defaultSitIdleClip;
    }
}
