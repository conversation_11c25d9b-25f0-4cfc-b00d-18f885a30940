using System.Collections;
using System.Linq;
using CEditor;
using Sirenix.OdinInspector;
using UnityEngine;
using VGame.Framework;

namespace Slate.ActionClips
{
    [System.Serializable]
    public class CAnimEmoTransClipParams
    {
        [SerializeField, Tooltip("情绪"), ValueDropdown("EmoConfig")]
        public int emo;

        IEnumerable EmoConfig()
        {
            return CfgManager.tables.TbCEditorSentiment.DataList.Select(x =>
            {
                return new ValueDropdownItem($"{x.ID}-{x.Sentiment} [{x.Detail}]", x.ID); 
            });
        }
    }
    [Name("情绪动作配置")]
    [Attachable(typeof(CAnimEmoTransTrack))]
    [Category("C类编辑器")]
    public class CAnimEmoTransClip : BaseDialogueActorActionClip<CAnimEmoTransClipParams>
    {
        

        private GameObject performer;
        private AnimTransMaster master;

        [SerializeField, HideInInspector]
        private float _length = 1;

        public override float length
        {
            get { return _length; }
            set { _length = value; }
        }

        public override string info
        {
            get
            {
                //return GetCfg().emo.ToString();
                var cfg = CfgManager.tables.TbCEditorSentiment;
                if (cfg != null)
                {
                    var item = cfg.GetOrDefault(GetCfg().emo);
                    if (item != null)
                        return item.Detail.ToString();
                }
                return "情绪片段";
            }
        }

        //Called in forward sampling when the clip is entered
        protected override void OnEnter()
        {
            performer = GetActor();
            master = performer.GetComponent<AnimTransMaster>();
        }

        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime)
        {
            if (master != null && performer != null)
            {
                if(GetCfg().emo >= 0)
                    master.currentEmo = GetCfg().emo;
            }
        }

        //Called in forwards sampling when the clip exits
        protected override void OnExit() { }

        //Called in backwards sampling when the clip is entered.
        protected override void OnReverseEnter() { }

        //Called in backwards sampling when the clip exits.
        protected override void OnReverse() { }
    }
}