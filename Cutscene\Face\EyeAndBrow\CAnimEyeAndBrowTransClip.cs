using System.Collections;
using CEditor;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using ET;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;
using VGame;
using VGame.Framework;

namespace Slate.ActionClips
{
    [System.Serializable]
    public class CAnimEyeAndBrowTransClipParams
    {
        [SerializeField, LabelText("眉眼资产"), ValueDropdown("AnimationConfig"), OnValueChanged("OnAnimationChange")]
        public int animClipKey;

        private IEnumerable AnimationConfig()
        {
            return CfgManager.tables.TbCEditorEyeAndBrowAssets.DataList.Where(x => x.IsReady).Select(x =>
            {
                return new ValueDropdownItem($"{x.ID}-{x.Detail} [{x.FileName}]", x.ID);
            });
        }

        private AnimationClip _animationClip;
        public async ETTask<AnimationClip> GetClip()
        {
            if (_animationClip != null) return _animationClip;
            var cfg = CfgManager.tables.TbCEditorEyeAndBrowAssets.GetOrDefault(animClipKey);
            if (cfg != null && cfg.IsReady && !string.IsNullOrEmpty(cfg.FileName))
            {
                _animationClip = await DialogueUtil.Load<AnimationClip>(cfg.FileName);
                return _animationClip;
            }

            return null;
        }

        public void OnAnimationChange()
        {
            _animationClip = null;
        }
        //[SerializeField, LabelText("眉眼资产")]
        //public AnimationClip eyeAndBrowClip;

        [SerializeField, LabelText("眉眼动作强度")]
        public float eyeAndBrowWeight = 1;

        
        [SerializeField, LabelText("眼部动作播放逻辑")]
        public PlayingMode eyesPlayingMode = PlayingMode.Loop;
        
        [SerializeField, LabelText("眼部起始时间")]
        public float eyesStartTime = 0;
        
        [SerializeField, LabelText("眼部停止时间")]
        public float eyesEndTime = 99999;
        
        [SerializeField, LabelText("眼部过渡曲线")]
        public BlendType eyesBlendType = BlendType.EaseInOut;
        
        [SerializeField, LabelText("眼部过渡时长")]
        public float eyesBlendInDuration = 0.3f;
        
        [SerializeField, LabelText("眼部静态姿态混入类型")]
        public BlendType eyesStaticBlendType = BlendType.EaseInOut;
        
        [SerializeField, LabelText("眼部静态姿态混入时长")]
        public float eyesStaticBlendInDuration = 0.3f;

        
        [SerializeField, LabelText("眉毛动作播放逻辑")]
        public PlayingMode eyebrowPlayingMode = PlayingMode.Loop;
        
        [SerializeField, LabelText("眉毛起始时间")]
        public float eyebrowStartTime = 0;
        
        [SerializeField, LabelText("眉毛停止时间")]
        public float eyebrowEndTime = 3;
        
        [SerializeField, LabelText("眉毛过渡曲线")]
        public BlendType eyebrowBlendType = BlendType.EaseInOut;
        
        [SerializeField, LabelText("眉毛过渡时长")]
        public float eyebrowBlendInDuration = 1;
        
        [SerializeField, LabelText("眉毛静态姿态混入类型")]
        public BlendType eyebrowStaticBlendType = BlendType.EaseInOut;
        
        [SerializeField, LabelText("眉毛静态姿态混入时长")]
        public float eyebrowStaticBlendInDuration = 0.3f;

        
        [SerializeField, LabelText("眼部动作切换眨眼辅助")]
        public bool useEyesOverwriteAnim = true;
        
        [SerializeField, LabelText("闭眼资产")]
        public AnimationClip eyesOverwriteAnim;
        
        [SerializeField, LabelText("闭眼资产时间点")]
        public float eyesOverwriteAnimFrameTime = 0;
        
        [SerializeField, LabelText("闭眼资产混入时间")]
        public float eyesOverwriteAnimBlendInTime = 0.15f;
        
        [SerializeField, LabelText("闭眼资产混出时间")]
        public float eyesOverwriteAnimBlendOutTime = 0.3f;
    }
    
    
    [Name("面部眉眼动作配置")]
    [Attachable(typeof(CAnimEyeAndBrowTransTrack))]
    [Category("C类编辑器")]
    public class CAnimEyeAndBrowTransClip : BaseDialogueActorActionClip<CAnimEyeAndBrowTransClipParams>
    {
        private GameObject performer;

        public override string info
        {
            get
            {
                var asset = CfgManager.tables.TbCEditorEyeAndBrowAssets.GetOrDefault(GetCfg().animClipKey);
                if (asset != null)
                {
                    return asset.Detail;
                }
                else
                {
                    return "眉眼动作配置";
                }
            }
        }

        protected override void OnCreate()
        {
            base.OnCreate();

            var cfg = CfgManager.tables.TbCEditorEyeAndBrowAssets.DataList.Where(x => x.IsReady).ToList();
            if (cfg != null && cfg.Count > 0)
                GetCfg().animClipKey = cfg[0].ID;
        }

        //Called in forward sampling when the clip is entered
        //protected override void OnEnter()
        protected override async ETTask OnEnterAsync()
        {
            performer = GetActor();
            AnimTransMaster master = performer.GetComponent<AnimTransMaster>();
            if (GetCfg().animClipKey < 0)
                return;
            
            if (master != null && performer != null)
            {
                // eyebrow
                master.currentFaceEyebrowAnimClip = await GetCfg().GetClip();
                master.currentFaceEyebrowWeight = GetCfg().eyeAndBrowWeight;
                master.eyebrowPlayingMode = GetCfg().eyebrowPlayingMode;
                master.eyebrowStartTime = GetCfg().eyebrowStartTime;
                master.eyebrowEndTime = GetCfg().eyebrowEndTime;
                master.faceEyebrowBlendType = GetCfg().eyebrowBlendType;
                master.faceEyebrowBlendInDuration = GetCfg().eyebrowBlendInDuration;
                master.faceEyebrowStaticBlendType = GetCfg().eyebrowStaticBlendType;
                master.faceEyebrowStaticBlendInDuration = GetCfg().eyebrowStaticBlendInDuration;
                // eyes
                master.currentFaceEyesAnimClip = await GetCfg().GetClip();
                master.currentFaceEyesWeight = GetCfg().eyeAndBrowWeight;
                master.eyesPlayingMode = GetCfg().eyesPlayingMode;
                master.eyesStartTime = GetCfg().eyesStartTime;
                master.eyesEndTime = GetCfg().eyesEndTime;
                master.faceEyesBlendType = GetCfg().eyesBlendType;
                master.faceEyesBlendInDuration = GetCfg().eyesBlendInDuration;
                master.faceEyesStaticBlendType = GetCfg().eyesStaticBlendType;
                master.faceEyesStaticBlendInDuration = GetCfg().eyesStaticBlendInDuration;

                master.useEyesOverwriteAnim = GetCfg().useEyesOverwriteAnim;
                master.eyesOverwriteAnim = GetCfg().eyesOverwriteAnim;
                master.eyesOverwriteAnimFrameTime = GetCfg().eyesOverwriteAnimFrameTime;
                master.eyesOverwriteAnimBlendInTime = GetCfg().eyesOverwriteAnimBlendInTime;
                master.eyesOverwriteAnimBlendOutTime = GetCfg().eyesOverwriteAnimBlendOutTime;

                master.EyebrowTransAnim();
                master.EyesTransAnim();
            }
        }

        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime) { }

        //Called in forwards sampling when the clip exits
        protected override void OnExit() { }

        //Called in backwards sampling when the clip is entered.
        protected override void OnReverseEnter() { }

        //Called in backwards sampling when the clip exits.
        protected override void OnReverse() { }
    }
}