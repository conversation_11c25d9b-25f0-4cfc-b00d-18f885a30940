using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Playables;

namespace CEditor
{
    [ExecuteAlways]
    public class SetupInitPoseAnimSystem : MonoBehaviour
    {
        public AnimationClip clip;
        public PlayableGraph playableGraph;
        private AnimationClipPlayable clipPlayable;
        private AnimationMixerPlayable mixerPlayable;
        private AnimationPlayableOutput playableOutput;

        private void Awake()
        {
            PlayAnimSystem();
        }

        void Start()
        {
            
        }

        public void PlayAnimSystem()
        {
            if (playableGraph.IsValid() && playableGraph.IsPlaying())
                playableGraph.Destroy();
            playableGraph = PlayableGraph.Create();
            playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);
            playableOutput = AnimationPlayableOutput.Create(playableGraph, "Animation", GetComponent<Animator>());

            mixerPlayable = AnimationMixerPlayable.Create(playableGraph, 1);
            playableOutput.SetSourcePlayable(mixerPlayable);

            // 将剪辑包裹在可播放项中
            clipPlayable = AnimationClipPlayable.Create(playableGraph, clip);
            playableGraph.Connect(clipPlayable, 0, mixerPlayable, 0);

            // 播放该图。
            playableGraph.Play();
            playableGraph.Evaluate();
        }

        public void UpdateAnim()
        {
            if (playableGraph.IsValid() && playableGraph.IsPlaying())
            {
                playableGraph.Disconnect(mixerPlayable, 0);
                clipPlayable = AnimationClipPlayable.Create(playableGraph, clip);
                playableGraph.Connect(clipPlayable, 0, mixerPlayable, 0);
                playableGraph.Evaluate();
            }
        }

        private void Update()
        {
            
        }

        void OnDisable()
        {
            //销毁该图创建的所有可播放项和 PlayableOutput。
            if (playableGraph.IsValid() && playableGraph.IsPlaying())
                playableGraph.Destroy();
        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

