%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5c4150458655bed4b9cd723bf76a92af, type: 3}
  m_Name: LookatSettingDefault
  m_EditorClassIdentifier: 
  InitLookAnimator: 1
  InitEyeAnimator: 1
  LookBoneWeights:
  - 0.4200521
  - 0.2021357
  - 0.3816873
  isStartAfterTPose: 0
  CompensationWeight: 0.7
  CompensatePositions: 0.1
  RotationSpeed: 0.5
  LookAnimatorAmount: 0.8
  StopLookingAbove: 140
  LookWhenAbove: 9
  HoldRotateToOppositeUntil: 13
  XRotationLimitsMinMax: {x: -65, y: 65}
  YRotationLimitsMinMax: {x: -47, y: 68}
  XElasticRange: 20
  YElasticRange: 15
  StartLookElasticRangeX: 40
  StartLookElasticRangeY: 25
  EyesRandomMovement: 0
  EyeSpeed: 1
  StopLookAbove: 120
  EyesClampHorizontalMinMax: {x: -120, y: 120}
  EyesClampVerticalMinMax: {x: -90, y: 90}
  EyeBlendshapeSettings:
    LookOutLeft: LookOut_l
    LookInLeft: LoockIn_L
    LookUpLeft: Lookup_L
    LookDownLeft: LookDown_L
    LookOutRight: LookOut_R
    LookInRight: LoockIn_R
    LookUpRight: Lookup_R
    LookDownRight: LookDown_R
    LeftEyeLeftRangeMinMax: {x: 0, y: 67}
    LeftEyeRightRangeMinMax: {x: 0, y: 100}
    RightEyeLeftRangeMinMax: {x: 0, y: 100}
    RightEyeRightRangeMinMax: {x: 0, y: 67}
  SyncClamping: 0
  SyncRanges: 0
  SyncUseAmount: 0
  SyncTarget: 1
