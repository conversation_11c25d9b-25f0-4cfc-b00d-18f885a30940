using System.Collections;
using CEditor;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using ET;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;
using VGame;
using VGame.Framework;

namespace Slate.ActionClips
{
    [System.Serializable]
    public class CSitHeightOffsetClipParams
    {
        [SerializeField, LabelText("坐姿高度偏移量")]
        public float pelvisHeightOffset = 0f;

        [SerializeField, LabelText("坐姿高度过渡时长")]
        public float pelvisHeightBlendTime = 0f;

        [SerializeField, LabelText("启用脚部IK偏移")]
        public bool useIkFootOffset = true;

        //TODO(blakelin) remove TEMP HANDIK ADD new Track
        public bool isHandIk = false;
        
        public bool enableHandIK = false;
    }
    
    [Name("坐姿高度偏移配置")]
    [Attachable(typeof(CSitOffsetIKTransTrack))]
    [Category("C类编辑器")]
    public class CSitOffsetIKTransClip : BaseDialogueActorActionClip<CSitHeightOffsetClipParams>
    {
        private GameObject performer;

        public override string info
        {
            get
            {
                return "sitHeightOffset: " + GetCfg().pelvisHeightOffset;
            }
        }

        //Called in forward sampling when the clip is entered
        //protected override void OnEnter()
        protected override async ETTask OnEnterAsync()
        {
            performer = GetActor();
            AnimTransMaster master = performer.GetComponent<AnimTransMaster>();

            //TODO(blakelin) remove TEMP HANDIK ADD new Track
            if (GetCfg().isHandIk)
            {
                master.useIKHandFix = GetCfg().enableHandIK;
                return;
            }
            if (master != null && performer != null)
                master.SetNewSitPelvisHeightOffset(GetCfg().pelvisHeightOffset, GetCfg().pelvisHeightBlendTime, GetCfg().useIkFootOffset);
        }

        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime) { }

        //Called in forwards sampling when the clip exits
        protected override void OnExit() { }

        //Called in backwards sampling when the clip is entered.
        protected override void OnReverseEnter() { }

        //Called in backwards sampling when the clip exits.
        protected override void OnReverse() { }
    }
}