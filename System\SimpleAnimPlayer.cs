using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Playables;

namespace CEditor
{
    [ExecuteAlways]
    public class SimpleAnimPlayer : MonoBehaviour
    {
        public AnimationClip clip;
        public bool lateOneFrame = false;
        public PlayableGraph playableGraph;
        private AnimationClipPlayable clipPlayable;
        private AnimationMixerPlayable mixerPlayable;
        private AnimationPlayableOutput playableOutput;

        private void Awake()
        {
            //PlayAnimSystem();
        }

        void Start()
        {
            
        }

        public void PlayAnimSystem()
        {
            if (playableGraph.IsValid() && playableGraph.IsPlaying())
                playableGraph.Destroy();
            playableGraph = PlayableGraph.Create(name);
            playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);
            playableOutput = AnimationPlayableOutput.Create(playableGraph, "Animation", GetComponent<Animator>());

            mixerPlayable = AnimationMixerPlayable.Create(playableGraph, 1);
            mixerPlayable.SetInputWeight(0, 1);
            playableOutput.SetSourcePlayable(mixerPlayable);

            // 将剪辑包裹在可播放项中
            clipPlayable = AnimationClipPlayable.Create(playableGraph, clip);
            playableGraph.Connect(clipPlayable, 0, mixerPlayable, 0);

            playableGraph.Play();

            if (lateOneFrame)
            {
                clipPlayable.SetTime(0);
                clipPlayable.Pause();
                playableGraph.Evaluate();

                StartCoroutine(PlayNextFrame());
            }
            else
            {
                PlayNow();
            }
                
        }

        private void PlayNow()
        {
            clipPlayable.SetTime(0);
            clipPlayable.Play();
            playableGraph.Evaluate();
        }

        IEnumerator PlayNextFrame()
        {
            yield return new WaitForEndOfFrame();
            PlayNow();
        }

        public void UpdateAnim()
        {
            if (playableGraph.IsValid() && playableGraph.IsPlaying())
            {
                playableGraph.Disconnect(mixerPlayable, 0);
                clipPlayable = AnimationClipPlayable.Create(playableGraph, clip);
                playableGraph.Connect(clipPlayable, 0, mixerPlayable, 0);
                playableGraph.Evaluate();
            }
        }

        private void Update()
        {

        }

        void OnDisable()
        {
            //销毁该图创建的所有可播放项和 PlayableOutput。
            if (playableGraph.IsValid() && playableGraph.IsPlaying())
                playableGraph.Destroy();
        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

