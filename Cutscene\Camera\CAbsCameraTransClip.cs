using System.Collections;
using Cinemachine;
using Sirenix.OdinInspector;
using UnityEngine;
using CEditor;
using System.Collections.Generic;
using System.Linq;
using VGame.Framework;
using System;
using UnityEditor;
using UnityEngine.UI;
using UnityEngine.UIElements;
using VGame;
using Vcm.Protocol;

namespace Slate.ActionClips
{
    [Name("固定镜头配置")]
    [Attachable(typeof(CCameraTransTrack))]
    [Category("C类编辑器")]
    public class CAbsCameraTransClip : CCameraClipParent
    {
        //[SerializeField, LabelText("镜头挂点"), ValueDropdown("CamPickerConfig")]
        //public string CamPickerName = "";

        //private IEnumerable CamPickerConfig()
        //{
        //    Cutscene cutscene = root as Cutscene;
        //    if (cutscene == null)
        //        return null;
        //    if (cutscene.StoryScene == null)
        //        return null;
        //    var stationTemplatePickers = cutscene.StoryScene.GetComponentsInChildren<StationTemplatePicker>();
        //    List<string> pickerNames;
        //    pickerNames = stationTemplatePickers.Select(x => x.name).ToList();
        //    return pickerNames;
        //}

        [SerializeField, LabelText("参考相机")]
        public GameObject refCamera;

#if UNITY_EDITOR
        [Button("导入参考相机参数", ButtonHeight = 30)]
        [GUIColor(0.4f, 0.8f, 1f)]
        private void CopyCamParam()
        {
            if (refCamera != null)
            {
                cameraWorldPosition = refCamera.transform.position;
                cameraWorldRotation = refCamera.transform.rotation.eulerAngles;
            }
            Camera camComponent = refCamera.GetComponent<Camera>();
            if (camComponent != null)
                cameraFOV = camComponent.fieldOfView;
        }
#endif

        [SerializeField, LabelText("世界坐标位置")]
        public Vector3 cameraWorldPosition;

        [SerializeField, LabelText("世界坐标旋转")]
        public Vector3 cameraWorldRotation;

        [SerializeField, LabelText("镜头FOV")]
        public float cameraFOV = 30;

        [SerializeField, LabelText("切镜过渡时长")]
        public float lensCutBlendTime = 0;

        [SerializeField, LabelText("切镜过渡曲线类别")]
        public CinemachineBlendDefinition.Style lensCutBlendType = CinemachineBlendDefinition.Style.Cut;

        [HideIf("@lensCutBlendType != CinemachineBlendDefinition.Style.Custom", 1)]
        [SerializeField, LabelText("切镜自定义过渡曲线")]
        public AnimationCurve lensCutCustomBlendCurve;
        
        public override string info
        {
            get
            {
                return "固定镜头";
            }
        }

        //Called in forward sampling when the clip is entered
        protected override void OnEnter()
        {
            //cameraSwitchSystem = performer.GetComponent<CameraSwitchSystem>();
            if (StationTemplateManager.instance != null)
            {
                CCameraTransTrack track = parent as CCameraTransTrack;
                if (track != null)
                    track.InitStationTemplateOriginalTransform(CamPickerName);

                CameraSwitchPerformConfig cameraSwitchPerform = new CameraSwitchPerformConfig();
                cameraSwitchPerform.blendStyle = lensCutBlendType;
                cameraSwitchPerform.custromBlendCurve = lensCutCustomBlendCurve;
                cameraSwitchPerform.blendTime = lensCutBlendTime;

                StationTemplateManager.instance.SetAbsCamera(cameraSwitchPerform, cameraWorldPosition, cameraWorldRotation, cameraFOV);
            }
            
            Cutscene cutscene = root as Cutscene;
            if (TempUI.Instance != null && cutscene != null)
            {
                if(TempUI.Instance.GetMainCamera() != cutscene.camera)
                {
                    TempUI.Instance.ReloadMainCamera(cutscene.camera);
                    //Debug.Break();
                }
            }
        }

        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime)
        {

        }

        //Called in forwards sampling when the clip exits
        protected override void OnExit() { }

        //Called in backwards sampling when the clip is entered.
        protected override void OnReverseEnter() { }

        //Called in backwards sampling when the clip exits.
        protected override void OnReverse() { }
    }
}