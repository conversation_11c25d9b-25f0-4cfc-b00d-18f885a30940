using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace CEditor
{
    [ExecuteAlways]
    public class HandheldGripper : MonoBehaviour
    {
        Animator animator;
        SimpleAnimPlayer animSystem;

        // Start is called before the first frame update
        void Start()
        {

        }

        // Update is called once per frame
        void Update()
        {

        }

        public void SetupAnimSystem(AnimationClip anim, bool bodyJustInit)
        {
            animator = this.gameObject.GetComponent<Animator>();
            if (animator == null)
                animator = this.gameObject.AddComponent<Animator>();

            animSystem = this.gameObject.GetComponent<SimpleAnimPlayer>();
            if (animSystem != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(animSystem);
#else
                Destroy(animSystem);
#endif
            }
            animSystem = this.gameObject.AddComponent<SimpleAnimPlayer>();
            animSystem.clip = anim;
            animSystem.lateOneFrame = !bodyJustInit;
            animSystem.PlayAnimSystem();
        }

        private void OnDestroy()
        {

        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

