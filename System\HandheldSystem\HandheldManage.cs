using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Text;
using System;

namespace CEditor
{
    [Serializable]
    [ExecuteAlways]
    public class HandHoldState
    {
        public bool overwriteHandAnim = false;
        public bool isHoldingObject = false;
        public bool useHoldObjectAnim = false;
        public AnimationClip overwriteAnim = null;
        public GameObject holdObject = null;
        public AnimationClip holdObjectAnimation;
        public string refBoneName = "";
        public Vector3 positionOffset = Vector3.zero;
        public Vector3 rotateOffset = Vector3.zero;
        public Vector3 localscale = Vector3.one;
    }

    [Serializable]
    [ExecuteAlways]
    public class HandheldManage : MonoBehaviour
    {
        public AnimTransMaster animMaster;

        public bool useRightHandheldUpdateControl { get; set; } = true;

        public bool useLeftHandheldUpdateControl { get; set; } = true;

        public HandHoldState rightHandheldState { get; set; } = null;

        public HandHoldState leftHandheldState { get; set; } = null;

        public bool rightHandHoldingTrigger { get; set; } = false;

        public bool leftHandHoldingTrigger { get; set; } = false;

        public bool rightHandHoldingMaintainTrigger { get; set; } = false;

        public bool leftHandHoldingMaintainTrigger { get; set; } = false;

        private GameObject rightHandHoldObject = null;

        private GameObject leftHandHoldObject = null;

        private Animator rightHandHoldObjectAnimator = null;

        private Animator leftHandHoldObjectAnimator = null;

        private SimpleAnimPlayer rightHandHoldObjectAnimPlayer = null;

        private SimpleAnimPlayer leftHandHoldObjectAnimPlayer = null;

        private Transform rightHandHanging = null;

        private Transform leftHandHanging = null;

        private bool rightHandIsHolding = false;

        private bool leftHandIsHolding = false;


        // Start is called before the first frame update
        void Start()
        {

        }

        // Update is called once per frame
        void Update()
        {
            //if (useRightHandheldUpdateControl || (useRightHandheldUpdateControl == false && rightHandIsHolding))
            //    UpdateRightHandHoldState();
            //if (useLeftHandheldUpdateControl || (useLeftHandheldUpdateControl == false && leftHandIsHolding))
            //    UpdateLeftHandHoldState();
        }

        public void UpdateSystem()
        {
            if (useRightHandheldUpdateControl || (useRightHandheldUpdateControl == false && rightHandIsHolding))
                UpdateRightHandHoldState();
            if (useLeftHandheldUpdateControl || (useLeftHandheldUpdateControl == false && leftHandIsHolding))
                UpdateLeftHandHoldState();
        }

        private void InitRightHandHoldGameobjectComponent(GameObject obj, AnimationClip anim)
        {
            HandheldGripper gripper = obj.GetComponent<HandheldGripper>();
            if (gripper == null)
                gripper = obj.AddComponent<HandheldGripper>();

            if (anim != null)
                gripper.SetupAnimSystem(anim, animMaster.CheckBodyJustInit());
        }

        private void InitLeftHandHoldGameobjectComponent(GameObject obj, AnimationClip anim)
        {
            HandheldGripper gripper = obj.GetComponent<HandheldGripper>();
            if (gripper == null)
                gripper = obj.AddComponent<HandheldGripper>();

            if (anim != null)
                gripper.SetupAnimSystem(anim, animMaster.CheckBodyJustInit());
        }

        public void SetRightHandHoldState(HandHoldState holdState)
        {
            if (holdState == null) return;
            if (animMaster == null) return;

            ClearRightHandHoldState();

            // 覆写手部动作
            animMaster.OverwriteRightHand(holdState.overwriteHandAnim, holdState.overwriteAnim);

            // 加卸载手持物
            if (holdState.isHoldingObject && holdState.holdObject != null && !string.IsNullOrEmpty(holdState.refBoneName))
            {
                Transform hangingReferBone = StationTemplateManager.FindChildInTransform(
                    this.transform,
                    holdState.refBoneName,
                    new List<string>() { StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand],
                        StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit] });

                if (hangingReferBone != null)
                {
                    GameObject hanging = new GameObject(ZString.Concat(holdState.refBoneName, "_HoldHanging"));
                    hanging.transform.parent = hangingReferBone;
                    hanging.transform.localPosition = holdState.positionOffset;
                    hanging.transform.localRotation = Quaternion.Euler(holdState.rotateOffset);
                    hanging.transform.localScale = holdState.localscale;
                    rightHandHanging = hanging.transform;

                    rightHandHoldObject = GameObject.Instantiate(holdState.holdObject, rightHandHanging.transform.position, rightHandHanging.transform.rotation, rightHandHanging);
                    rightHandHoldObject.Active(true);

                    rightHandIsHolding = true;
                }
                else
                {
                    if (rightHandHoldObject != null)
                    {
#if UNITY_EDITOR
                        DestroyImmediate(rightHandHoldObject);
#else
                        Destroy(rightHandHoldObject);
#endif
                    }
                }

                if (!holdState.useHoldObjectAnim)
                    holdState.holdObjectAnimation = null;

                if (rightHandHoldObject != null)
                    InitRightHandHoldGameobjectComponent(rightHandHoldObject, holdState.holdObjectAnimation);
            }
        }

        public void ClearRightHandHoldState()
        {
            if (animMaster != null && animMaster.CheckIsOverWriteRightHand())
                animMaster.OverwriteRightHand(false, null);

            // 清理历史手持物
            if (rightHandHoldObject != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(rightHandHoldObject);
#else
                Destroy(rightHandHoldObject);
#endif
                rightHandHoldObject = null;
            }

            // 清理历史挂点
            if (rightHandHanging != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(rightHandHanging.gameObject);
#else
                Destroy(rightHandHanging.gameObject);
#endif
                rightHandHanging = null;
            }
            rightHandIsHolding = false;
        }

        public void SetLeftHandHoldState(HandHoldState holdState)
        {
            if (holdState == null) return;
            if (animMaster == null) return;

            ClearLeftHandHoldState();

            // 覆写手部动作
            animMaster.OverwriteLeftHand(holdState.overwriteHandAnim, holdState.overwriteAnim);

            // 加卸载手持物
            if (holdState.isHoldingObject && holdState.holdObject != null && !string.IsNullOrEmpty(holdState.refBoneName))
            {
                Transform hangingReferBone = StationTemplateManager.FindChildInTransform(
                    this.transform,
                    holdState.refBoneName,
                    new List<string>() { StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeStand],
                        StationTemplateDataCollector.namingConvention[NamingConventionTypes.InitBoneTreeSit] });

                if (hangingReferBone != null)
                {
                    GameObject hanging = new GameObject(ZString.Concat(holdState.refBoneName, "_HoldHanging"));
                    hanging.transform.parent = hangingReferBone;
                    hanging.transform.localPosition = holdState.positionOffset;
                    hanging.transform.localRotation = Quaternion.Euler(holdState.rotateOffset);
                    hanging.transform.localScale = holdState.localscale;
                    leftHandHanging = hanging.transform;

                    leftHandHoldObject = GameObject.Instantiate(holdState.holdObject, leftHandHanging.transform.position, leftHandHanging.transform.rotation, leftHandHanging);
                    leftHandHoldObject.Active(true);

                    leftHandIsHolding = true;
                }
                else
                {
                    if (rightHandHoldObject != null)
                    {
#if UNITY_EDITOR
                        DestroyImmediate(rightHandHoldObject);
#else
                        Destroy(rightHandHoldObject);
#endif
                    }
                }

                if (!holdState.useHoldObjectAnim)
                    holdState.holdObjectAnimation = null;

                if (leftHandHoldObject != null)
                    InitLeftHandHoldGameobjectComponent(leftHandHoldObject, holdState.holdObjectAnimation);
            }
        }

        public void ClearLeftHandHoldState()
        {
            if (animMaster != null && animMaster.CheckIsOverWriteLeftHand())
                animMaster.OverwriteLeftHand(false, null);

            // 清理历史手持物
            if (leftHandHoldObject != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(leftHandHoldObject);
#else
                Destroy(leftHandHoldObject);
#endif
                leftHandHoldObject = null;
            }

            // 清理历史挂点
            if (leftHandHanging != null)
            {
#if UNITY_EDITOR
                DestroyImmediate(leftHandHanging.gameObject);
#else
                Destroy(leftHandHanging.gameObject);
#endif
                leftHandHanging = null;
            }
            leftHandIsHolding = false;
        }

        private void UpdateRightHandHoldState()
        {
            if (rightHandHoldingTrigger)
                rightHandHoldingMaintainTrigger = false;
            if (rightHandHoldingTrigger || rightHandHoldingMaintainTrigger)
            {
                //if (/*NewID == OldID*/ true)
                //{
                    if (!rightHandIsHolding)
                    {
                        if (rightHandheldState != null)
                        {
                            SetRightHandHoldState(rightHandheldState);
                        }
                    }
                //}
                //else
                //{
                //    ClearRightHandHoldState();
                //}
                rightHandHoldingTrigger = false;
            }
            else
            {
                ClearRightHandHoldState();
                useRightHandheldUpdateControl = false;
            }
        }

        private void UpdateLeftHandHoldState()
        {
            //Debug.Log(ZString.Concat("UpdateLeftHandHoldState ", leftHandHoldingTrigger, ' ', leftHandHoldingMaintainTrigger));
            if (leftHandHoldingTrigger)
                leftHandHoldingMaintainTrigger = false;
            if (leftHandHoldingTrigger || leftHandHoldingMaintainTrigger)
            {
                //if (/*NewID == OldID*/ true)
                //{
                if (!leftHandIsHolding)
                {
                    if (leftHandheldState != null)
                    {
                        SetLeftHandHoldState(leftHandheldState);
                    }
                }
                //}
                //else
                //{
                //    ClearLeftHandHoldState();
                //}
                leftHandHoldingTrigger = false;
            }
            else
            {
                ClearLeftHandHoldState();
                useLeftHandheldUpdateControl = false;
            }
        }

        public void ForceUpdateRightHnadheldState()
        {
            if (useRightHandheldUpdateControl || (useRightHandheldUpdateControl == false && rightHandIsHolding))
                UpdateRightHandHoldState();
        }

        public void ForceUpdateLeftHnadheldState()
        {
            if (useLeftHandheldUpdateControl || (useLeftHandheldUpdateControl == false && leftHandIsHolding))
                UpdateLeftHandHoldState();
        }

        private void OnDestroy()
        {
            ClearRightHandHoldState();
            ClearLeftHandHoldState();
        }

        void OnDrawGizmos()
        {
            // Your gizmo drawing thing goes here if required...

#if UNITY_EDITOR
            // Ensure continuous Update calls.
            if (!Application.isPlaying)
            {
                UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
                UnityEditor.SceneView.RepaintAll();
            }
#endif
        }
    }
}

