using System.Collections.Generic;
using CEditor;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Slate.ActionClips
{
    [System.Serializable]
    public class CClothClipParams
    {
        [SerializeField, LabelText("角色名")]
        public string characterName = "";

        [SerializeField, LabelText("进入时启用全部布料")]
        public bool enterEnableCloth = true;

        [SerializeField, LabelText("进入时全部布料权重"), Toolt<PERSON>("小于0时不覆盖设置"), Range(-1.0f, 1.0f)]
        public float enterBlendWeight = -1.0f;
        
        [SerializeField, LabelText("进入时单个布料配置")]
        public List<ClothClipSerializeData> enterClothConfigs = new();
        
        [SerializeField, LabelText("离开时全部布料权重"), Tooltip("小于0时不覆盖设置"), Range(-1.0f, 1.0f)]
        public float exitBlendWeight = -1.0f;

        [SerializeField, LabelText("离开时启用全部布料")]
        public bool exitEnableCloth = true;

        [SerializeField, LabelText("离开时单个布料配置")]
        public List<ClothClipSerializeData> exitClothConfigs = new();
    }
    [Name("布料配置")]
    [Attachable(typeof(CClothControlTrack))]
    [Category("C类编辑器")]
    public class CClothControlClip : BaseDialogueActorActionClip<CClothClipParams>
    {
        private GameObject performer;
        private ClothManager clothMaster;

        [SerializeField, HideInInspector]
        private float _length = 1;
        private CClothClipParams _tmpEnterRestoredParams = new();

        public override float length
        {
            get { return _length; }
            set { _length = value; }
        }

        public override string info
        {
            get{ return "布料配置"; }
        }

        protected override void OnCreate()
        {
            base.OnCreate();

            var actor = GetActor();
            if (actor)
            {
                GetCfg().characterName = actor.name;
            }
        }

        //Called in forward sampling when the clip is entered
        protected override void OnEnter()
        {
            _tmpEnterRestoredParams = CollectCurrentClothParams();
            ExeChange(GetCfg().characterName, GetCfg().enterClothConfigs, GetCfg().enterEnableCloth, GetCfg().enterBlendWeight, false);
        }
        
        protected override void OnReverse()
        {
            ExeChange(GetCfg().characterName, _tmpEnterRestoredParams.enterClothConfigs, _tmpEnterRestoredParams.enterEnableCloth, _tmpEnterRestoredParams.enterBlendWeight, true);
        }

        //Called in forwards sampling when the clip exits
        protected override void OnExit()
        {
            ExeChange(GetCfg().characterName, GetCfg().exitClothConfigs, GetCfg().exitEnableCloth, GetCfg().exitBlendWeight, false);
        }
        
        //Called in backwards sampling when the clip is entered.
        protected override void OnReverseEnter()
        {
            ExeChange(GetCfg().characterName, GetCfg().enterClothConfigs, GetCfg().enterEnableCloth, GetCfg().enterBlendWeight, false);
        }

        private void ExeChange(string charName, List<ClothClipSerializeData> inClothData, bool enable, float blendWeight, bool restore)
        {
            if (performer == null)
                performer = GetActor();
            if (clothMaster == null)
                clothMaster = performer.GetComponent<ClothManager>();
            if (clothMaster == null)
            {
                clothMaster = performer.AddComponent<ClothManager>();
                clothMaster.Init();
            }

            if (clothMaster != null)
                clothMaster.SetClothManagerState(charName, inClothData, enable, blendWeight, restore);
        }

        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime)
        {

        }

        private CClothClipParams CollectCurrentClothParams()
        {
            var actor = GetActor();
            if (actor == null)
            {
                return new CClothClipParams()
                {
                    characterName = "",
                    enterEnableCloth = false,
                    enterClothConfigs = new List<ClothClipSerializeData>(),
                    enterBlendWeight = -1.0f,
                    exitEnableCloth = false,
                    exitClothConfigs = new List<ClothClipSerializeData>(),
                    exitBlendWeight = -1.0f
                };
            }

            var clothManager = actor.GetComponent<ClothManager>();
            if (clothManager == null)
            {
                return new CClothClipParams()
                {
                    characterName = actor.name,
                    enterEnableCloth = false,
                    enterClothConfigs = new List<ClothClipSerializeData>(),
                    enterBlendWeight = -1.0f,
                    exitEnableCloth = false,
                    exitClothConfigs = new List<ClothClipSerializeData>(),
                    exitBlendWeight = -1.0f
                };
            }

            var currentConfigs = clothManager.GetCurrentClothConfigs();

            return new CClothClipParams()
            {
                characterName = actor.name,
                enterEnableCloth = clothManager.IsEnable(),
                enterClothConfigs = currentConfigs,
                enterBlendWeight = clothManager.GetBlendWeight(),
                exitEnableCloth = clothManager.IsEnable(),
                exitClothConfigs = currentConfigs,
                exitBlendWeight = clothManager.GetBlendWeight()
            };
        }

        //Called in backwards sampling when the clip exits.
    }
}