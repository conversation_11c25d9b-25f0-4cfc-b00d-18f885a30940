﻿#if UNITY_EDITOR
using System.IO;
using UnityEngine;
using System.Linq;

public class CImageBrowserWindowUtility
{
    public static bool IsImageFile(string path)
    {
        string ext = Path.GetExtension(path).ToLower();
        return new[] { ".png", ".jpg", ".jpeg" }.Contains(ext);
    }

    public static Texture2D LoadTexture(string path)
    {
        if (!File.Exists(path))
        {
            return null;
        }

        byte[] fileData = File.ReadAllBytes(path);
        Texture2D tex = new Texture2D(2, 2);
        return tex.LoadImage(fileData) ? tex : null;
    }
    
    // 添加深拷贝方法
    public static Texture2D CopyTexture(Texture2D source)
    {
        if (source == null)
        {
            return null;
        }
        
        var rt = RenderTexture.GetTemporary(source.width, source.height);
        Graphics.Blit(source, rt);
        var tempTex = new Texture2D(source.width, source.height, source.format, false);
        tempTex.ReadPixels(new Rect(0, 0, source.width, source.height), 0, 0);
        tempTex.Apply();
        RenderTexture.ReleaseTemporary(rt);
        return tempTex;
    }

    public static Texture2D GenerateThumbnail(Texture2D original)
    {
        if (original == null)
        {
            return null;
        }
        
        int maxSize = 128;
        int width, height;
        if (original.width > original.height)
        {
            width = maxSize;
            height = Mathf.RoundToInt((float)original.height / original.width * maxSize);
        }
        else
        {
            height = maxSize;
            width = Mathf.RoundToInt((float)original.width / original.height * maxSize);
        }

        RenderTexture rt = RenderTexture.GetTemporary(width, height);
        Graphics.Blit(original, rt);
        Texture2D thumbnail = new Texture2D(width, height);
        thumbnail.ReadPixels(new Rect(0, 0, width, height), 0, 0);
        thumbnail.Apply();
        RenderTexture.ReleaseTemporary(rt);
        
        return thumbnail;
    }

    public static Texture2D GenerateTextureByInfo(SCameraInfoParams inInfo)
    {
        Texture2D tex = LoadTexture(inInfo.Path);
        if (tex)
        {
            Texture2D thumbnail = GenerateThumbnail(tex);
            return thumbnail;
        }
        return null;
    }
}
#endif