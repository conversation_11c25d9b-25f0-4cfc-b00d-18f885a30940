using System.Collections;
using CEditor;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using ET;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;
using VGame;
using VGame.Framework;

namespace Slate.ActionClips
{
    [System.Serializable]
    public class CProgrammaticMouthClipParams
    {
        //[SerializeField, LabelText("嘴部资产"), ValueDropdown("AnimationConfig"), OnValueChanged("OnAnimationChange")]
        //public int animClipKey;

        //private IEnumerable AnimationConfig()
        //{
        //    return CfgManager.tables.TbCEditorMouthAssets.DataList.Where(x => x.IsReady && ((x.ID - 100) < 100) && ((x.ID - 100) > 0)).Select(x =>
        //    {
        //        return new ValueDropdownItem($"{x.ID}-{x.Detail} [{x.FileName}]", x.ID);
        //    });
        //}

        //private AnimationClip _animationClip;
        //public async ETTask<AnimationClip> GetClip()
        //{
        //    if (_animationClip != null) return _animationClip;
        //    var cfg = CfgManager.tables.TbCEditorMouthAssets.GetOrDefault(animClipKey);
        //    if (cfg != null && cfg.IsReady && !string.IsNullOrEmpty(cfg.FileName))
        //    {
        //        _animationClip = await DialogueUtil.Load<AnimationClip>(cfg.FileName);
        //        return _animationClip;
        //    }

        //    return null;
        //}
        //public void OnAnimationChange()
        //{
        //    _animationClip = null;
        //}
        ////[SerializeField, LabelText("嘴部资产")]
        ////public AnimationClip mouthClip;

        //[SerializeField, LabelText("嘴部动作强度")]
        //public float mouthWeight = 1;
        
        //[SerializeField, LabelText("嘴部动作播放逻辑")]
        //public PlayingMode mouthPlayingMode = PlayingMode.EndStop;
        
        //[SerializeField, LabelText("嘴部起始时间")]
        //public float mouthStartTime = 0;
        
        //[SerializeField, LabelText("嘴部停止时间")]
        //public float mouthEndTime = 3;
        
        //[SerializeField, LabelText("嘴部过渡曲线")]
        //public BlendType mouthBlendType = BlendType.EaseInOut;
        
        //[SerializeField, LabelText("嘴部过渡时长")]
        //public float mouthBlendInDuration = 0.3f;
        
        //[SerializeField, LabelText("嘴部静态姿态混入类型")]
        //public BlendType mouthStaticBlendType = BlendType.EaseInOut;
        
        //[SerializeField, LabelText("嘴部静态姿态混入时长")]
        //public float mouthStaticBlendInDuration = 0.3f;
    }

    [Name("程序化嘴部资产配置")]
    [Attachable(typeof(CProgrammaticMouthTrack))]
    [Category("C类编辑器")]
    public class CProgrammaticMouthClip : BaseDialogueActorActionClip<CProgrammaticMouthClipParams>
    {
        private GameObject performer;

        public override string info
        {
            get
            {
                return "程序化嘴部资产配置";
            }
        }

        //Called in forward sampling when the clip is entered
        //protected override void OnEnter()
        protected override async ETTask OnEnterAsync()
        {
            performer = GetActor();
            AnimTransMaster master = performer.GetComponent<AnimTransMaster>();

            //if (master != null && performer != null)
            //{
            //    master.currentFaceMouthAnimClip = await GetCfg().GetClip();
            //    master.currentFaceMouthWeight = GetCfg().mouthWeight;
            //    master.mouthPlayingMode = GetCfg().mouthPlayingMode;
            //    master.mouthStartTime = GetCfg().mouthStartTime;
            //    master.mouthEndTime = GetCfg().mouthEndTime;
            //    master.faceMouthBlendType = GetCfg().mouthBlendType;
            //    master.faceMouthBlendInDuration = GetCfg().mouthBlendInDuration;
            //    master.faceMouthStaticBlendType = GetCfg().mouthStaticBlendType;
            //    master.faceMouthStaticBlendInDuration = GetCfg().mouthStaticBlendInDuration;

            //    master.MouthTransAnim();
            //}
        }

        //Called per frame while the clip is updating. Time is the local time within the clip.
        //So a time of 0 means the start of the clip.
        protected override void OnUpdate(float time, float previousTime) { }

        //Called in forwards sampling when the clip exits
        protected override void OnExit() { }

        //Called in backwards sampling when the clip is entered.
        protected override void OnReverseEnter() { }

        //Called in backwards sampling when the clip exits.
        protected override void OnReverse() { }
    }
}